#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة مميزات ثلاثية الأبعاد والتفاصيل عالية الدقة
"""

import re

def add_3d_features():
    print("🏗️ إضافة مميزات ثلاثية الأبعاد والتفاصيل عالية الدقة...")
    
    # قراءة الملف الحالي
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إضافة مكتبات ثلاثية الأبعاد
    libraries_3d = '''
    <!-- مكتبات ثلاثية الأبعاد -->
    <script src="https://unpkg.com/mapbox-gl@2.15.0/dist/mapbox-gl.js"></script>
    <link href="https://unpkg.com/mapbox-gl@2.15.0/dist/mapbox-gl.css" rel="stylesheet" />
    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.155.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/deck.gl@8.9.0/dist.min.js"></script>'''
    
    # إضافة المكتبات قبل إغلاق head
    content = content.replace('</head>', libraries_3d + '\n</head>')
    
    # إضافة CSS للمميزات ثلاثية الأبعاد
    css_3d = '''
        /* مميزات ثلاثية الأبعاد */
        .map-3d-controls {
            position: fixed;
            top: 80px;
            left: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-label {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .control-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .control-button.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .control-slider {
            width: 100%;
            margin: 5px 0;
        }
        
        .layer-toggle {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .layer-toggle input[type="checkbox"] {
            margin-left: 8px;
        }
        
        .view-mode-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-top: 10px;
        }
        
        /* تحسينات للعرض ثلاثي الأبعاد */
        .leaflet-container {
            background: #f8f9fa !important;
        }
        
        .building-3d {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .building-3d:hover {
            filter: brightness(1.2);
            transform: scale(1.05);
        }
        
        .street-3d {
            stroke-width: 3;
            stroke: #2c3e50;
            fill: none;
            transition: all 0.3s ease;
        }
        
        .street-3d:hover {
            stroke-width: 5;
            stroke: #3498db;
        }
        
        /* مؤشر الارتفاع */
        .elevation-indicator {
            position: fixed;
            bottom: 100px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1001;
        }
        
        /* شريط طبقات الخريطة */
        .map-layers-panel {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1001;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border: 1px solid rgba(0,0,0,0.1);
            max-width: 250px;
        }
        
        .layer-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .layer-item:last-child {
            border-bottom: none;
        }
        
        .layer-opacity {
            width: 60px;
            margin-right: 10px;
        }
    '''
    
    # إضافة CSS قبل إغلاق style
    content = content.replace('</style>', css_3d + '\n</style>')
    
    # إضافة عناصر التحكم ثلاثية الأبعاد
    controls_3d = '''
    <!-- عناصر التحكم ثلاثية الأبعاد -->
    <div class="map-3d-controls" id="map3dControls">
        <div class="control-group">
            <label class="control-label">🏗️ العرض ثلاثي الأبعاد</label>
            <div class="view-mode-selector">
                <button class="control-button active" onclick="setViewMode('2d')">2D</button>
                <button class="control-button" onclick="setViewMode('3d')">3D</button>
            </div>
        </div>
        
        <div class="control-group">
            <label class="control-label">🏢 المباني</label>
            <div class="layer-toggle">
                <label>عرض المباني</label>
                <input type="checkbox" id="buildingsToggle" onchange="toggleBuildings(this.checked)" checked>
            </div>
            <input type="range" class="control-slider" id="buildingHeight" min="1" max="10" value="5" 
                   onchange="setBuildingHeight(this.value)">
            <small>ارتفاع المباني: <span id="heightValue">5</span>x</small>
        </div>
        
        <div class="control-group">
            <label class="control-label">🛣️ الشوارع</label>
            <div class="layer-toggle">
                <label>عرض الشوارع</label>
                <input type="checkbox" id="streetsToggle" onchange="toggleStreets(this.checked)" checked>
            </div>
            <div class="layer-toggle">
                <label>أسماء الشوارع</label>
                <input type="checkbox" id="streetNamesToggle" onchange="toggleStreetNames(this.checked)">
            </div>
        </div>
        
        <div class="control-group">
            <label class="control-label">🌍 طبقات الخريطة</label>
            <button class="control-button" onclick="setMapLayer('satellite')">أقمار صناعية</button>
            <button class="control-button" onclick="setMapLayer('terrain')">تضاريس</button>
            <button class="control-button" onclick="setMapLayer('streets')">شوارع</button>
        </div>
        
        <div class="control-group">
            <label class="control-label">📐 زاوية الرؤية</label>
            <input type="range" class="control-slider" id="tiltSlider" min="0" max="60" value="0" 
                   onchange="setMapTilt(this.value)">
            <small>الميل: <span id="tiltValue">0</span>°</small>
        </div>
        
        <div class="control-group">
            <label class="control-label">🔄 الدوران</label>
            <input type="range" class="control-slider" id="rotationSlider" min="0" max="360" value="0" 
                   onchange="setMapRotation(this.value)">
            <small>الدوران: <span id="rotationValue">0</span>°</small>
        </div>
    </div>
    
    <!-- لوحة طبقات الخريطة -->
    <div class="map-layers-panel" id="mapLayersPanel">
        <h4 style="margin: 0 0 15px 0; color: #333;">طبقات الخريطة</h4>
        
        <div class="layer-item">
            <label>الأماكن</label>
            <div>
                <input type="range" class="layer-opacity" min="0" max="100" value="100" 
                       onchange="setLayerOpacity('places', this.value)">
                <input type="checkbox" checked onchange="toggleLayer('places', this.checked)">
            </div>
        </div>
        
        <div class="layer-item">
            <label>المباني</label>
            <div>
                <input type="range" class="layer-opacity" min="0" max="100" value="80" 
                       onchange="setLayerOpacity('buildings', this.value)">
                <input type="checkbox" checked onchange="toggleLayer('buildings', this.checked)">
            </div>
        </div>
        
        <div class="layer-item">
            <label>الشوارع</label>
            <div>
                <input type="range" class="layer-opacity" min="0" max="100" value="90" 
                       onchange="setLayerOpacity('streets', this.value)">
                <input type="checkbox" checked onchange="toggleLayer('streets', this.checked)">
            </div>
        </div>
        
        <div class="layer-item">
            <label>التضاريس</label>
            <div>
                <input type="range" class="layer-opacity" min="0" max="100" value="70" 
                       onchange="setLayerOpacity('terrain', this.value)">
                <input type="checkbox" onchange="toggleLayer('terrain', this.checked)">
            </div>
        </div>
    </div>
    
    <!-- مؤشر الارتفاع -->
    <div class="elevation-indicator" id="elevationIndicator">
        الارتفاع: <span id="currentElevation">0</span> متر
    </div>'''
    
    # إضافة عناصر التحكم بعد زر الموقع
    content = content.replace(
        '<button class="location-btn"',
        controls_3d + '\n    <button class="location-btn"'
    )
    
    # إضافة JavaScript للمميزات ثلاثية الأبعاد
    js_3d = '''
        // ==================== مميزات ثلاثية الأبعاد ====================
        
        let currentViewMode = '2d';
        let buildingsLayer = null;
        let streetsLayer = null;
        let terrainLayer = null;
        let mapboxMap = null;
        
        // تهيئة المميزات ثلاثية الأبعاد
        function init3DFeatures() {
            console.log('🏗️ تهيئة المميزات ثلاثية الأبعاد...');
            
            // إضافة طبقات ثلاثية الأبعاد
            addBuildingsLayer();
            addStreetsLayer();
            addTerrainLayer();
            
            // إضافة مستمعي الأحداث
            map.on('mousemove', updateElevationIndicator);
            map.on('zoomend', updateLayersVisibility);
        }
        
        // تغيير وضع العرض
        function setViewMode(mode) {
            currentViewMode = mode;
            
            // تحديث أزرار الوضع
            document.querySelectorAll('.view-mode-selector .control-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            if (mode === '3d') {
                enable3DView();
            } else {
                disable3DView();
            }
            
            console.log(`🔄 تم تغيير وضع العرض إلى: ${mode}`);
        }
        
        // تفعيل العرض ثلاثي الأبعاد
        function enable3DView() {
            // إضافة تأثيرات ثلاثية الأبعاد للعلامات
            if (markersCluster) {
                markersCluster.eachLayer(function(marker) {
                    const element = marker.getElement();
                    if (element) {
                        element.style.transform += ' perspective(1000px) rotateX(15deg)';
                        element.style.filter = 'drop-shadow(0 10px 20px rgba(0,0,0,0.3))';
                    }
                });
            }
            
            // تفعيل طبقة المباني
            if (buildingsLayer) {
                buildingsLayer.addTo(map);
            }
            
            showNotification('تم تفعيل العرض ثلاثي الأبعاد', 'success');
        }
        
        // إلغاء العرض ثلاثي الأبعاد
        function disable3DView() {
            // إزالة تأثيرات ثلاثية الأبعاد
            if (markersCluster) {
                markersCluster.eachLayer(function(marker) {
                    const element = marker.getElement();
                    if (element) {
                        element.style.transform = element.style.transform.replace(' perspective(1000px) rotateX(15deg)', '');
                        element.style.filter = 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))';
                    }
                });
            }
            
            // إزالة طبقة المباني
            if (buildingsLayer && map.hasLayer(buildingsLayer)) {
                map.removeLayer(buildingsLayer);
            }
            
            showNotification('تم إلغاء العرض ثلاثي الأبعاد', 'info');
        }
        
        // إضافة طبقة المباني
        function addBuildingsLayer() {
            // محاكاة بيانات المباني
            const buildingsData = generateBuildingsData();
            
            buildingsLayer = L.layerGroup();
            
            buildingsData.forEach(building => {
                const buildingPolygon = L.polygon(building.coordinates, {
                    color: '#2c3e50',
                    fillColor: getBuildingColor(building.type),
                    fillOpacity: 0.7,
                    weight: 2,
                    className: 'building-3d'
                });
                
                buildingPolygon.bindPopup(`
                    <div style="text-align: center;">
                        <h4>${building.name}</h4>
                        <p>النوع: ${building.type}</p>
                        <p>الارتفاع: ${building.height} متر</p>
                        <p>الطوابق: ${building.floors}</p>
                    </div>
                `);
                
                buildingsLayer.addLayer(buildingPolygon);
            });
        }
        
        // إضافة طبقة الشوارع
        function addStreetsLayer() {
            // محاكاة بيانات الشوارع
            const streetsData = generateStreetsData();
            
            streetsLayer = L.layerGroup();
            
            streetsData.forEach(street => {
                const streetLine = L.polyline(street.coordinates, {
                    color: getStreetColor(street.type),
                    weight: getStreetWidth(street.type),
                    opacity: 0.8,
                    className: 'street-3d'
                });
                
                streetLine.bindPopup(`
                    <div style="text-align: center;">
                        <h4>${street.name}</h4>
                        <p>النوع: ${street.type}</p>
                        <p>الطول: ${street.length} كم</p>
                    </div>
                `);
                
                streetsLayer.addLayer(streetLine);
            });
            
            streetsLayer.addTo(map);
        }
        
        // إضافة طبقة التضاريس
        function addTerrainLayer() {
            terrainLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri',
                opacity: 0.7
            });
        }
        
        // تبديل عرض المباني
        function toggleBuildings(show) {
            if (show && buildingsLayer) {
                buildingsLayer.addTo(map);
            } else if (buildingsLayer && map.hasLayer(buildingsLayer)) {
                map.removeLayer(buildingsLayer);
            }
        }
        
        // تبديل عرض الشوارع
        function toggleStreets(show) {
            if (show && streetsLayer) {
                streetsLayer.addTo(map);
            } else if (streetsLayer && map.hasLayer(streetsLayer)) {
                map.removeLayer(streetsLayer);
            }
        }
        
        // تبديل أسماء الشوارع
        function toggleStreetNames(show) {
            // تنفيذ عرض أسماء الشوارع
            console.log('تبديل أسماء الشوارع:', show);
        }
        
        // تعيين ارتفاع المباني
        function setBuildingHeight(height) {
            document.getElementById('heightValue').textContent = height;
            
            if (buildingsLayer) {
                buildingsLayer.eachLayer(function(layer) {
                    const element = layer.getElement();
                    if (element) {
                        element.style.transform = `scale(1, ${height / 5})`;
                    }
                });
            }
        }
        
        // تعيين طبقة الخريطة
        function setMapLayer(layerType) {
            // إزالة الطبقات الحالية
            map.eachLayer(function(layer) {
                if (layer._url && layer._url.includes('tile')) {
                    map.removeLayer(layer);
                }
            });
            
            let newLayer;
            switch (layerType) {
                case 'satellite':
                    newLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '© Esri'
                    });
                    break;
                case 'terrain':
                    newLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '© Esri'
                    });
                    break;
                default:
                    newLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors'
                    });
            }
            
            newLayer.addTo(map);
            showNotification(`تم تغيير طبقة الخريطة إلى: ${layerType}`, 'success');
        }
        
        // تعيين ميل الخريطة
        function setMapTilt(tilt) {
            document.getElementById('tiltValue').textContent = tilt;
            
            // تطبيق الميل على الخريطة
            const mapContainer = document.getElementById('map');
            mapContainer.style.transform = `perspective(1000px) rotateX(${tilt}deg)`;
        }
        
        // تعيين دوران الخريطة
        function setMapRotation(rotation) {
            document.getElementById('rotationValue').textContent = rotation;
            
            // تطبيق الدوران على الخريطة
            const mapContainer = document.getElementById('map');
            const currentTransform = mapContainer.style.transform || '';
            const newTransform = currentTransform.replace(/rotateZ\\([^)]*\\)/, '') + ` rotateZ(${rotation}deg)`;
            mapContainer.style.transform = newTransform;
        }
        
        // تعيين شفافية الطبقة
        function setLayerOpacity(layerName, opacity) {
            const opacityValue = opacity / 100;
            
            switch (layerName) {
                case 'buildings':
                    if (buildingsLayer) {
                        buildingsLayer.setStyle({ fillOpacity: opacityValue });
                    }
                    break;
                case 'streets':
                    if (streetsLayer) {
                        streetsLayer.setStyle({ opacity: opacityValue });
                    }
                    break;
                // إضافة طبقات أخرى حسب الحاجة
            }
        }
        
        // تبديل طبقة
        function toggleLayer(layerName, show) {
            switch (layerName) {
                case 'buildings':
                    toggleBuildings(show);
                    break;
                case 'streets':
                    toggleStreets(show);
                    break;
                case 'terrain':
                    if (show && terrainLayer) {
                        terrainLayer.addTo(map);
                    } else if (terrainLayer && map.hasLayer(terrainLayer)) {
                        map.removeLayer(terrainLayer);
                    }
                    break;
            }
        }
        
        // تحديث مؤشر الارتفاع
        function updateElevationIndicator(e) {
            // محاكاة بيانات الارتفاع
            const elevation = Math.floor(Math.random() * 2000) + 100;
            document.getElementById('currentElevation').textContent = elevation;
        }
        
        // تحديث رؤية الطبقات حسب مستوى التكبير
        function updateLayersVisibility() {
            const zoom = map.getZoom();
            
            // إظهار المباني عند التكبير العالي
            if (zoom >= 15 && buildingsLayer && !map.hasLayer(buildingsLayer)) {
                buildingsLayer.addTo(map);
            } else if (zoom < 15 && buildingsLayer && map.hasLayer(buildingsLayer)) {
                map.removeLayer(buildingsLayer);
            }
        }
        
        // توليد بيانات المباني (محاكاة)
        function generateBuildingsData() {
            const buildings = [];
            const center = [15.3694, 44.1910]; // صنعاء
            
            for (let i = 0; i < 50; i++) {
                const lat = center[0] + (Math.random() - 0.5) * 0.01;
                const lng = center[1] + (Math.random() - 0.5) * 0.01;
                
                buildings.push({
                    name: `مبنى ${i + 1}`,
                    type: ['سكني', 'تجاري', 'إداري', 'تعليمي'][Math.floor(Math.random() * 4)],
                    height: Math.floor(Math.random() * 100) + 10,
                    floors: Math.floor(Math.random() * 20) + 1,
                    coordinates: [
                        [lat, lng],
                        [lat + 0.0001, lng],
                        [lat + 0.0001, lng + 0.0001],
                        [lat, lng + 0.0001]
                    ]
                });
            }
            
            return buildings;
        }
        
        // توليد بيانات الشوارع (محاكاة)
        function generateStreetsData() {
            const streets = [];
            const center = [15.3694, 44.1910];
            
            for (let i = 0; i < 20; i++) {
                const startLat = center[0] + (Math.random() - 0.5) * 0.02;
                const startLng = center[1] + (Math.random() - 0.5) * 0.02;
                const endLat = startLat + (Math.random() - 0.5) * 0.01;
                const endLng = startLng + (Math.random() - 0.5) * 0.01;
                
                streets.push({
                    name: `شارع ${i + 1}`,
                    type: ['رئيسي', 'فرعي', 'محلي'][Math.floor(Math.random() * 3)],
                    length: (Math.random() * 5 + 0.5).toFixed(1),
                    coordinates: [
                        [startLat, startLng],
                        [endLat, endLng]
                    ]
                });
            }
            
            return streets;
        }
        
        // الحصول على لون المبنى حسب النوع
        function getBuildingColor(type) {
            const colors = {
                'سكني': '#3498db',
                'تجاري': '#e74c3c',
                'إداري': '#f39c12',
                'تعليمي': '#2ecc71'
            };
            return colors[type] || '#95a5a6';
        }
        
        // الحصول على لون الشارع حسب النوع
        function getStreetColor(type) {
            const colors = {
                'رئيسي': '#2c3e50',
                'فرعي': '#34495e',
                'محلي': '#7f8c8d'
            };
            return colors[type] || '#bdc3c7';
        }
        
        // الحصول على عرض الشارع حسب النوع
        function getStreetWidth(type) {
            const widths = {
                'رئيسي': 6,
                'فرعي': 4,
                'محلي': 2
            };
            return widths[type] || 3;
        }
        
        // تهيئة المميزات ثلاثية الأبعاد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                init3DFeatures();
                console.log('✅ تم تهيئة المميزات ثلاثية الأبعاد بنجاح');
            }, 2000);
        });
    '''
    
    # إضافة JavaScript قبل إغلاق script
    content = content.replace('</script>', js_3d + '\n</script>')
    
    # حفظ الملف المحدث
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إضافة المميزات ثلاثية الأبعاد بنجاح!")
    print("🏗️ المميزات المضافة:")
    print("   - عرض ثلاثي الأبعاد للمباني")
    print("   - شوارع تفاعلية مع تفاصيل")
    print("   - طبقات خرائط متعددة (أقمار، تضاريس)")
    print("   - تحكم في زاوية الرؤية والدوران")
    print("   - مؤشر الارتفاع")
    print("   - لوحة تحكم شاملة")

if __name__ == "__main__":
    add_3d_features()
