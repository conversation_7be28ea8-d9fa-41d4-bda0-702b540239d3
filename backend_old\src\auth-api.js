// يمن ناف - واجهة برمجية للتحقق من المصادقة والصلاحيات
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const userService = require('./postgres-users');
const permissionsManager = require('./postgres-permissions');
const { authenticateToken } = require('./auth-middleware');

// الإعدادات
const JWT_SECRET = process.env.JWT_SECRET || 'yemen-nav-secret-key';
const JWT_EXPIRES_IN = '24h';

// تسجيل الدخول
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                message: 'يجب توفير اسم المستخدم وكلمة المرور',
                error: 'MISSING_CREDENTIALS'
            });
        }

        // الحصول على المستخدم من قاعدة البيانات
        const user = await userService.getUserByUsername(username);

        if (!user) {
            return res.status(401).json({
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
                error: 'INVALID_CREDENTIALS'
            });
        }

        // التحقق من كلمة المرور
        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
            return res.status(401).json({
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
                error: 'INVALID_CREDENTIALS'
            });
        }

        // التحقق من أن المستخدم نشط
        if (!user.is_active) {
            return res.status(403).json({
                message: 'تم تعطيل حسابك. يرجى الاتصال بالمسؤول',
                error: 'ACCOUNT_DISABLED'
            });
        }

        // الحصول على صلاحيات المستخدم
        const permissions = await userService.getUserPermissions(user.user_id);

        // التحقق من إمكانية وصول المستخدم إلى صفحة الإدارة
        const canAccessAdmin = await userService.canAccessAdmin(user.user_id);

        // إنشاء رمز JWT
        const token = jwt.sign(
            {
                id: user.user_id,
                username: user.username,
                role: user.role_id,
                permissions: Array.isArray(permissions) ? permissions.map(p => p.code) : [],
                canAccessAdmin: canAccessAdmin
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        // تحديث آخر تسجيل دخول
        await userService.updateLastLogin(user.user_id);

        // إزالة كلمة المرور من الكائن المُرجع
        delete user.password;

        res.json({
            message: 'تم تسجيل الدخول بنجاح',
            token,
            user: {
                id: user.user_id,
                username: user.username,
                email: user.email,
                fullName: user.full_name,
                role: user.role_id,
                roleName: user.role_name,
                permissions: Array.isArray(permissions) ? permissions.map(p => p.code) : [],
                canAccessAdmin: canAccessAdmin
            }
        });
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        res.status(500).json({
            message: 'حدث خطأ أثناء تسجيل الدخول',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

// التحقق من صحة الرمز
router.get('/verify-token', authenticateToken, (req, res) => {
    res.json({
        message: 'الرمز صالح',
        user: req.user
    });
});

// الحصول على معلومات المستخدم الحالي
router.get('/me', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;

        // الحصول على المستخدم من قاعدة البيانات
        const user = await userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                message: 'لم يتم العثور على المستخدم',
                error: 'USER_NOT_FOUND'
            });
        }

        // الحصول على صلاحيات المستخدم
        const permissions = await userService.getUserPermissions(userId);

        // التحقق من إمكانية وصول المستخدم إلى صفحة الإدارة
        const canAccessAdmin = await userService.canAccessAdmin(userId);

        // إزالة كلمة المرور من الكائن المُرجع
        delete user.password;

        res.json({
            user: {
                id: user.user_id,
                username: user.username,
                email: user.email,
                fullName: user.full_name,
                role: user.role_id,
                roleName: user.role_name,
                permissions: Array.isArray(permissions) ? permissions.map(p => p.code) : [],
                canAccessAdmin: canAccessAdmin
            }
        });
    } catch (error) {
        console.error('خطأ في الحصول على معلومات المستخدم:', error);
        res.status(500).json({
            message: 'حدث خطأ أثناء الحصول على معلومات المستخدم',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

// تسجيل الخروج (لا يتطلب أي إجراء على الخادم، يتم التعامل معه على العميل)
router.post('/logout', (req, res) => {
    res.json({ message: 'تم تسجيل الخروج بنجاح' });
});

module.exports = router;
