// يمن ناف - ملف وسيط للتحقق من المصادقة والصلاحيات
const jwt = require('jsonwebtoken');
const permissionsManager = require('./postgres-permissions');

// الإعدادات
const JWT_SECRET = process.env.JWT_SECRET || 'yemen-nav-secret-key';

// وسيط للتحقق من رمز المصادقة
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({
            message: 'يجب توفير رمز المصادقة',
            error: 'TOKEN_REQUIRED'
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            // تحديد نوع الخطأ
            let errorType = 'INVALID_TOKEN';
            let errorMessage = 'رمز المصادقة غير صالح';

            if (err.name === 'TokenExpiredError') {
                errorType = 'TOKEN_EXPIRED';
                errorMessage = 'رمز المصادقة منتهي الصلاحية';
            }

            return res.status(403).json({
                message: errorMessage,
                error: errorType
            });
        }

        req.user = user;
        next();
    });
}

// وسيط للتحقق من أن المستخدم مدير
async function isAdmin(req, res, next) {
    try {
        // التحقق من أن المستخدم مدير (role_id = 1)
        if (req.user && req.user.role === 1) {
            return next();
        }

        // التحقق من أن المستخدم لديه صلاحية إدارة المستخدمين
        if (req.user && req.user.id) {
            const hasPermission = await permissionsManager.hasPermission(req.user.id, 'manage_users');

            if (hasPermission) {
                return next();
            }
        }

        return res.status(403).json({
            message: 'ليس لديك صلاحية الوصول إلى هذا المورد',
            error: 'PERMISSION_DENIED',
            requiredPermission: 'manage_users'
        });
    } catch (error) {
        console.error('خطأ في التحقق من صلاحيات المدير:', error);
        return res.status(500).json({
            message: 'حدث خطأ أثناء التحقق من الصلاحيات',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
}

// وسيط للتحقق من صلاحية معينة
function hasPermission(permissionCode) {
    return async (req, res, next) => {
        try {
            // التحقق من وجود المستخدم
            if (!req.user) {
                return res.status(401).json({
                    message: 'يجب تسجيل الدخول للوصول إلى هذا المورد',
                    error: 'UNAUTHORIZED'
                });
            }

            // التحقق من أن المستخدم مدير (role_id = 1)
            if (req.user.role === 1) {
                return next();
            }

            // التحقق من أن المستخدم لديه الصلاحية المطلوبة
            const hasRequiredPermission = await permissionsManager.hasPermission(req.user.id, permissionCode);

            if (hasRequiredPermission) {
                return next();
            }

            return res.status(403).json({
                message: 'ليس لديك صلاحية الوصول إلى هذا المورد',
                error: 'PERMISSION_DENIED',
                requiredPermission: permissionCode
            });
        } catch (error) {
            console.error(`خطأ في التحقق من صلاحية ${permissionCode}:`, error);
            return res.status(500).json({
                message: 'حدث خطأ أثناء التحقق من الصلاحيات',
                error: 'INTERNAL_SERVER_ERROR'
            });
        }
    };
}

module.exports = {
    authenticateToken,
    isAdmin,
    hasPermission
};
