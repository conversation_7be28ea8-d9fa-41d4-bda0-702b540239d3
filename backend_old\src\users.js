// وحدة إدارة المستخدمين لتطبيق Yemen GPS
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const db = require('./database');

// إنشاء مستخدم جديد
async function createUser(username, password, email, fullName, accountType = 'free') {
  try {
    // التحقق من وجود المستخدم
    const userCheck = await db.query(
      'SELECT * FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );
    
    if (userCheck.rows.length > 0) {
      throw new Error('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل');
    }
    
    // تشفير كلمة المرور
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // الحصول على معرف الدور للمستخدم العادي
    const roleResult = await db.query(
      'SELECT role_id FROM roles WHERE role_name = $1',
      ['user']
    );
    
    if (roleResult.rows.length === 0) {
      throw new Error('لم يتم العثور على دور المستخدم');
    }
    
    const roleId = roleResult.rows[0].role_id;
    
    // إدراج المستخدم الجديد
    const result = await db.query(
      `INSERT INTO users (
        username, password, email, full_name, account_type, role_id, 
        registration_date, is_active, is_verified
      ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, true, false) 
      RETURNING *`,
      [username, hashedPassword, email, fullName, accountType, roleId]
    );
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error);
    throw error;
  }
}

// تسجيل دخول المستخدم
async function loginUser(username, password) {
  try {
    // البحث عن المستخدم
    const result = await db.query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );
    
    if (result.rows.length === 0) {
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
    }
    
    const user = result.rows[0];
    
    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
    }
    
    // التحقق من أن الحساب نشط
    if (!user.is_active) {
      throw new Error('هذا الحساب غير نشط');
    }
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.user_id, 
        username: user.username,
        role: user.role_id 
      },
      process.env.JWT_SECRET || 'yemen_gps_secret_key',
      { expiresIn: '24h' }
    );
    
    // تحديث آخر تسجيل دخول
    await db.query(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE user_id = $1',
      [user.user_id]
    );
    
    return {
      token,
      user: {
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        accountType: user.account_type,
        isVerified: user.is_verified
      }
    };
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    throw error;
  }
}

// التحقق من صحة رمز JWT
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'yemen_gps_secret_key');
    return decoded;
  } catch (error) {
    console.error('خطأ في التحقق من الرمز:', error);
    throw new Error('رمز غير صالح أو منتهي الصلاحية');
  }
}

// الحصول على معلومات المستخدم بواسطة المعرف
async function getUserById(userId) {
  try {
    const result = await db.query(
      'SELECT * FROM users WHERE user_id = $1',
      [userId]
    );
    
    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في الحصول على معلومات المستخدم:', error);
    throw error;
  }
}

// تحديث معلومات المستخدم
async function updateUser(userId, userData) {
  try {
    const { email, fullName, profileImage, preferences } = userData;
    
    const result = await db.query(
      `UPDATE users 
       SET email = COALESCE($1, email), 
           full_name = COALESCE($2, full_name), 
           profile_image = COALESCE($3, profile_image), 
           preferences = COALESCE($4, preferences),
           updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $5
       RETURNING *`,
      [email, fullName, profileImage, preferences, userId]
    );
    
    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في تحديث معلومات المستخدم:', error);
    throw error;
  }
}

// الحصول على جميع المستخدمين (للمشرفين)
async function getAllUsers() {
  try {
    const result = await db.query(`
      SELECT u.user_id, u.username, u.email, u.full_name, u.phone, u.account_type, u.role_id, r.role_name, u.registration_date, u.last_login, u.is_active, u.is_verified
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.role_id
      ORDER BY u.user_id
    `);
    return result.rows;
  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error);
    throw error;
  }
}

// تحديث مستخدم بواسطة المشرف
async function updateUserByAdmin(userId, userData) {
  try {
    const { username, email, fullName, password, roleId, isActive } = userData;
    
    // تشفير كلمة المرور إذا تم تقديمها
    let hashedPassword = null;
    if (password) {
      const saltRounds = 10;
      hashedPassword = await bcrypt.hash(password, saltRounds);
    }
    
    // تحديث بيانات المستخدم
    const result = await db.query(
      `UPDATE users 
       SET username = COALESCE($1, username), 
           email = COALESCE($2, email), 
           full_name = COALESCE($3, full_name), 
           password = COALESCE($4, password),
           role_id = COALESCE($5, role_id),
           is_active = COALESCE($6, is_active),
           updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $7
       RETURNING *`,
      [username, email, fullName, hashedPassword, roleId, isActive, userId]
    );
    
    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في تحديث معلومات المستخدم:', error);
    throw error;
  }
}

// حذف مستخدم
async function deleteUser(userId) {
  try {
    const result = await db.query(
      'DELETE FROM users WHERE user_id = $1 RETURNING *',
      [userId]
    );
    
    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    throw error;
  }
}

// الحصول على جميع الأدوار
async function getAllRoles() {
  try {
    const result = await db.query(
      'SELECT role_id, role_name, description FROM roles ORDER BY role_id'
    );
    
    return result.rows;
  } catch (error) {
    console.error('خطأ في الحصول على قائمة الأدوار:', error);
    throw error;
  }
}

// تصدير الوظائف
module.exports = {
  createUser,
  loginUser,
  verifyToken,
  getUserById,
  updateUser,
  getAllUsers,
  updateUserByAdmin,
  deleteUser,
  getAllRoles
};
