// backend/src/users.js
const db = require('./database');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

const JWT_SECRET = process.env.JWT_SECRET || 'yemen_nav_secret_key';
const SALT_ROUNDS = 10;

// دالة لإنشاء مستخدم جديد
async function createUser(username, password, email, fullName, accountType = 'free') {
  try {
    // التحقق من وجود المستخدم
    const userCheck = await db.query(
      'SELECT * FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );

    if (userCheck.rows.length > 0) {
      throw new Error('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل');
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);

    // الحصول على معرف الدور للمستخدم العادي
    const roleResult = await db.query(
      'SELECT role_id FROM roles WHERE role_name = $1',
      ['user']
    );

    if (roleResult.rows.length === 0) {
      throw new Error('لم يتم العثور على دور المستخدم');
    }

    const roleId = roleResult.rows[0].role_id;

    // إدخال المستخدم الجديد
    const result = await db.query(
      `INSERT INTO users (username, password, email, full_name, account_type, role_id, is_active, is_verified)
       VALUES ($1, $2, $3, $4, $5, $6, true, false)
       RETURNING user_id, username, email, full_name, account_type, role_id`,
      [username, hashedPassword, email, fullName, accountType, roleId]
    );

    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    throw err;
  }
}

// دالة لتسجيل الدخول
async function loginUser(username, password) {
  try {
    // البحث عن المستخدم
    const result = await db.query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );

    if (result.rows.length === 0) {
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
    }

    const user = result.rows[0];

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
    }

    // التحقق من أن الحساب نشط
    if (!user.is_active) {
      throw new Error('الحساب غير نشط');
    }

    // تحديث آخر تسجيل دخول
    await db.query(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE user_id = $1',
      [user.user_id]
    );

    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.user_id, 
        username: user.username,
        roleId: user.role_id,
        accountType: user.account_type
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    return {
      token,
      user: {
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        accountType: user.account_type,
        roleId: user.role_id
      }
    };
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    throw err;
  }
}

// دالة للتحقق من رمز JWT
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (err) {
    console.error('خطأ في التحقق من الرمز:', err.message);
    throw new Error('رمز غير صالح');
  }
}

// دالة للحصول على معلومات المستخدم
async function getUserById(userId) {
  try {
    const result = await db.query(
      `SELECT user_id, username, email, full_name, profile_image, account_type, role_id, 
       registration_date, last_login, is_active, is_verified, preferences
       FROM users WHERE user_id = $1`,
      [userId]
    );

    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    return result.rows[0];
  } catch (err) {
    console.error('خطأ في الحصول على معلومات المستخدم:', err.message);
    throw err;
  }
}

// دالة لتحديث معلومات المستخدم
async function updateUser(userId, userData) {
  try {
    const { email, fullName, profileImage, preferences } = userData;
    
    const result = await db.query(
      `UPDATE users 
       SET email = COALESCE($1, email),
           full_name = COALESCE($2, full_name),
           profile_image = COALESCE($3, profile_image),
           preferences = COALESCE($4, preferences),
           updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $5
       RETURNING user_id, username, email, full_name, profile_image, account_type, preferences`,
      [email, fullName, profileImage, preferences, userId]
    );

    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    return result.rows[0];
  } catch (err) {
    console.error('خطأ في تحديث معلومات المستخدم:', err.message);
    throw err;
  }
}

module.exports = {
  createUser,
  loginUser,
  verifyToken,
  getUserById,
  updateUser
};