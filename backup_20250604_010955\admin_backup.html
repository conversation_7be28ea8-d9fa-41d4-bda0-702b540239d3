<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم يمن GPS</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- <PERSON><PERSON>wal Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/custom.css">

    <!-- خط Khalid Art المخصص -->
    <style>
        @font-face {
            font-family: 'Khalid-Art-Bold';
            src: url('/fonts/Khalid-Art-bold.ttf') format('truetype');
            font-weight: bold;
            font-style: normal;
            font-display: swap;
        }

        body {
            font-family: 'Khalid-Art-Bold', 'Tajawal', sans-serif;
            direction: rtl;
            padding-top: 70px;
        }

        /* نافذة تسجيل الدخول الاحترافية */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
            padding-top: 0;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            margin: 20px;
            position: relative;
            z-index: 1;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
        }

        .login-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
        }

        .login-body {
            padding: 40px 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.8);
        }

        .form-floating .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .form-floating label {
            color: #6c757d;
            font-weight: 500;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .login-footer {
            text-align: center;
            padding: 20px 30px;
            background: rgba(248, 249, 250, 0.5);
            color: #6c757d;
            font-size: 14px;
        }

        /* مؤشر التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-overlay.show {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 10000;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* تحسينات إضافية */
        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        .btn-action {
            margin: 0 2px;
        }

        /* تأثيرات أيقونة الموقع الحالي */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.1;
            }
            100% {
                transform: scale(1);
                opacity: 0.3;
            }
        }

        .current-location-marker {
            z-index: 1000 !important;
        }

        /* تحسين زر تحديد الموقع */
        #locationBtn {
            position: relative;
            overflow: hidden;
        }

        #locationBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        #locationBtn:active {
            transform: translateY(0);
        }

        /* تأثير النبض للزر عند التحميل */
        #locationBtn:disabled {
            animation: buttonPulse 1.5s infinite;
        }

        @keyframes buttonPulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }
    </style>
</head>
<body>
    <!-- نافذة تسجيل الدخول الاحترافية -->
    <div class="login-container" id="loginContainer">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <h3 class="mb-0">خرائط اليمن</h3>
                <p class="mb-0 mt-2">لوحة التحكم الإدارية</p>
            </div>

            <div class="login-body">
                <form id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>اسم المستخدم
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>كلمة المرور
                        </label>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        <a href="#" class="text-decoration-none" style="color: #667eea;">
                            نسيت كلمة المرور؟
                        </a>
                    </div>

                    <button type="submit" class="login-btn" id="loginBtn">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div class="text-center mt-3">
                    <div class="alert alert-info d-none" id="loginAlert"></div>
                </div>
            </div>

            <div class="login-footer">
                <p class="mb-0">
                    <i class="fas fa-shield-alt me-1"></i>
                    نظام آمن ومحمي
                </p>
                <small>جميع الحقوق محفوظة © 2024</small>
            </div>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <img src="img/logo.png" alt="شعار يمن GPS" onerror="this.src='img/placeholder.svg'">
                لوحة تحكم يمن GPS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin.html">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-globe"></i> الموقع الرئيسي
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <a class="btn btn-dark dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="img/user-avatar.png" alt="صورة المستخدم" class="user-avatar" id="userAvatar" onerror="this.src='img/placeholder.svg'">
                            <span id="userName">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="#" id="profileLink">
                                    <i class="fas fa-user"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" id="settingsLink">
                                    <i class="fas fa-cog"></i> الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="logout()">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </h2>
                        <p class="text-muted">مرحباً بك في لوحة تحكم يمن GPS. يمكنك إدارة المستخدمين والمواقع والتصنيفات والعملاء من هنا.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="usersCount">0</h4>
                                <p class="mb-0">المستخدمين</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="categoriesCount">0</h4>
                                <p class="mb-0">التصنيفات</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tags fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="locationsCount">0</h4>
                                <p class="mb-0">المواقع</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-map-marker-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 id="clientsCount">0</h4>
                                <p class="mb-0">العملاء</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-building fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- علامات التبويب -->
        <ul class="nav nav-tabs mb-4" id="dataTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">
                    <i class="fas fa-users"></i> المستخدمين
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab" aria-controls="locations" aria-selected="false">
                    <i class="fas fa-map-marker-alt"></i> المواقع
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="false">
                    <i class="fas fa-tags"></i> التصنيفات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="clients-tab" data-bs-toggle="tab" data-bs-target="#clients" type="button" role="tab" aria-controls="clients" aria-selected="false">
                    <i class="fas fa-building"></i> العملاء
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="governorates-tab" data-bs-toggle="tab" data-bs-target="#governorates" type="button" role="tab" aria-controls="governorates" aria-selected="false">
                    <i class="fas fa-city"></i> المحافظات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="maps-tab" data-bs-toggle="tab" data-bs-target="#maps" type="button" role="tab" aria-controls="maps" aria-selected="false">
                    <i class="fas fa-map"></i> الخرائط
                </button>
            </li>
        </ul>

        <!-- محتوى التبويبات -->
        <div class="tab-content" id="dataTabsContent">
            <!-- تبويب المستخدمين -->
            <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة المستخدمين</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addUserBtn">
                                <i class="fas fa-plus"></i> إضافة مستخدم
                            </button>
                            <input type="text" class="form-control form-control-sm" id="usersSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب المواقع -->
            <div class="tab-pane fade" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة المواقع</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addLocationBtn">
                                <i class="fas fa-plus"></i> إضافة موقع
                            </button>
                            <input type="text" class="form-control form-control-sm" id="locationsSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>الوصف</th>
                                        <th>التصنيف</th>
                                        <th>الإحداثيات</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="locationsTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب التصنيفات -->
            <div class="tab-pane fade" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة التصنيفات</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addCategoryBtn">
                                <i class="fas fa-plus"></i> إضافة تصنيف
                            </button>
                            <input type="text" class="form-control form-control-sm" id="categoriesSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>التصنيف</th>
                                        <th>الأيقونة</th>
                                        <th>اللون</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="categoriesTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب العملاء -->
            <div class="tab-pane fade" id="clients" role="tabpanel" aria-labelledby="clients-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>قائمة العملاء</span>
                        <div class="d-flex">
                            <button class="btn btn-sm btn-success btn-add" id="addClientBtn">
                                <i class="fas fa-plus"></i> إضافة عميل
                            </button>
                            <input type="text" class="form-control form-control-sm" id="clientsSearch" placeholder="بحث...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>رقم الجهاز</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="clientsTable">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب المحافظات -->
            <div class="tab-pane fade" id="governorates" role="tabpanel" aria-labelledby="governorates-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة المحافظات</h5>
                        <div>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addGovernorateModal">
                                <i class="fas fa-plus"></i> إضافة محافظة
                            </button>
                            <button class="btn btn-success btn-sm" onclick="loadGovernorates()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- شريط البحث -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="governoratesSearch" placeholder="البحث في المحافظات...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <select class="form-select" id="governoratesFilter">
                                    <option value="">جميع المحافظات</option>
                                    <option value="complete">مكتملة البيانات</option>
                                    <option value="partial">بيانات جزئية</option>
                                    <option value="in_progress">قيد التطوير</option>
                                    <option value="pending">في الانتظار</option>
                                </select>
                            </div>
                        </div>

                        <!-- جدول المحافظات -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>الرقم</th>
                                        <th>الاسم العربي</th>
                                        <th>الاسم الإنجليزي</th>
                                        <th>الكود</th>
                                        <th>عدد المواقع</th>
                                        <th>حالة البيانات</th>
                                        <th>الإحداثيات</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="governoratesTable">
                                    <!-- سيتم ملء البيانات ديناميكياً -->
                                </tbody>
                            </table>
                        </div>

                        <!-- إحصائيات المحافظات -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4 id="totalGovernorates">0</h4>
                                        <p class="mb-0">إجمالي المحافظات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4 id="completeGovernorates">0</h4>
                                        <p class="mb-0">مكتملة البيانات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 id="partialGovernorates">0</h4>
                                        <p class="mb-0">بيانات جزئية</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4 id="totalPlacesInGovernorates">0</h4>
                                        <p class="mb-0">إجمالي المواقع</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب الخرائط -->
            <div class="tab-pane fade" id="maps" role="tabpanel" aria-labelledby="maps-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الخرائط</h5>
                        <div>
                            <button class="btn btn-info btn-sm" onclick="getCurrentLocation()" id="locationBtn" title="تحديد موقعي الحالي">
                                <i class="fas fa-crosshairs"></i> موقعي الحالي
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="refreshMap()">
                                <i class="fas fa-sync"></i> تحديث الخريطة
                            </button>
                            <button class="btn btn-success btn-sm" onclick="addNewLocation()">
                                <i class="fas fa-plus"></i> إضافة موقع جديد
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- عنصر الخريطة -->
                        <div id="map" style="width: 100%; height: 500px; margin-bottom: 20px;"></div>
                        
                        <!-- أدوات التحكم بالخريطة -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>البحث عن موقع:</label>
                                    <input type="text" id="locationSearch" class="form-control" placeholder="أدخل اسم الموقع...">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>تصفية حسب التصنيف:</label>
                                    <select id="categoryFilter" class="form-select">
                                        <option value="">جميع التصنيفات</option>
                                        <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>تصفية حسب العميل:</label>
                                    <select id="clientFilter" class="form-select">
                                        <option value="">جميع العملاء</option>
                                        <!-- سيتم ملء هذه القائمة ديناميكياً -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل مستخدم -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="mb-3">
                            <label for="fullName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>
                        <div class="mb-3">
                            <label for="userUsername" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="userUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="userEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="userPhone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="userPhone">
                        </div>
                        <div class="mb-3">
                            <label for="userPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="userPassword">
                            <small class="form-text text-muted">اتركه فارغاً إذا كنت لا ترغب في تغيير كلمة المرور</small>
                        </div>
                        <div class="mb-3">
                            <label for="userRole" class="form-label">الدور</label>
                            <select class="form-select" id="userRole" required>
                                <option value="1">مدير</option>
                                <option value="2">مستخدم عادي</option>
                                <option value="3">مطور</option>
                            </select>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="userActive" checked>
                            <label class="form-check-label" for="userActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل موقع -->
    <div class="modal fade" id="locationModal" tabindex="-1" aria-labelledby="locationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="locationModalLabel">إضافة موقع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="locationForm">
                        <input type="hidden" id="locationId">
                        <div class="mb-3">
                            <label for="locationName" class="form-label">اسم الموقع</label>
                            <input type="text" class="form-control" id="locationName" required>
                        </div>
                        <div class="mb-3">
                            <label for="locationDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="locationCategory" class="form-label">التصنيف</label>
                            <select class="form-select" id="locationCategory" required>
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </select>
                        </div>
                        <div class="row mb-3">
                            <div class="col">
                                <label for="locationLat" class="form-label">خط العرض</label>
                                <input type="number" step="any" class="form-control" id="locationLat" required>
                            </div>
                            <div class="col">
                                <label for="locationLng" class="form-label">خط الطول</label>
                                <input type="number" step="any" class="form-control" id="locationLng" required>
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="locationActive" checked>
                            <label class="form-check-label" for="locationActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveLocationBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل تصنيف -->
    <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalLabel">إضافة تصنيف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">اسم التصنيف</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="mb-3">
                            <label for="categoryDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="categoryIcon" class="form-label">الأيقونة</label>
                            <div class="input-group">
                                <span class="input-group-text"><i id="iconPreview" class="fas fa-tag"></i></span>
                                <input type="text" class="form-control" id="categoryIcon" placeholder="fa-tag">
                            </div>
                            <small class="form-text text-muted">أدخل اسم أيقونة Font Awesome (مثال: fa-map-marker)</small>
                        </div>
                        <div class="mb-3">
                            <label for="categoryColor" class="form-label">اللون</label>
                            <input type="color" class="form-control form-control-color" id="categoryColor" value="#198754">
                        </div>
                        <div class="mb-3">
                            <label for="categoryParent" class="form-label">التصنيف الأب</label>
                            <select class="form-select" id="categoryParent">
                                <option value="">بدون تصنيف أب</option>
                                <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveCategoryBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة/تعديل عميل -->
    <div class="modal fade" id="clientModal" tabindex="-1" aria-labelledby="clientModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clientModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="clientForm">
                        <input type="hidden" id="clientId">
                        <div class="mb-3">
                            <label for="clientName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="clientName" required>
                        </div>
                        <div class="mb-3">
                            <label for="clientEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="clientEmail">
                        </div>
                        <div class="mb-3">
                            <label for="clientPhone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="clientPhone">
                        </div>
                        <div class="mb-3">
                            <label for="clientAddress" class="form-label">العنوان</label>
                            <textarea class="form-control" id="clientAddress" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="clientDeviceSN" class="form-label">رقم الجهاز</label>
                            <input type="text" class="form-control" id="clientDeviceSN">
                        </div>
                        <div class="mb-3">
                            <label for="clientLicenseN" class="form-label">رقم الترخيص</label>
                            <input type="text" class="form-control" id="clientLicenseN">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="clientActive" checked>
                            <label class="form-check-label" for="clientActive">نشط</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveClientBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تأكيد الحذف -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmMessage">هل أنت متأكد من حذف هذا العنصر؟</p>
                    <input type="hidden" id="deleteItemId">
                    <input type="hidden" id="deleteItemType">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة موقع جديد -->
    <div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addLocationModalLabel">إضافة موقع جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addLocationForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newLocationName" class="form-label">اسم الموقع *</label>
                                    <input type="text" class="form-control" id="newLocationName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newLocationCategory" class="form-label">التصنيف *</label>
                                    <select class="form-select" id="newLocationCategory" required>
                                        <option value="">اختر التصنيف...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newLocationLat" class="form-label">خط العرض</label>
                                    <input type="number" step="any" class="form-control" id="newLocationLat" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newLocationLng" class="form-label">خط الطول</label>
                                    <input type="number" step="any" class="form-control" id="newLocationLng" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="newLocationAddress" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="newLocationAddress">
                        </div>
                        <div class="mb-3">
                            <label for="newLocationPhone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="newLocationPhone">
                        </div>
                        <div class="mb-3">
                            <label for="newLocationDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="newLocationDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewLocation()">حفظ الموقع</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة محافظة جديدة -->
    <div class="modal fade" id="addGovernorateModal" tabindex="-1" aria-labelledby="addGovernorateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addGovernorateModalLabel">إضافة محافظة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addGovernorateForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernorateName" class="form-label">الاسم العربي *</label>
                                    <input type="text" class="form-control" id="newGovernorateName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernorateNameEn" class="form-label">الاسم الإنجليزي *</label>
                                    <input type="text" class="form-control" id="newGovernorateNameEn" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernorateCode" class="form-label">كود المحافظة</label>
                                    <input type="text" class="form-control" id="newGovernorateCode" placeholder="مثال: SA">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernoratePopulation" class="form-label">عدد السكان</label>
                                    <input type="number" class="form-control" id="newGovernoratePopulation">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernorateLat" class="form-label">خط العرض</label>
                                    <input type="number" step="any" class="form-control" id="newGovernorateLat">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="newGovernorateLng" class="form-label">خط الطول</label>
                                    <input type="number" step="any" class="form-control" id="newGovernorateLng">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="newGovernorateDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="newGovernorateDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewGovernorate()">حفظ المحافظة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Admin JS -->
    <script src="js/admin.js"></script>

    <!-- كود لوحة التحكم المستقل -->
    <script>
        // سيتم التعامل مع المصادقة وتحميل البيانات عبر admin.js
        console.log('تحميل لوحة التحكم - سيتم التعامل مع المصادقة عبر admin.js');

        // ===== نظام تسجيل الدخول الاحترافي =====

        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            setupLoginForm();
            setupTabEvents();
        });

        // إعداد أحداث التبويبات
        function setupTabEvents() {
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-bs-target');
                    console.log('تم النقر على تبويب:', targetId);

                    // تحميل البيانات حسب التبويب المحدد
                    setTimeout(() => {
                        switch(targetId) {
                            case '#users':
                                loadUsers();
                                break;
                            case '#locations':
                                loadLocations();
                                break;
                            case '#categories':
                                loadCategories();
                                break;
                            case '#clients':
                                loadClients();
                                break;
                            case '#governorates':
                                loadGovernorates();
                                break;
                            case '#maps':
                                // تحميل الخريطة
                                initializeMap();
                                loadMapLocations();
                                break;
                        }
                    }, 100);
                });
            });
        }

        // التحقق من حالة تسجيل الدخول
        function checkLoginStatus() {
            const isLoggedIn = sessionStorage.getItem('isLoggedIn');
            const loginContainer = document.getElementById('loginContainer');
            const dashboardContainer = document.querySelector('.container-fluid');

            if (isLoggedIn === 'true') {
                // المستخدم مسجل دخول - إخفاء نافذة تسجيل الدخول وإظهار لوحة التحكم
                loginContainer.style.display = 'none';
                dashboardContainer.style.display = 'block';
                document.body.style.paddingTop = '70px';

                // تحميل بيانات لوحة التحكم
                loadDashboardData();
            } else {
                // المستخدم غير مسجل دخول - إظهار نافذة تسجيل الدخول
                loginContainer.style.display = 'flex';
                dashboardContainer.style.display = 'none';
                document.body.style.paddingTop = '0';
            }
        }

        // إعداد نموذج تسجيل الدخول
        function setupLoginForm() {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
        }

        // التعامل مع تسجيل الدخول
        async function handleLogin(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;
            const loginBtn = document.getElementById('loginBtn');
            const loginAlert = document.getElementById('loginAlert');

            // التحقق من البيانات
            if (!username || !password) {
                showLoginAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // تعطيل الزر وإظهار مؤشر التحميل
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // تسجيل دخول ناجح
                    showLoginAlert('تم تسجيل الدخول بنجاح!', 'success');

                    // حفظ حالة تسجيل الدخول
                    sessionStorage.setItem('isLoggedIn', 'true');
                    sessionStorage.setItem('userInfo', JSON.stringify(result.user));

                    if (rememberMe) {
                        localStorage.setItem('rememberLogin', 'true');
                    }

                    // الانتقال إلى لوحة التحكم بعد ثانيتين
                    setTimeout(() => {
                        checkLoginStatus();
                    }, 1500);

                } else {
                    // فشل تسجيل الدخول
                    showLoginAlert(result.error || 'خطأ في تسجيل الدخول', 'danger');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showLoginAlert('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'danger');
            } finally {
                // إعادة تفعيل الزر
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول';
            }
        }

        // عرض تنبيه تسجيل الدخول
        function showLoginAlert(message, type) {
            const loginAlert = document.getElementById('loginAlert');
            loginAlert.className = `alert alert-${type}`;
            loginAlert.textContent = message;
            loginAlert.classList.remove('d-none');

            // إخفاء التنبيه بعد 5 ثوان
            setTimeout(() => {
                loginAlert.classList.add('d-none');
            }, 5000);
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // مسح بيانات تسجيل الدخول
                sessionStorage.clear();
                localStorage.removeItem('rememberLogin');

                // إعادة تحميل الصفحة
                location.reload();
            }
        }

        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type} show`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.getElementById('notificationArea').appendChild(notification);

            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }

        // كود الخرائط - Leaflet (نفس الخريطة من index.html)
        let map;
        let placesLayer;
        let markers = [];

        // دالة تهيئة الخريطة
        function initMap() {
            console.log('جاري تهيئة الخريطة...');

            // التحقق من وجود عنصر الخريطة
            const mapElement = document.getElementById("map");
            if (!mapElement) {
                console.error('لم يتم العثور على عنصر الخريطة');
                return;
            }

            // إنشاء الخريطة باستخدام Leaflet
            map = L.map('map').setView([15.3694, 44.1910], 12);

            // طبقات الخرائط المختلفة (نفس الطبقات من index.html)
            const baseLayers = {
                // خريطة الشوارع التفصيلية
                'الشوارع التفصيلية': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19
                }),

                // خريطة الأقمار الصناعية مع الأسماء
                'الأقمار الصناعية': L.layerGroup([
                    L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '© Esri, DigitalGlobe, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                        maxZoom: 19
                    }),
                    L.tileLayer('https://{s}.basemaps.cartocdn.com/light_only_labels/{z}/{x}/{y}{r}.png', {
                        attribution: '© CARTO',
                        maxZoom: 19,
                        subdomains: 'abcd'
                    })
                ])
            };

            // إضافة الطبقة الافتراضية
            baseLayers['الشوارع التفصيلية'].addTo(map);

            // إضافة تحكم الطبقات
            L.control.layers(baseLayers).addTo(map);

            // إضافة مقياس المسافة
            L.control.scale({
                position: 'bottomleft',
                metric: true,
                imperial: false
            }).addTo(map);

            // إضافة طبقة المواقع
            placesLayer = L.layerGroup().addTo(map);

            // إضافة حدث النقر لإضافة مواقع جديدة
            map.on('click', onMapClick);

            // تحميل المواقع الموجودة
            loadMapLocations();

            console.log('تم تهيئة الخريطة بنجاح');
        }

        // دالة تحديد الموقع الحالي
        function getCurrentLocation() {
            const locationBtn = document.getElementById('locationBtn');

            // تغيير حالة الزر إلى التحميل
            locationBtn.disabled = true;
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديد...';

            // التحقق من دعم المتصفح لـ Geolocation
            if (!navigator.geolocation) {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                resetLocationButton();
                return;
            }

            // خيارات تحديد الموقع
            const options = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            };

            // طلب الموقع الحالي
            navigator.geolocation.getCurrentPosition(
                onLocationSuccess,
                onLocationError,
                options
            );
        }

        // عند نجاح تحديد الموقع
        function onLocationSuccess(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = position.coords.accuracy;

            console.log(`تم تحديد الموقع: ${lat}, ${lng} (دقة: ${accuracy}م)`);

            // إزالة العلامة السابقة إن وجدت
            if (window.currentLocationMarker) {
                map.removeLayer(window.currentLocationMarker);
            }

            // إنشاء أيقونة مخصصة للموقع الحالي
            const currentLocationIcon = L.divIcon({
                className: 'current-location-marker',
                html: `
                    <div style="
                        background: #007bff;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        border: 3px solid white;
                        box-shadow: 0 0 10px rgba(0,123,255,0.5);
                        position: relative;
                        z-index: 1000;
                    ">
                        <div style="
                            position: absolute;
                            top: -5px;
                            left: -5px;
                            width: 30px;
                            height: 30px;
                            border: 2px solid #007bff;
                            border-radius: 50%;
                            opacity: 0.3;
                            animation: pulse 2s infinite;
                        "></div>
                    </div>
                `,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });

            // إضافة العلامة للموقع الحالي
            window.currentLocationMarker = L.marker([lat, lng], {
                icon: currentLocationIcon
            }).addTo(map);

            // إضافة نافذة معلومات
            window.currentLocationMarker.bindPopup(`
                <div style="text-align: center;">
                    <h6><i class="fas fa-map-marker-alt text-primary"></i> موقعك الحالي</h6>
                    <p><strong>الإحداثيات:</strong><br>
                    ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                    <p><strong>دقة التحديد:</strong> ${Math.round(accuracy)} متر</p>
                    <button class="btn btn-sm btn-success" onclick="addLocationHere(${lat}, ${lng})">
                        <i class="fas fa-plus"></i> إضافة موقع هنا
                    </button>
                </div>
            `).openPopup();

            // التحرك إلى الموقع الحالي
            map.setView([lat, lng], 15);

            showNotification('تم تحديد موقعك الحالي بنجاح!', 'success');
            resetLocationButton();
        }

        // عند فشل تحديد الموقع
        function onLocationError(error) {
            let errorMessage = 'حدث خطأ في تحديد الموقع';

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = 'تم رفض الإذن لتحديد الموقع. يرجى السماح بالوصول للموقع في إعدادات المتصفح.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                    break;
                case error.TIMEOUT:
                    errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                    break;
            }

            console.error('خطأ في تحديد الموقع:', error);
            showNotification(errorMessage, 'error');
            resetLocationButton();
        }

        // إعادة تعيين زر الموقع
        function resetLocationButton() {
            const locationBtn = document.getElementById('locationBtn');
            if (locationBtn) {
                locationBtn.disabled = false;
                locationBtn.innerHTML = '<i class="fas fa-crosshairs"></i> موقعي الحالي';
            }
        }

        // إضافة موقع في الموقع الحالي
        function addLocationHere(lat, lng) {
            showAddLocationModal(lat, lng);
        }

        // دالة التعامل مع النقر على الخريطة لإضافة موقع جديد
        async function onMapClick(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;

            if (confirm('هل تريد إضافة موقع جديد في هذه النقطة؟')) {
                showAddLocationModal(lat, lng);
            }
        }

        // تحميل المواقع على الخريطة
        function loadMapLocations() {
            fetch('/api/maps')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.places) {
                        data.places.forEach(place => {
                            addMarkerToMap(place);
                        });
                        console.log(`تم تحميل ${data.places.length} موقع على الخريطة`);
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المواقع:', error);
                });
        }

        // إضافة علامة على الخريطة
        function addMarkerToMap(place) {
            const categoryIcon = getCategoryIcon(place.category.name);

            const marker = L.marker([place.lat, place.lng], {
                icon: L.divIcon({
                    className: 'custom-marker',
                    html: `
                        <div style="background: ${categoryIcon.color}; width: 35px; height: 35px; border-radius: 50%; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                            <i class="${categoryIcon.icon}"></i>
                        </div>
                    `,
                    iconSize: [35, 35],
                    iconAnchor: [17, 17]
                })
            });

            // نافذة المعلومات
            const popupContent = `
                <div style="max-width: 250px;">
                    <h6>${place.name}</h6>
                    <p>${place.description}</p>
                    <p><strong>العنوان:</strong> ${place.address}</p>
                    <p><strong>الهاتف:</strong> ${place.phone}</p>
                    <p><strong>التصنيف:</strong> ${place.category.name}</p>
                    <p><strong>التقييم:</strong> ${place.rating}/5</p>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-sm btn-primary" onclick="editLocationOnMap(${place.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteLocationFromMap(${place.id})">حذف</button>
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent);
            placesLayer.addLayer(marker);
        }

        // الحصول على أيقونة التصنيف
        function getCategoryIcon(categoryName) {
            const icons = {
                'مطاعم': { icon: 'fas fa-utensils', color: '#FF5722' },
                'فنادق': { icon: 'fas fa-bed', color: '#2196F3' },
                'مستشفيات': { icon: 'fas fa-hospital', color: '#F44336' },
                'محطات وقود': { icon: 'fas fa-gas-pump', color: '#4CAF50' },
                'مراكز تسوق': { icon: 'fas fa-shopping-cart', color: '#9C27B0' },
                'مدارس': { icon: 'fas fa-school', color: '#FF9800' },
                'مساجد': { icon: 'fas fa-mosque', color: '#795548' },
                'بنوك': { icon: 'fas fa-university', color: '#607D8B' }
            };

            return icons[categoryName] || { icon: 'fas fa-map-marker-alt', color: '#666666' };
        }

        // عرض نموذج إضافة موقع جديد
        function showAddLocationModal(lat, lng) {
            document.getElementById('newLocationLat').value = lat;
            document.getElementById('newLocationLng').value = lng;

            // تحميل التصنيفات
            loadCategoriesForNewLocation();

            const modal = new bootstrap.Modal(document.getElementById('addLocationModal'));
            modal.show();
        }

        // تحميل التصنيفات للموقع الجديد
        function loadCategoriesForNewLocation() {
            fetch('/api/categories')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.categories) {
                        const select = document.getElementById('newLocationCategory');
                        select.innerHTML = '<option value="">اختر التصنيف...</option>';

                        data.categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل التصنيفات:', error);
                });
        }

        // دالة حفظ موقع جديد
        async function saveNewLocation() {
            const name = document.getElementById('newLocationName').value.trim();
            const categoryId = document.getElementById('newLocationCategory').value;
            const lat = parseFloat(document.getElementById('newLocationLat').value);
            const lng = parseFloat(document.getElementById('newLocationLng').value);
            const address = document.getElementById('newLocationAddress').value.trim();
            const phone = document.getElementById('newLocationPhone').value.trim();
            const description = document.getElementById('newLocationDescription').value.trim();

            // التحقق من البيانات المطلوبة
            if (!name || !categoryId) {
                alert('يرجى ملء الحقول المطلوبة (الاسم والتصنيف)');
                return;
            }

            if (isNaN(lat) || isNaN(lng)) {
                alert('إحداثيات غير صحيحة');
                return;
            }

            try {
                const locationData = {
                    name_ar: name,
                    name_en: name,
                    description_ar: description,
                    description_en: description,
                    latitude: lat,
                    longitude: lng,
                    address_ar: address,
                    address_en: address,
                    phone: phone,
                    category_id: parseInt(categoryId),
                    governorate_id: 1, // سيتم تحديدها لاحقاً
                    is_active: true
                };

                const response = await fetch('/api/admin/places', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(locationData)
                });

                const result = await response.json();

                if (result.success) {
                    alert('تم إضافة الموقع بنجاح!');

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addLocationModal'));
                    modal.hide();

                    // مسح النموذج
                    document.getElementById('addLocationForm').reset();

                    // تحديث الخريطة
                    loadMapLocations();

                    // تحديث قائمة المواقع إذا كانت مفتوحة
                    if (typeof loadLocations === 'function') {
                        loadLocations();
                    }
                } else {
                    alert('خطأ في إضافة الموقع: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('خطأ في حفظ الموقع:', error);
                alert('حدث خطأ أثناء حفظ الموقع');
            }
        }

        // دالة تعديل موقع من الخريطة
        function editLocationOnMap(locationId) {
            alert(`سيتم تنفيذ تعديل الموقع ${locationId} قريباً`);
        }

        // دالة حذف موقع من الخريطة
        async function deleteLocationFromMap(locationId) {
            if (!confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/places/${locationId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    alert('تم حذف الموقع بنجاح!');
                    loadMapLocations(); // تحديث الخريطة

                    // تحديث قائمة المواقع إذا كانت مفتوحة
                    if (typeof loadLocations === 'function') {
                        loadLocations();
                    }
                } else {
                    alert('خطأ في حذف الموقع: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('خطأ في حذف الموقع:', error);
                alert('حدث خطأ أثناء حذف الموقع');
            }
        }

        // ===== دوال إدارة المحافظات =====

        // تحميل المحافظات
        async function loadGovernorates() {
            try {
                const response = await fetch('/api/governorates');
                const data = await response.json();

                if (data.success && data.governorates) {
                    displayGovernorates(data.governorates);
                    updateGovernoratesStats(data.governorates);
                } else {
                    console.error('خطأ في تحميل المحافظات:', data.error);
                    showNotification('حدث خطأ أثناء تحميل بيانات المحافظات', 'error');
                }
            } catch (error) {
                console.error('خطأ في تحميل المحافظات:', error);
                showNotification('حدث خطأ أثناء تحميل بيانات المحافظات', 'error');
            }
        }

        // عرض المحافظات في الجدول
        function displayGovernorates(governorates) {
            const tbody = document.getElementById('governoratesTable');
            if (!tbody) return;

            tbody.innerHTML = '';

            governorates.forEach(gov => {
                const statusBadge = getStatusBadge(gov.data_status);
                const coordinates = gov.latitude && gov.longitude ?
                    `${parseFloat(gov.latitude).toFixed(4)}, ${parseFloat(gov.longitude).toFixed(4)}` :
                    'غير محدد';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${gov.id}</td>
                    <td><strong>${gov.name_ar}</strong></td>
                    <td>${gov.name_en}</td>
                    <td><span class="badge bg-secondary">${gov.code || 'غير محدد'}</span></td>
                    <td><span class="badge bg-info">${gov.actual_places_count}</span></td>
                    <td>${statusBadge}</td>
                    <td><small>${coordinates}</small></td>
                    <td><small>${gov.created_at ? new Date(gov.created_at).toLocaleDateString('ar-SA') : 'غير محدد'}</small></td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-primary" onclick="editGovernorate(${gov.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-info" onclick="viewGovernorateDetails(${gov.id})" title="التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteGovernorate(${gov.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // الحصول على شارة الحالة
        function getStatusBadge(status) {
            const badges = {
                'complete': '<span class="badge bg-success">مكتملة</span>',
                'partial': '<span class="badge bg-warning">جزئية</span>',
                'in_progress': '<span class="badge bg-info">قيد التطوير</span>',
                'pending': '<span class="badge bg-secondary">في الانتظار</span>'
            };
            return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
        }

        // تحديث إحصائيات المحافظات
        function updateGovernoratesStats(governorates) {
            const total = governorates.length;
            const complete = governorates.filter(g => g.data_status === 'complete').length;
            const partial = governorates.filter(g => g.data_status === 'partial').length;
            const totalPlaces = governorates.reduce((sum, g) => sum + (g.actual_places_count || 0), 0);

            document.getElementById('totalGovernorates').textContent = total;
            document.getElementById('completeGovernorates').textContent = complete;
            document.getElementById('partialGovernorates').textContent = partial;
            document.getElementById('totalPlacesInGovernorates').textContent = totalPlaces;
        }

        // حفظ محافظة جديدة
        async function saveNewGovernorate() {
            const nameAr = document.getElementById('newGovernorateName').value.trim();
            const nameEn = document.getElementById('newGovernorateNameEn').value.trim();
            const code = document.getElementById('newGovernorateCode').value.trim();
            const population = document.getElementById('newGovernoratePopulation').value;
            const lat = document.getElementById('newGovernorateLat').value;
            const lng = document.getElementById('newGovernorateLng').value;
            const description = document.getElementById('newGovernorateDescription').value.trim();

            // التحقق من البيانات المطلوبة
            if (!nameAr || !nameEn) {
                alert('يرجى ملء الحقول المطلوبة (الاسم العربي والإنجليزي)');
                return;
            }

            try {
                const governorateData = {
                    name_ar: nameAr,
                    name_en: nameEn,
                    code: code,
                    population: population ? parseInt(population) : null,
                    latitude: lat ? parseFloat(lat) : null,
                    longitude: lng ? parseFloat(lng) : null,
                    description: description
                };

                const response = await fetch('/api/admin/governorates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(governorateData)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('تم إضافة المحافظة بنجاح!', 'success');

                    // إغلاق النموذج
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addGovernorateModal'));
                    modal.hide();

                    // مسح النموذج
                    document.getElementById('addGovernorateForm').reset();

                    // تحديث قائمة المحافظات
                    loadGovernorates();
                } else {
                    alert('خطأ في إضافة المحافظة: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('خطأ في حفظ المحافظة:', error);
                alert('حدث خطأ أثناء حفظ المحافظة');
            }
        }

        // تعديل محافظة
        function editGovernorate(governorateId) {
            alert(`سيتم تنفيذ تعديل المحافظة ${governorateId} قريباً`);
        }

        // عرض تفاصيل المحافظة
        function viewGovernorateDetails(governorateId) {
            alert(`سيتم عرض تفاصيل المحافظة ${governorateId} قريباً`);
        }

        // حذف محافظة
        async function deleteGovernorate(governorateId) {
            if (!confirm('هل أنت متأكد من حذف هذه المحافظة؟ سيتم حذف جميع المواقع المرتبطة بها أيضاً.')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/governorates/${governorateId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('تم حذف المحافظة بنجاح!', 'success');
                    loadGovernorates(); // تحديث القائمة
                } else {
                    alert('خطأ في حذف المحافظة: ' + (result.error || 'خطأ غير معروف'));
                }
            } catch (error) {
                console.error('خطأ في حذف المحافظة:', error);
                alert('حدث خطأ أثناء حذف المحافظة');
            }
        }

        // دالة تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            console.log('بدء تحميل بيانات لوحة التحكم...');

            try {
                // تحميل الإحصائيات أولاً
                await loadStats();

                // تحميل البيانات الأساسية
                await loadUsers();
                await loadCategories();
                await loadLocations();
                await loadClients();
                await loadGovernorates();

                console.log('تم تحميل جميع بيانات لوحة التحكم بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
                showNotification('حدث خطأ في تحميل البيانات', 'error');
            }
        }

        // تحميل الإحصائيات
        async function loadStats() {
            try {
                const response = await fetch('/api/admin/stats');
                const data = await response.json();

                if (data.success && data.stats) {
                    updateStatsDisplay(data.stats);
                } else {
                    console.error('خطأ في تحميل الإحصائيات:', data.error);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // تحديث عرض الإحصائيات
        function updateStatsDisplay(stats) {
            // تحديث عدادات الإحصائيات في الصفحة الرئيسية
            const elements = {
                'usersCount': stats.users || 0,
                'locationsCount': stats.locations || 0,
                'categoriesCount': stats.categories || 0,
                'clientsCount': stats.clients || 0
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });

            console.log('تم تحديث الإحصائيات:', stats);
        }

        // دالة تحميل المستخدمين
        function loadUsers() {
            console.log('جاري تحميل المستخدمين...');

            fetch('/api/admin/users')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل المستخدمين');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('استجابة API المستخدمين:', data);
                    if (data.success && data.users) {
                        console.log(`تم تحميل ${data.users.length} مستخدم:`, data.users);
                        displayUsers(data.users);
                    } else {
                        console.error('خطأ في البيانات:', data.error);
                        showNotification('فشل في تحميل بيانات المستخدمين', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المستخدمين:', error);
                });
        }

        // دالة تحميل التصنيفات
        async function loadCategories() {
            console.log('جاري تحميل التصنيفات...');

            try {
                const response = await fetch('/api/categories');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.categories) {
                    console.log(`تم تحميل ${data.categories.length} تصنيف:`, data.categories);
                    displayCategories(data.categories);

                    // تحديث قائمة التصنيفات في نموذج إضافة الموقع
                    updateCategoriesDropdown(data.categories);
                } else {
                    console.error('خطأ في تحميل التصنيفات:', data.error);
                    showNotification('فشل في تحميل بيانات التصنيفات', 'error');
                }
            } catch (error) {
                console.error('خطأ في تحميل التصنيفات:', error);
                showNotification('حدث خطأ في الاتصال بالخادم', 'error');
            }
        }

        // تحديث قائمة التصنيفات في النماذج
        function updateCategoriesDropdown(categories) {
            const categorySelect = document.getElementById('newLocationCategory');
            if (categorySelect) {
                categorySelect.innerHTML = '<option value="">اختر التصنيف...</option>';
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name_ar;
                    categorySelect.appendChild(option);
                });
                console.log('تم تحديث قائمة التصنيفات في النموذج');
            }
        }

        // دالة تحميل المواقع
        function loadLocations() {
            console.log('جاري تحميل المواقع...');

            fetch('/api/admin/locations')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('فشل في تحميل المواقع');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('استجابة API المواقع:', data);
                    if (data.success && data.places) {
                        console.log(`تم تحميل ${data.places.length} موقع:`, data.places);
                        displayLocations(data.places);
                    } else {
                        console.error('خطأ في البيانات:', data.error);
                        showNotification('فشل في تحميل بيانات المواقع', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المواقع:', error);
                });
        }

        // دالة تحميل العملاء
        async function loadClients() {
            console.log('جاري تحميل العملاء...');

            try {
                const response = await fetch('/api/admin/clients');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.clients) {
                    console.log(`تم تحميل ${data.clients.length} عميل:`, data.clients);
                    displayClients(data.clients);
                } else {
                    console.error('خطأ في تحميل العملاء:', data.error);
                    showNotification('فشل في تحميل بيانات العملاء', 'error');
                }
            } catch (error) {
                console.error('خطأ في تحميل العملاء:', error);
                showNotification('حدث خطأ في الاتصال بالخادم', 'error');
            }
        }

        // دالة عرض المستخدمين
        function displayUsers(users) {
            console.log('عرض المستخدمين:', users.length, 'مستخدم');
            const tbody = document.querySelector('#usersTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول المستخدمين');
                return;
            }

            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.user_id}</td>
                    <td>${user.username}</td>
                    <td>${user.full_name || 'غير محدد'}</td>
                    <td>${user.email}</td>
                    <td>${user.phone || 'غير محدد'}</td>
                    <td><span class="badge ${user.is_active ? 'bg-success' : 'bg-danger'}">${user.is_active ? 'نشط' : 'غير نشط'}</span></td>
                    <td>${new Date(user.registration_date).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editUser(${user.user_id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.user_id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض المستخدمين في الجدول بنجاح');
        }

        // دالة عرض التصنيفات
        function displayCategories(categories) {
            console.log('عرض التصنيفات:', categories.length, 'تصنيف');
            const tbody = document.querySelector('#categoriesTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول التصنيفات');
                return;
            }

            tbody.innerHTML = '';

            categories.forEach(category => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${category.id}</td>
                    <td>${category.name}</td>
                    <td><i class="${category.icon}" style="color: ${category.color}"></i> ${category.icon}</td>
                    <td><span class="badge" style="background-color: ${category.color}">${category.color}</span></td>
                    <td>${new Date(category.created_at).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editCategory(${category.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCategory(${category.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض التصنيفات في الجدول بنجاح');
        }

        // دالة عرض المواقع
        function displayLocations(locations) {
            console.log('عرض المواقع:', locations.length, 'موقع');
            const tbody = document.querySelector('#locationsTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول المواقع');
                return;
            }

            tbody.innerHTML = '';

            locations.forEach(location => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${location.id}</td>
                    <td>${location.name}</td>
                    <td>${location.description || 'غير محدد'}</td>
                    <td>${location.category_name || 'غير محدد'}</td>
                    <td>${location.lat}, ${location.lng}</td>
                    <td><span class="badge ${location.status === 'active' ? 'bg-success' : 'bg-warning'}">${location.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editLocation(${location.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteLocation(${location.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض المواقع في الجدول بنجاح');
        }

        // دالة عرض العملاء
        function displayClients(clients) {
            console.log('عرض العملاء:', clients.length, 'عميل');
            const tbody = document.querySelector('#clientsTable');
            if (!tbody) {
                console.error('لم يتم العثور على جدول العملاء');
                return;
            }

            tbody.innerHTML = '';

            clients.forEach(client => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${client.id}</td>
                    <td>${client.name}</td>
                    <td>${client.email}</td>
                    <td>${client.phone}</td>
                    <td>${client.devicesn || 'غير محدد'}</td>
                    <td><span class="badge ${client.status === 'active' ? 'bg-success' : 'bg-warning'}">${client.status === 'active' ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-1" onclick="editClient(${client.id})">تعديل</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteClient(${client.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            console.log('تم عرض العملاء في الجدول بنجاح');
        }

        // إعداد زر تسجيل الخروج
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                console.log('تم النقر على زر تسجيل الخروج');
                // حذف جميع بيانات المستخدم من التخزين المحلي
                sessionStorage.clear();
                localStorage.clear();

                // إعادة تحميل الصفحة لإظهار نموذج تسجيل الدخول
                location.reload();
            });
            console.log('تم إعداد زر تسجيل الخروج');
        }

        // إعداد أحداث النقر على علامات التبويب
        const tabButtons = document.querySelectorAll('.nav-link');
        tabButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // إضافة الفئة النشطة للزر المنقور
                this.classList.add('active');
            });
        });

        // إعداد أحداث النماذج
        document.querySelectorAll('.btn-add').forEach(button => {
            button.addEventListener('click', function() {
                const targetModal = this.getAttribute('data-bs-target') || '#userModal';
                const modal = new bootstrap.Modal(document.querySelector(targetModal));
                modal.show();
            });
        });

        // منع إعادة التوجيه عند إعادة تحميل الصفحة
        window.onbeforeunload = function() {
            // تخزين حالة التبويب الحالية
            const activeTab = document.querySelector('.nav-link.active');
            if (activeTab) {
                sessionStorage.setItem('activeTab', activeTab.id);
            }
        };

        // استعادة حالة التبويب عند إعادة تحميل الصفحة
        const savedTab = sessionStorage.getItem('activeTab');
        if (savedTab) {
            const tabToActivate = document.getElementById(savedTab);
            if (tabToActivate) {
                tabToActivate.click();
            }
        }

        // وظائف التعديل والحذف للمستخدمين
        function editUser(userId) {
            console.log('تعديل المستخدم:', userId);
            // TODO: تنفيذ وظيفة التعديل
            alert('سيتم تنفيذ وظيفة تعديل المستخدم قريباً');
        }

        function deleteUser(userId) {
            console.log('حذف المستخدم:', userId);
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                // TODO: تنفيذ وظيفة الحذف
                alert('سيتم تنفيذ وظيفة حذف المستخدم قريباً');
            }
        }

        // وظائف التعديل والحذف للتصنيفات
        function editCategory(categoryId) {
            console.log('تعديل التصنيف:', categoryId);
            alert('سيتم تنفيذ وظيفة تعديل التصنيف قريباً');
        }

        function deleteCategory(categoryId) {
            console.log('حذف التصنيف:', categoryId);
            if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
                alert('سيتم تنفيذ وظيفة حذف التصنيف قريباً');
            }
        }

        // وظائف التعديل والحذف للمواقع
        function editLocation(locationId) {
            console.log('تعديل الموقع:', locationId);
            alert('سيتم تنفيذ وظيفة تعديل الموقع قريباً');
        }

        function deleteLocation(locationId) {
            console.log('حذف الموقع:', locationId);
            if (confirm('هل أنت متأكد من حذف هذا الموقع؟')) {
                alert('سيتم تنفيذ وظيفة حذف الموقع قريباً');
            }
        }

        // وظائف التعديل والحذف للعملاء
        function editClient(clientId) {
            console.log('تعديل العميل:', clientId);
            alert('سيتم تنفيذ وظيفة تعديل العميل قريباً');
        }

        function deleteClient(clientId) {
            console.log('حذف العميل:', clientId);
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                alert('سيتم تنفيذ وظيفة حذف العميل قريباً');
            }
        }

        // وظيفة البحث في الجداول
        function setupSearch() {
            // البحث في المستخدمين
            const usersSearch = document.getElementById('usersSearch');
            if (usersSearch) {
                usersSearch.addEventListener('input', function() {
                    filterTable('usersTable', this.value);
                });
            }

            // البحث في التصنيفات
            const categoriesSearch = document.getElementById('categoriesSearch');
            if (categoriesSearch) {
                categoriesSearch.addEventListener('input', function() {
                    filterTable('categoriesTable', this.value);
                });
            }

            // البحث في المواقع
            const locationsSearch = document.getElementById('locationsSearch');
            if (locationsSearch) {
                locationsSearch.addEventListener('input', function() {
                    filterTable('locationsTable', this.value);
                });
            }

            // البحث في العملاء
            const clientsSearch = document.getElementById('clientsSearch');
            if (clientsSearch) {
                clientsSearch.addEventListener('input', function() {
                    filterTable('clientsTable', this.value);
                });
            }
        }

        // دالة تصفية الجدول
        function filterTable(tableId, searchTerm) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const rows = table.getElementsByTagName('tr');
            searchTerm = searchTerm.toLowerCase();

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].textContent || cells[j].innerText;
                    if (cellText.toLowerCase().indexOf(searchTerm) > -1) {
                        found = true;
                        break;
                    }
                }

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        // تشغيل وظيفة البحث عند تحميل الصفحة
        setupSearch();

        // دوال تحديث الإحصائيات
        function updateUsersCount(count) {
            const element = document.getElementById('usersCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد المستخدمين:', count);
            }
        }

        function updateCategoriesCount(count) {
            const element = document.getElementById('categoriesCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد التصنيفات:', count);
            }
        }

        function updateLocationsCount(count) {
            const element = document.getElementById('locationsCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد المواقع:', count);
            }
        }

        function updateClientsCount(count) {
            const element = document.getElementById('clientsCount');
            if (element) {
                element.textContent = count;
                console.log('تم تحديث عدد العملاء:', count);
            }
        }

        // تم حذف الكود المكرر للخرائط - يتم استخدام التعريف الأول فقط

        // تم حذف جميع الدوال المكررة - سيتم استخدام admin.js فقط
    </script>

    <!-- Google Maps JavaScript API -->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0&libraries=places,visualization,drawing&v=weekly&callback=initMap" defer></script>

    <!-- Admin JavaScript -->
    <script src="/js/admin.js"></script>
</body>
</html>
