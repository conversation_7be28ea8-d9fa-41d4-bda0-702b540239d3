#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم خرائط اليمن - Python Flask Server
Yemen Maps Server - Python Flask Implementation
"""

from flask import Flask, jsonify, send_from_directory, request
from flask_cors import CORS
import psycopg2
import psycopg2.extras
import os
import logging

# إعداد التطبيق
app = Flask(__name__,
           static_folder='public',
           template_folder='templates')
CORS(app)

# إعداد قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin'
}

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def get_places_with_images():
    """جلب الأماكن مع الصور من قاعدة البيانات"""
    conn = get_db_connection()
    if not conn:
        # إرجاع بيانات افتراضية إذا فشل الاتصال بقاعدة البيانات
        return [
            {
                'id': 1,
                'name': 'ميدان السبعين',
                'description': 'ميدان السبعين هو أحد أشهر ميادين صنعاء',
                'latitude': 15.3136,
                'longitude': 44.1867,
                'address': 'صنعاء، اليمن',
                'city': 'صنعاء',
                'contact': '777123456',
                'email': None,
                'website': None,
                'category_id': 'landmark',
                'place_id': 'ChIJ5UfYV33FAxYRV_jxCFmR3Ic',
                'rating': 4.5,
                'images': ['/images/places/ChIJ5UfYV33FAxYRV_jxCFmR3Ic_0.jpg']
            },
            {
                'id': 2,
                'name': 'قلعة القاهرة',
                'description': 'قلعة تاريخية شهيرة تقع على قمة جبل صبر',
                'latitude': 13.5789,
                'longitude': 44.0209,
                'address': 'تعز، اليمن',
                'city': 'تعز',
                'contact': '777123457',
                'email': None,
                'website': None,
                'category_id': 'historical',
                'place_id': 'ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg',
                'rating': 4.8,
                'images': ['/images/places/ChIJ7YCGE1HfAxYRVKLZ_Gtxhgg_0.jpg']
            },
            {
                'id': 3,
                'name': 'مدينة سيئون',
                'description': 'مدينة تاريخية في وادي حضرموت',
                'latitude': 15.9431,
                'longitude': 48.7879,
                'address': 'حضرموت، اليمن',
                'city': 'حضرموت',
                'contact': '777123458',
                'email': None,
                'website': None,
                'category_id': 'city',
                'place_id': 'ChIJ4862o_PaAxYRaZqhQwhe_pQ',
                'rating': 4.3,
                'images': ['/images/places/ChIJ4862o_PaAxYRaZqhQwhe_pQ_0.jpg']
            }
        ]

    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # جلب الأماكن
        cursor.execute("""
            SELECT
                p.id,
                p.name_ar as name,
                p.description_ar as description,
                p.latitude,
                p.longitude,
                p.address_ar as address,
                g.name_ar as city,
                p.phone as contact,
                p.email,
                p.website,
                p.category_id,
                p.google_place_id as place_id,
                p.rating
            FROM places p
            LEFT JOIN governorates g ON p.governorate_id = g.id
            WHERE p.is_active = true
            ORDER BY p.name_ar
        """)

        places = cursor.fetchall()

        # إضافة الصور لكل مكان - استخدام الصور المحلية فقط
        for place in places:
            place['images'] = []

            # البحث في مجلد الصور المحلية
            if place['place_id']:
                images_dir = os.path.join('public', 'images', 'places')
                if os.path.exists(images_dir):
                    files = os.listdir(images_dir)
                    place_images = [f for f in files if f.startswith(place['place_id']) and f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
                    place_images.sort(key=lambda x: int(x.split('_')[1].split('.')[0]) if '_' in x and x.split('_')[1].split('.')[0].isdigit() else 0)
                    place['images'] = [f'/images/places/{img}' for img in place_images]

        return places

    except Exception as e:
        logger.error(f"خطأ في جلب الأماكن: {e}")
        return []
    finally:
        if conn:
            conn.close()

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return send_from_directory('templates', 'index.html')

@app.route('/admin')
def admin():
    """لوحة التحكم"""
    return send_from_directory('templates', 'admin.html')

@app.route('/old-admin')
def old_admin():
    """صفحة الإدارة القديمة - قاعدة البيانات"""
    return send_from_directory('templates', 'old_admin.html')

# API المواقع
@app.route('/api/locations')
def api_locations():
    """API جلب المواقع من قاعدة البيانات - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT
                        p.id,
                        p.name_ar as name,
                        p.description_ar as description,
                        p.latitude,
                        p.longitude,
                        p.address_ar as address,
                        p.phone as contact,
                        p.google_place_id as place_id,
                        p.rating,
                        p.reviews_count,
                        p.is_active,
                        c.name as category_name,
                        g.name_ar as governorate_name
                    FROM places p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN governorates g ON p.governorate_id = g.id
                    WHERE p.is_active = true
                    ORDER BY p.id ASC
                    LIMIT 100
                """)
                places = cursor.fetchall()
                conn.close()

                # تحويل البيانات للتوافق مع الواجهة الأمامية
                formatted_places = []
                for place in places:
                    formatted_place = {
                        'id': place['id'],
                        'name': place['name'],
                        'latitude': float(place['latitude']) if place['latitude'] else 0,
                        'longitude': float(place['longitude']) if place['longitude'] else 0,
                        'address': place['address'] or '',
                        'city': place['governorate_name'] or '',
                        'contact': place['contact'] or '',
                        'description': place['description'] or '',
                        'place_id': place['place_id'] or f"yemen_{place['id']}",
                        'category': place['category_name'] or '',
                        'rating': float(place['rating']) if place['rating'] else 0,
                        'reviews_count': place['reviews_count'] or 0,
                        'images': []  # سيتم إضافة الصور لاحقاً
                    }
                    formatted_places.append(formatted_place)

                return jsonify({
                    'success': True,
                    'locations': formatted_places,
                    'total': len(formatted_places)
                })

            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - المواقع: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API المواقع: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب المواقع'
        }), 500

# API الإحصائيات
@app.route('/api/stats')
def api_stats():
    """API جلب الإحصائيات"""
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM places WHERE is_active = true')
            total_places = cursor.fetchone()[0]
            conn.close()

            return jsonify({
                'success': True,
                'stats': {
                    'total_places': total_places,
                    'total_images': 787,
                    'total_governorates': 21
                }
            })
        else:
            return jsonify({
                'success': True,
                'stats': {
                    'total_places': 3,
                    'total_images': 787,
                    'total_governorates': 21
                }
            })
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {e}")
        return jsonify({
            'success': True,
            'stats': {
                'total_places': 3,
                'total_images': 787,
                'total_governorates': 21
            }
        })

# API تفاصيل المكان
@app.route('/api/place/<place_id>')
def api_place_details(place_id):
    """API جلب تفاصيل مكان محدد"""
    try:
        places = get_places_with_images()
        place = next((p for p in places if p['place_id'] == place_id), None)
        if place:
            return jsonify({
                'success': True,
                'place': place
            })
        else:
            return jsonify({
                'success': False,
                'error': 'المكان غير موجود'
            }), 404
    except Exception as e:
        logger.error(f"خطأ في API تفاصيل المكان: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب التفاصيل'
        }), 500

# API اختبار الاتصال
@app.route('/api/test-connection')
def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    conn = get_db_connection()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute('SELECT NOW() as current_time')
            result = cursor.fetchone()
            conn.close()
            return jsonify({
                'message': 'الاتصال بقاعدة البيانات يعمل بنجاح',
                'timestamp': str(result[0])
            })
        except Exception as e:
            return jsonify({'error': f'خطأ في قاعدة البيانات: {e}'}), 500
    else:
        return jsonify({'error': 'فشل الاتصال بقاعدة البيانات'}), 500

# API إحصائيات قاعدة البيانات
@app.route('/api/db-stats')
def api_db_stats():
    """API جلب إحصائيات قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()

            # إحصائيات افتراضية
            stats = {
                'users': 0,
                'customers': 0,
                'places': 0,
                'active_users': 0,
                'inactive_users': 0,
                'new_users_month': 0,
                'active_customers': 0,
                'new_customers_month': 0,
                'total_orders': 0
            }

            # عدد الأماكن
            cursor.execute('SELECT COUNT(*) FROM places WHERE is_active = true')
            stats['places'] = cursor.fetchone()[0]

            conn.close()

            return jsonify({
                'success': True,
                'stats': stats
            })
        else:
            return jsonify({
                'success': True,
                'stats': {
                    'users': 0,
                    'customers': 0,
                    'places': 0,
                    'active_users': 0,
                    'inactive_users': 0,
                    'new_users_month': 0,
                    'active_customers': 0,
                    'new_customers_month': 0,
                    'total_orders': 0
                }
            })
    except Exception as e:
        logger.error(f"خطأ في API إحصائيات قاعدة البيانات: {e}")
        return jsonify({
            'success': True,
            'stats': {
                'users': 0,
                'customers': 0,
                'places': 0,
                'active_users': 0,
                'inactive_users': 0,
                'new_users_month': 0,
                'active_customers': 0,
                'new_customers_month': 0,
                'total_orders': 0
            }
        })

# API المستخدمين
@app.route('/api/users')
def api_users():
    """API جلب المستخدمين"""
    try:
        # بيانات افتراضية للمستخدمين
        default_users = [
            {
                'id': 1,
                'name': 'أحمد محمد',
                'username': 'ahmed',
                'email': '<EMAIL>',
                'is_active': True,
                'created_at': '2024-01-15'
            },
            {
                'id': 2,
                'name': 'فاطمة علي',
                'username': 'fatima',
                'email': '<EMAIL>',
                'is_active': True,
                'created_at': '2024-02-10'
            },
            {
                'id': 3,
                'name': 'محمد حسن',
                'username': 'mohammed',
                'email': '<EMAIL>',
                'is_active': False,
                'created_at': '2024-01-20'
            }
        ]

        return jsonify({
            'success': True,
            'users': default_users
        })
    except Exception as e:
        logger.error(f"خطأ في API المستخدمين: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب المستخدمين'
        }), 500

# API العملاء
@app.route('/api/customers')
def api_customers():
    """API جلب العملاء"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT id, name, email, phone, status, created_at, updated_at
                    FROM clients
                    ORDER BY created_at DESC
                """)
                clients = cursor.fetchall()
                conn.close()

                return jsonify({
                    'success': True,
                    'customers': clients
                })
            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - العملاء: {db_error}")

        # بيانات افتراضية في حالة فشل قاعدة البيانات
        default_customers = [
            {
                'id': 1,
                'name': 'شركة الخليج للتجارة',
                'email': '<EMAIL>',
                'phone': '+967-1-234567',
                'status': 'active',
                'created_at': '2024-01-10'
            },
            {
                'id': 2,
                'name': 'مؤسسة النور للخدمات',
                'email': '<EMAIL>',
                'phone': '+967-2-345678',
                'status': 'active',
                'created_at': '2024-02-05'
            },
            {
                'id': 3,
                'name': 'شركة الأمل للتطوير',
                'email': '<EMAIL>',
                'phone': '+967-3-456789',
                'status': 'inactive',
                'created_at': '2024-01-25'
            }
        ]

        return jsonify({
            'success': True,
            'customers': default_customers
        })
    except Exception as e:
        logger.error(f"خطأ في API العملاء: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب العملاء'
        }), 500

# API المحافظات
@app.route('/api/governorates')
def api_governorates():
    """API جلب المحافظات من قاعدة البيانات - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT
                        g.id,
                        g.name_ar,
                        g.name_en,
                        g.code,
                        g.latitude,
                        g.longitude,
                        g.created_at,
                        COUNT(p.id) as actual_places_count,
                        CASE
                            WHEN COUNT(p.id) > 100 THEN 'complete'
                            WHEN COUNT(p.id) > 50 THEN 'partial'
                            WHEN COUNT(p.id) > 0 THEN 'in_progress'
                            ELSE 'pending'
                        END as data_status
                    FROM governorates g
                    LEFT JOIN places p ON g.id = p.governorate_id AND p.is_active = true
                    GROUP BY g.id, g.name_ar, g.name_en, g.code, g.latitude, g.longitude, g.created_at
                    ORDER BY g.name_ar
                """)
                governorates = cursor.fetchall()
                conn.close()

                # تحويل التواريخ إلى نص لضمان التوافق مع JSON
                for gov in governorates:
                    if gov['created_at']:
                        gov['created_at'] = gov['created_at'].isoformat()
                    if gov['latitude']:
                        gov['latitude'] = float(gov['latitude'])
                    if gov['longitude']:
                        gov['longitude'] = float(gov['longitude'])

                return jsonify({
                    'success': True,
                    'governorates': governorates
                })

            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - المحافظات: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API المحافظات: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب المحافظات'
        }), 500

# API التصنيفات
@app.route('/api/categories')
def api_categories():
    """API جلب التصنيفات من قاعدة البيانات - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT id, name, icon, color, parent_id, created_at
                    FROM categories
                    ORDER BY id ASC
                """)
                categories = cursor.fetchall()
                conn.close()

                # تحويل التواريخ إلى نص لضمان التوافق مع JSON
                for category in categories:
                    if category['created_at']:
                        category['created_at'] = category['created_at'].isoformat()

                return jsonify({
                    'success': True,
                    'categories': categories
                })

            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - التصنيفات: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API التصنيفات: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب التصنيفات'
        }), 500

# API الخرائط
@app.route('/api/maps')
def api_maps():
    """API جلب بيانات الخرائط من قاعدة البيانات - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT
                        p.id,
                        p.name_ar as name,
                        p.description_ar as description,
                        p.latitude,
                        p.longitude,
                        p.address_ar as address,
                        p.phone,
                        p.rating,
                        p.reviews_count,
                        c.name as category_name,
                        c.icon as category_icon,
                        c.color as category_color,
                        g.name_ar as governorate_name
                    FROM places p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN governorates g ON p.governorate_id = g.id
                    WHERE p.is_active = true
                    AND p.latitude IS NOT NULL
                    AND p.longitude IS NOT NULL
                    ORDER BY p.rating DESC NULLS LAST
                    LIMIT 500
                """)
                places = cursor.fetchall()
                conn.close()

                # تحويل البيانات للتوافق مع خرائط الويب
                map_data = []
                for place in places:
                    map_point = {
                        'id': place['id'],
                        'name': place['name'],
                        'description': place['description'] or '',
                        'lat': float(place['latitude']),
                        'lng': float(place['longitude']),
                        'address': place['address'] or '',
                        'phone': place['phone'] or '',
                        'rating': float(place['rating']) if place['rating'] else 0,
                        'reviews_count': place['reviews_count'] or 0,
                        'category': {
                            'name': place['category_name'] or 'غير محدد',
                            'icon': place['category_icon'] or 'place',
                            'color': place['category_color'] or '#666666'
                        },
                        'governorate': place['governorate_name'] or 'غير محدد'
                    }
                    map_data.append(map_point)

                return jsonify({
                    'success': True,
                    'places': map_data,
                    'total': len(map_data)
                })

            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - الخرائط: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API الخرائط: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب بيانات الخرائط'
        }), 500

# API جلب المواقع للإدارة
@app.route('/api/admin/locations', methods=['GET'])
def get_admin_locations():
    """جلب جميع المواقع للإدارة"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT p.*, c.name_ar as category_name, g.name_ar as governorate_name
                    FROM places p
                    LEFT JOIN categories c ON p.category_id = c.id
                    LEFT JOIN governorates g ON p.governorate_id = g.id
                    ORDER BY p.created_at DESC
                """)

                places = []
                for row in cursor.fetchall():
                    place = {
                        'id': row[0],
                        'name_ar': row[1],
                        'name_en': row[2],
                        'description_ar': row[3],
                        'description_en': row[4],
                        'latitude': float(row[5]) if row[5] else None,
                        'longitude': float(row[6]) if row[6] else None,
                        'address_ar': row[7],
                        'address_en': row[8],
                        'phone': row[9],
                        'category_id': row[10],
                        'governorate_id': row[11],
                        'is_active': row[12],
                        'created_at': row[13].isoformat() if row[13] else None,
                        'updated_at': row[14].isoformat() if row[14] else None,
                        'category_name': row[15],
                        'governorate_name': row[16]
                    }
                    places.append(place)

                conn.close()

                return jsonify({
                    'success': True,
                    'places': places,
                    'total': len(places)
                })

            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - جلب المواقع: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في جلب المواقع: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب المواقع'
        }), 500

# API إضافة موقع جديد
@app.route('/api/admin/places', methods=['POST'])
def add_place():
    """إضافة موقع جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['name_ar', 'latitude', 'longitude', 'category_id']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'error': f'الحقل {field} مطلوب'
                }), 400

        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO places (
                        name_ar, name_en, description_ar, description_en,
                        latitude, longitude, address_ar, address_en,
                        phone, category_id, governorate_id, is_active,
                        created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
                    ) RETURNING id
                """, (
                    data['name_ar'],
                    data.get('name_en', data['name_ar']),
                    data.get('description_ar', ''),
                    data.get('description_en', ''),
                    float(data['latitude']),
                    float(data['longitude']),
                    data.get('address_ar', ''),
                    data.get('address_en', ''),
                    data.get('phone', ''),
                    int(data['category_id']),
                    data.get('governorate_id', 1),
                    data.get('is_active', True)
                ))

                place_id = cursor.fetchone()[0]
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'message': 'تم إضافة الموقع بنجاح',
                    'place_id': place_id
                })

            except Exception as db_error:
                conn.rollback()
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - إضافة موقع: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في إضافة موقع: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في إضافة الموقع'
        }), 500

# API حذف موقع
@app.route('/api/admin/places/<int:place_id>', methods=['DELETE'])
def delete_place(place_id):
    """حذف موقع"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()

                # التحقق من وجود الموقع
                cursor.execute("SELECT id FROM places WHERE id = %s", (place_id,))
                if not cursor.fetchone():
                    conn.close()
                    return jsonify({
                        'success': False,
                        'error': 'الموقع غير موجود'
                    }), 404

                # حذف الموقع
                cursor.execute("DELETE FROM places WHERE id = %s", (place_id,))
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'message': 'تم حذف الموقع بنجاح'
                })

            except Exception as db_error:
                conn.rollback()
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - حذف موقع: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في حذف موقع: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في حذف الموقع'
        }), 500

# API إضافة محافظة جديدة
@app.route('/api/admin/governorates', methods=['POST'])
def add_governorate():
    """إضافة محافظة جديدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['name_ar', 'name_en']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'error': f'الحقل {field} مطلوب'
                }), 400

        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO governorates (
                        name_ar, name_en, code, population,
                        latitude, longitude, description, created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, NOW()
                    ) RETURNING id
                """, (
                    data['name_ar'],
                    data['name_en'],
                    data.get('code'),
                    data.get('population'),
                    data.get('latitude'),
                    data.get('longitude'),
                    data.get('description', '')
                ))

                governorate_id = cursor.fetchone()[0]
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'message': 'تم إضافة المحافظة بنجاح',
                    'governorate_id': governorate_id
                })

            except Exception as db_error:
                conn.rollback()
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - إضافة محافظة: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في إضافة محافظة: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في إضافة المحافظة'
        }), 500

# API حذف محافظة
@app.route('/api/admin/governorates/<int:governorate_id>', methods=['DELETE'])
def delete_governorate(governorate_id):
    """حذف محافظة"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor()

                # التحقق من وجود المحافظة
                cursor.execute("SELECT id FROM governorates WHERE id = %s", (governorate_id,))
                if not cursor.fetchone():
                    conn.close()
                    return jsonify({
                        'success': False,
                        'error': 'المحافظة غير موجودة'
                    }), 404

                # حذف المحافظة (سيتم حذف المواقع المرتبطة تلقائياً إذا كان هناك CASCADE)
                cursor.execute("DELETE FROM governorates WHERE id = %s", (governorate_id,))
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'message': 'تم حذف المحافظة بنجاح'
                })

            except Exception as db_error:
                conn.rollback()
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - حذف محافظة: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في حذف محافظة: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في حذف المحافظة'
        }), 500

# ===== APIs للمصادقة وإدارة المستخدمين =====

@app.route('/api/login', methods=['POST'])
def api_login():
    """API تسجيل الدخول"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({
                'success': False,
                'error': 'اسم المستخدم وكلمة المرور مطلوبان'
            }), 400

        # التحقق من قاعدة البيانات
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

                # البحث عن المستخدم
                cursor.execute("""
                    SELECT id, username, email, full_name, password, role, is_active, created_at
                    FROM users
                    WHERE (username = %s OR email = %s) AND is_active = true
                """, (username, username))

                user = cursor.fetchone()
                conn.close()

                if user and user['password'] == password:
                    # إنشاء session token بسيط
                    import time
                    session_token = f"token_{user['id']}_{int(time.time())}"

                    return jsonify({
                        'success': True,
                        'user': {
                            'id': user['id'],
                            'username': user['username'],
                            'email': user['email'],
                            'full_name': user['full_name'],
                            'role': user['role']
                        },
                        'token': session_token
                    })

            except Exception as db_error:
                logger.error(f"خطأ في قاعدة البيانات: {db_error}")
                conn.close()

        # إذا فشلت قاعدة البيانات، استخدم البيانات الافتراضية
        if username == 'admin' and password == 'yemen123':
            return jsonify({
                'success': True,
                'user': {
                    'id': 1,
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'full_name': 'مدير النظام',
                    'role': 'admin'
                },
                'token': 'token_admin_123'
            })

        # إذا لم تنجح أي طريقة
        return jsonify({
            'success': False,
            'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'
        }), 401

    except Exception as e:
        logger.error(f"خطأ في API تسجيل الدخول: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في الخادم'
        }), 500

@app.route('/api/admin/users')
def api_admin_users():
    """API جلب المستخدمين للوحة التحكم - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT id, username, email, full_name, phone, type as role, is_active, registration_date as created_at, last_login
                    FROM users
                    ORDER BY id ASC
                """)
                users = cursor.fetchall()
                conn.close()

                # تحويل التواريخ إلى نص لضمان التوافق مع JSON
                for user in users:
                    if user['created_at']:
                        user['created_at'] = user['created_at'].isoformat()
                    if user['last_login']:
                        user['last_login'] = user['last_login'].isoformat()

                return jsonify({
                    'success': True,
                    'users': users
                })
            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - المستخدمين: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API المستخدمين: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب المستخدمين'
        }), 500

@app.route('/api/admin/stats')
def api_admin_stats():
    """API إحصائيات لوحة التحكم"""
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()

            # إحصائيات من قاعدة البيانات
            stats = {}

            # عدد المستخدمين
            cursor.execute('SELECT COUNT(*) FROM users')
            stats['users'] = cursor.fetchone()[0]

            # المستخدمين النشطين وغير النشطين
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = true')
            stats['active_users'] = cursor.fetchone()[0]
            stats['inactive_users'] = stats['users'] - stats['active_users']

            # عدد العملاء
            cursor.execute('SELECT COUNT(*) FROM clients')
            stats['clients'] = cursor.fetchone()[0]

            # عدد الأماكن
            cursor.execute('SELECT COUNT(*) FROM places WHERE is_active = true')
            stats['locations'] = cursor.fetchone()[0]

            # عدد المحافظات
            cursor.execute('SELECT COUNT(*) FROM governorates')
            stats['governorates'] = cursor.fetchone()[0]

            # عدد التصنيفات
            cursor.execute('SELECT COUNT(*) FROM categories')
            stats['categories'] = cursor.fetchone()[0]

            conn.close()

            return jsonify({
                'success': True,
                'stats': stats
            })
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب الإحصائيات'
        }), 500

@app.route('/api/admin/clients')
def api_admin_clients():
    """API جلب العملاء للوحة التحكم - بيانات حقيقية فقط"""
    try:
        conn = get_db_connection()
        if conn:
            try:
                cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cursor.execute("""
                    SELECT id, name, email, phone, status, created_at, updated_at
                    FROM clients
                    ORDER BY id ASC
                """)
                clients = cursor.fetchall()
                conn.close()

                # تحويل التواريخ إلى نص لضمان التوافق مع JSON
                for client in clients:
                    if client['created_at']:
                        client['created_at'] = client['created_at'].isoformat()
                    if client['updated_at']:
                        client['updated_at'] = client['updated_at'].isoformat()

                return jsonify({
                    'success': True,
                    'clients': clients
                })
            except Exception as db_error:
                conn.close()
                logger.error(f"خطأ في قاعدة البيانات - العملاء: {db_error}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في قاعدة البيانات: {str(db_error)}'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'لا يمكن الاتصال بقاعدة البيانات'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في API العملاء: {e}")
        return jsonify({
            'success': False,
            'error': 'حدث خطأ في جلب العملاء'
        }), 500

# خدمة الملفات الثابتة
@app.route('/favicon.ico')
def favicon():
    """خدمة أيقونة الموقع ICO"""
    return send_from_directory('public', 'favicon.svg', mimetype='image/svg+xml')

@app.route('/favicon.svg')
def favicon_svg():
    """خدمة أيقونة الموقع SVG"""
    return send_from_directory('public', 'favicon.svg', mimetype='image/svg+xml')

@app.route('/images/<path:filename>')
def serve_images(filename):
    """خدمة الصور"""
    return send_from_directory('public/images', filename)

@app.route('/img/<path:filename>')
def serve_img(filename):
    """خدمة الصور من مجلد img"""
    return send_from_directory('public/img', filename)

@app.route('/css/<path:filename>')
def serve_css(filename):
    """خدمة ملفات CSS"""
    return send_from_directory('public/css', filename)

@app.route('/js/<path:filename>')
def serve_js(filename):
    """خدمة ملفات JavaScript"""
    return send_from_directory('public/js', filename)

@app.route('/fonts/<path:filename>')
def serve_fonts(filename):
    """خدمة ملفات الخطوط"""
    return send_from_directory('templates/fonts', filename)

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم خرائط اليمن...")

    # اختبار الاتصال بقاعدة البيانات
    conn = get_db_connection()
    if conn:
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        conn.close()
    else:
        print("❌ فشل الاتصال بقاعدة البيانات")

    # الحصول على IP المحلي
    import socket
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
    except:
        local_ip = "*************"  # IP افتراضي

    # التحقق من وجود شهادات SSL
    cert_file = 'ssl/yemenmaps_com/yemenmaps_com.crt'
    key_file = 'ssl/yemenmaps.com.key'

    # تفعيل HTTPS
    use_https = True  # تغيير إلى False لإيقاف HTTPS

    if use_https and os.path.exists(cert_file) and os.path.exists(key_file):
        # تشغيل مع HTTPS
        print("🔒 تم تفعيل HTTPS بنجاح!")
        print("🔒 الخادم متاح على: https://localhost:5000")
        print(f"🔒 الخادم متاح على: https://{local_ip}:5000")
        print("🌐 الخادم متاح من الإنترنت: https://yemenmaps.com:8443")
        print("📊 لوحة التحكم: https://localhost:5000/admin")
        print("✅ تحديد الموقع سيعمل من جميع الأجهزة!")
        print("📱 للوصول من خارج السيرفر:")
        print("   https://yemenmaps.com:8443")

        import ssl
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)

        app.run(host='0.0.0.0', port=5000, debug=True, ssl_context=context)
    else:
        # تشغيل مع HTTP العادي
        print("⚠️  تشغيل بدون HTTPS - تحديد الموقع قد لا يعمل من خارج localhost")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print(f"🌐 الخادم متاح على: http://{local_ip}:5000")
        print("🌐 الخادم متاح من الإنترنت: http://***********:5000")
        print("📊 لوحة التحكم: http://localhost:5000/admin")
        print("💡 لتفعيل HTTPS، شغل: setup_ssl.bat")
        print("🔧 أو ضع شهادات SSL في مجلد ssl/")

        app.run(host='0.0.0.0', port=5000, debug=True)
