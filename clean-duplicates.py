#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف التكرارات في ملف index.html
"""

import re

def clean_duplicates():
    print("🧹 تنظيف التكرارات في الملف...")
    
    # قراءة الملف
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إزالة دوال toggleLayersPanel المكررة
    content = re.sub(r'\s*function toggleLayersPanel\(\) \{[^}]+\}\s*', '', content, flags=re.MULTILINE)
    
    # إزالة CSS المكرر للمميزات ثلاثية الأبعاد
    content = re.sub(r'\s*/\* مميزات ثلاثية الأبعاد \*/.*?(?=/\*|$)', '', content, flags=re.DOTALL)
    
    # إزالة JavaScript المكرر للمميزات ثلاثية الأبعاد
    content = re.sub(r'\s*// ==================== مميزات ثلاثية الأبعاد ====================.*?(?=// ====================|$)', '', content, flags=re.DOTALL)
    
    # إزالة الأسطر الفارغة المتتالية
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
    
    # حفظ الملف المنظف
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم تنظيف الملف من التكرارات")
    print("🗑️ تم إزالة:")
    print("   - دوال toggleLayersPanel المكررة")
    print("   - CSS المكرر")
    print("   - JavaScript المكرر")
    print("   - الأسطر الفارغة الزائدة")

if __name__ == "__main__":
    clean_duplicates()
