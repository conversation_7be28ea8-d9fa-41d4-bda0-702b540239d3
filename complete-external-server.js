#!/usr/bin/env node
/**
 * 🗺️ خادم خرائط اليمن الخارجي الكامل - HTTPS
 * Complete Yemen Maps External Server - HTTPS
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');

const config = {
    port: 8443,
    host: '0.0.0.0',
    domain: 'yemenmaps.com',
    ssl: {
        key: "D:/yemen-maps/ssl/yemenmaps.com.key",
        cert: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.crt",
        ca: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.ca-bundle"
    },
    templatesPath: "D:/yemen-maps/templates",
    staticPaths: {
        "/templates": "D:/yemen-maps/templates",
        "/tiles": "D:/yemen-maps/templates/yemen",
        "/fonts": "D:/yemen-maps/templates/fonts",
        "/js": "D:/yemen-maps/js",
        "/public": "D:/yemen-maps/public",
        "/static": "D:/yemen-maps/static"
    }
};

let stats = {
    total: 0,
    external: 0,
    startTime: new Date()
};

// دالة تسجيل الطلبات
function logRequest(req, res) {
    const timestamp = new Date().toISOString();
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);
    
    const isExternal = !clientIP || (!clientIP.includes('127.0.0.1') && !clientIP.includes('::1') && !clientIP.includes('localhost'));
    
    stats.total++;
    if (isExternal) {
        stats.external++;
    }
    
    const logColor = isExternal ? '\x1b[91m' : '\x1b[92m'; // أحمر للخارجي، أخضر للمحلي
    const resetColor = '\x1b[0m';
    const flag = isExternal ? '🌍🔥' : '🏠';
    
    console.log(`${logColor}${flag} [${timestamp}] ${req.method} ${req.url} - IP: ${clientIP} ${isExternal ? '(خارجي)' : '(محلي)'}${resetColor}`);
}

// دالة تحديد نوع المحتوى
function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html; charset=utf-8',
        '.js': 'application/javascript; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject'
    };
    return mimeTypes[ext] || 'application/octet-stream';
}

// دالة معالجة الطلبات
function handleRequest(req, res) {
    logRequest(req, res);
    
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // إعداد CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // الصفحة الرئيسية - توجيه إلى index.html
    if (pathname === '/') {
        const indexPath = path.join(config.templatesPath, 'index.html');
        if (fs.existsSync(indexPath)) {
            try {
                const content = fs.readFileSync(indexPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة index.html:', error.message);
            }
        }
    }
    
    // صفحة الإدارة
    if (pathname === '/admin') {
        const adminPath = path.join(config.templatesPath, 'admin.html');
        if (fs.existsSync(adminPath)) {
            try {
                const content = fs.readFileSync(adminPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة admin.html:', error.message);
            }
        }
    }
    
    // API للحالة
    if (pathname === '/api/status') {
        const status = {
            server: "Yemen Maps HTTPS Server",
            status: "running",
            port: config.port,
            domain: config.domain,
            ssl: true,
            stats: {
                total_requests: stats.total,
                external_requests: stats.external,
                uptime_seconds: Math.floor((Date.now() - stats.startTime.getTime()) / 1000)
            },
            timestamp: new Date().toISOString()
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify(status, null, 2));
        return;
    }
    
    // API للمواقع (توجيه للخادم المحلي)
    if (pathname.startsWith('/api/')) {
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
            message: "API متاح عبر الخادم المحلي على المنفذ 5000",
            local_api: `http://localhost:5000${pathname}`,
            note: "يرجى استخدام الخادم المحلي للوصول إلى قاعدة البيانات"
        }));
        return;
    }
    
    // معالجة الملفات الثابتة
    let filePath = null;
    
    // البحث في المسارات المختلفة
    for (const [route, directory] of Object.entries(config.staticPaths)) {
        if (pathname.startsWith(route)) {
            const relativePath = pathname.substring(route.length);
            filePath = path.join(directory, relativePath);
            break;
        }
    }
    
    // إذا لم نجد في المسارات الثابتة، جرب في templates
    if (!filePath) {
        filePath = path.join(config.templatesPath, pathname);
    }
    
    // التحقق من وجود الملف
    if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
        try {
            const content = fs.readFileSync(filePath);
            const contentType = getContentType(filePath);
            
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content);
        } catch (error) {
            console.log(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
            res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
            res.end('خطأ في الخادم');
        }
    } else {
        // 404 - الملف غير موجود
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>404 - الصفحة غير موجودة</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
        h1 { color: #e74c3c; }
        a { color: #3498db; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚫 404 - الصفحة غير موجودة</h1>
        <p>المسار المطلوب غير متوفر: <code>${pathname}</code></p>
        <p><a href="/">🏠 العودة للصفحة الرئيسية</a></p>
        <p><a href="/api/status">📊 حالة الخادم</a></p>
    </div>
</body>
</html>
        `);
    }
}

// إنشاء خادم HTTPS
console.log('🗺️ بدء خادم خرائط اليمن الخارجي الكامل - HTTPS');
console.log('=====================================');

try {
    console.log('🔍 فحص شهادات SSL...');

    if (!fs.existsSync(config.ssl.key)) {
        throw new Error('المفتاح الخاص غير موجود: ' + config.ssl.key);
    }

    if (!fs.existsSync(config.ssl.cert)) {
        throw new Error('الشهادة غير موجودة: ' + config.ssl.cert);
    }

    console.log('✅ المفتاح الخاص موجود');
    console.log('✅ الشهادة موجودة');

    const sslOptions = {
        key: fs.readFileSync(config.ssl.key),
        cert: fs.readFileSync(config.ssl.cert)
    };

    // إضافة CA bundle إذا كان موجوداً
    if (fs.existsSync(config.ssl.ca)) {
        console.log('✅ إضافة CA bundle');
        sslOptions.ca = fs.readFileSync(config.ssl.ca);
    }

    const httpsServer = https.createServer(sslOptions, handleRequest);
    
    httpsServer.listen(config.port, config.host, () => {
        console.log('🔒 خادم HTTPS يعمل بنجاح على:');
        console.log(`   الدومين: https://${config.domain}:${config.port}`);
        console.log(`   المحلي: https://localhost:${config.port}`);
        console.log('');
        console.log('🎯 جرب الآن: https://yemenmaps.com:8443');
        console.log('');
        console.log('📁 المسارات المتاحة:');
        console.log('   / - الخريطة الرئيسية');
        console.log('   /admin - لوحة التحكم');
        console.log('   /api/status - حالة الخادم');
        console.log('   /templates/* - ملفات القوالب');
        console.log('   /tiles/* - خرائط اليمن المحلية');
        console.log('   /fonts/* - الخطوط');
        console.log('');
        console.log('🔍 مراقبة الطلبات نشطة...');
        console.log('🚨 سيتم تمييز الطلبات الخارجية بوضوح!');
        console.log('=====================================');
    });
    
    httpsServer.on('error', (err) => {
        console.log('❌ خطأ في خادم HTTPS:', err.message);
    });
    
} catch (error) {
    console.log('❌ خطأ في إنشاء خادم HTTPS:', error.message);
    process.exit(1);
}
