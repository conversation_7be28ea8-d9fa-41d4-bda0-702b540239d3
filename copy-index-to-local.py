#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ ملف index.html الأصلي وتعديله للعمل محلياً
"""

import re

def create_local_version():
    print("🔄 نسخ وتعديل index.html للعمل محلياً...")
    
    # قراءة الملف الأصلي
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التعديلات المطلوبة للنسخة المحلية
    modifications = [
        # تغيير العنوان
        (r'<title>🇾🇪 خرائط اليمن - الواجهة المحسنة</title>', 
         '<title>🇾🇪 خرائط اليمن - النسخة المحلية المتميزة</title>'),
        
        # إضافة شريط حالة محلي
        (r'<body>', '''<body>
    <!-- شريط الحالة المحلي -->
    <div style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 8px 20px; text-align: center; font-size: 14px; font-weight: 600; z-index: 1002; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <span style="animation: pulse 2s infinite;">🔒</span>
        النسخة المحلية المتميزة - يعمل بدون إنترنت | 
        <span id="local-places-count">جاري التحميل...</span> موقع متاح |
        <span id="local-server-status" style="color: #fff;">متصل</span>
    </div>
    <style>
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.7; } 100% { opacity: 1; } }
        body { padding-top: 40px !important; }
        #map { height: calc(100vh - 40px) !important; }
    </style>'''),
        
        # تعديل مصدر البلاطات لاستخدام المحلية مع احتياطي أونلاين
        (r"L\.tileLayer\('https://[^']+'\s*,\s*{[^}]+}\)\.addTo\(map\);", 
         '''// طبقة البلاطات المحلية مع احتياطي أونلاين
            const localTileLayer = L.tileLayer('/tiles/yemen/{z}/{x}/{y}.png', {
                attribution: '© خرائط اليمن المحلية',
                maxZoom: 16,
                minZoom: 6
            });
            
            const onlineTileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 6,
                subdomains: ['a', 'b', 'c']
            });
            
            // محاولة استخدام البلاطات المحلية أولاً
            localTileLayer.addTo(map);
            
            // التبديل للأونلاين في حالة فشل المحلية
            let tileErrorCount = 0;
            localTileLayer.on('tileerror', function(e) {
                tileErrorCount++;
                if (tileErrorCount > 5) {
                    console.log('🌐 التبديل للبلاطات الأونلاين بسبب فشل المحلية');
                    map.removeLayer(localTileLayer);
                    onlineTileLayer.addTo(map);
                }
            });'''),
        
        # تحديث عداد الأماكن في الشريط المحلي
        (r"updateStats\(\);", '''updateStats();
            // تحديث الشريط المحلي
            if (document.getElementById('local-places-count')) {
                document.getElementById('local-places-count').textContent = 
                    allPlaces.length.toLocaleString('ar') + ' مكان';
            }'''),
        
        # إضافة فحص حالة الخادم المحلي
        (r"// تحديث الإحصائيات", '''// فحص حالة الخادم المحلي
        async function checkLocalServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'متصل';
                    document.getElementById('local-server-status').style.color = '#fff';
                }
            } catch (error) {
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'غير متصل';
                    document.getElementById('local-server-status').style.color = '#ffeb3b';
                }
            }
        }
        
        // فحص دوري لحالة الخادم
        setInterval(checkLocalServerStatus, 30000);
        
        // تحديث الإحصائيات'''),
    ]
    
    # تطبيق التعديلات
    for pattern, replacement in modifications:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # حفظ النسخة المحلية
    with open('templates/index-premium-local.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إنشاء templates/index-premium-local.html بنجاح!")
    print("🌐 الرابط: http://localhost:5000/index-premium-local.html")

if __name__ == "__main__":
    create_local_version()
