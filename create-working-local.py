#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء نسخة محلية تعمل بضمان مع البلاطات الأونلاين
"""

import re

def create_working_local():
    print("🔄 إنشاء نسخة محلية تعمل بضمان...")
    
    # قراءة الملف الأصلي
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التعديلات للنسخة العاملة
    modifications = [
        # تغيير العنوان
        (r'<title>🇾🇪 خرائط اليمن - الواجهة المحسنة</title>', 
         '<title>🇾🇪 خرائط اليمن - النسخة المحلية العاملة</title>'),
        
        # إضافة شريط حالة محلي
        (r'<body>', '''<body>
    <!-- شريط الحالة المحلي -->
    <div style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, #2196F3, #21CBF3); color: white; padding: 8px 20px; text-align: center; font-size: 14px; font-weight: 600; z-index: 1002; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <span style="animation: pulse 2s infinite;">🌐</span>
        النسخة المحلية العاملة - بيانات محلية + خرائط أونلاين | 
        <span id="working-places-count">جاري التحميل...</span> موقع متاح |
        <span id="working-server-status" style="color: #fff;">متصل</span>
    </div>
    <style>
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.7; } 100% { opacity: 1; } }
        body { padding-top: 40px !important; }
        #map { height: calc(100vh - 40px) !important; }
    </style>'''),
        
        # استخدام البلاطات الأونلاين مباشرة
        (r"L\.tileLayer\('https://[^']+'\s*,\s*{[^}]+}\)\.addTo\(map\);", 
         '''// استخدام البلاطات الأونلاين مباشرة لضمان العمل
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors | البيانات محلية',
                maxZoom: 18,
                minZoom: 6,
                subdomains: ['a', 'b', 'c']
            }).addTo(map);'''),
        
        # تحديث عداد الأماكن
        (r"updateStats\(\);", '''updateStats();
            // تحديث الشريط المحلي
            if (document.getElementById('working-places-count')) {
                document.getElementById('working-places-count').textContent = 
                    allPlaces.length.toLocaleString('ar') + ' مكان';
            }'''),
        
        # إضافة فحص حالة الخادم
        (r"// تحديث الإحصائيات", '''// فحص حالة الخادم المحلي
        async function checkWorkingServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (document.getElementById('working-server-status')) {
                    document.getElementById('working-server-status').textContent = 'متصل';
                    document.getElementById('working-server-status').style.color = '#fff';
                }
            } catch (error) {
                if (document.getElementById('working-server-status')) {
                    document.getElementById('working-server-status').textContent = 'غير متصل';
                    document.getElementById('working-server-status').style.color = '#ffeb3b';
                }
            }
        }
        
        // فحص دوري لحالة الخادم
        setInterval(checkWorkingServerStatus, 30000);
        
        // تحديث الإحصائيات'''),
        
        # تعديل رسائل التحميل
        (r'جاري تحميل الأماكن', 'جاري تحميل البيانات المحلية'),
        (r'تم تحميل الأماكن', 'تم تحميل البيانات المحلية'),
    ]
    
    # تطبيق التعديلات
    for pattern, replacement in modifications:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # حفظ النسخة العاملة
    with open('templates/index-working-local.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إنشاء templates/index-working-local.html بنجاح!")
    print("🌐 الرابط: http://localhost:5000/index-working-local.html")
    print("📝 هذه النسخة تستخدم:")
    print("   - البيانات المحلية (7445+ مكان)")
    print("   - البلاطات الأونلاين (لضمان العمل)")
    print("   - جميع المميزات الأصلية")

if __name__ == "__main__":
    create_working_local()
