-- يمن ناف - <PERSON>ل<PERSON> SQL لإضافة مستخدم مدير مع صلاحيات كاملة

-- إضافة مستخدم مدير جديد (كلمة المرور: yemen123)
INSERT INTO users (username, email, password, full_name, role_id, is_active, is_verified)
VALUES (
    'admin',
    '<EMAIL>',
    '$2b$10$X7tPj4cjLsVUoA/2o8yw3.Qi.Ym5OJbR8vQzPYxbniQTgPOxRVSJi', -- كلمة المرور المشفرة: yemen123
    'مدير النظام',
    1, -- دور المدير
    TRUE, -- نشط
    TRUE -- تم التحقق
)
ON CONFLICT (username) 
DO UPDATE SET 
    role_id = 1,
    is_active = TRUE,
    is_verified = TRUE;

-- التأكد من وجود دور المدير
INSERT INTO roles (id, name, description)
VALUES (
    1,
    'admin',
    'مدير النظام'
)
ON CONFLICT (id) DO NOTHING;

-- التأكد من وجود جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول ربط الأدوار بالصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

-- إضافة الصلاحيات الأساسية إذا لم تكن موجودة
INSERT INTO permissions (name, code, description) VALUES
    ('إدارة المستخدمين', 'manage_users', 'إنشاء وتعديل وحذف المستخدمين'),
    ('إدارة العملاء', 'manage_clients', 'إنشاء وتعديل وحذف العملاء'),
    ('إدارة المواقع', 'manage_locations', 'إنشاء وتعديل وحذف المواقع'),
    ('إدارة الإعدادات', 'manage_settings', 'تعديل إعدادات النظام'),
    ('عرض لوحة التحكم', 'view_dashboard', 'الوصول إلى لوحة التحكم'),
    ('إدارة النسخ الاحتياطي', 'manage_backups', 'إنشاء واستعادة النسخ الاحتياطي'),
    ('إدارة التصنيفات', 'manage_categories', 'إنشاء وتعديل وحذف التصنيفات'),
    ('إدارة المسارات', 'manage_routes', 'إنشاء وتعديل وحذف المسارات'),
    ('إدارة التقارير', 'manage_reports', 'إنشاء وعرض التقارير'),
    ('إدارة الأدوار', 'manage_roles', 'إنشاء وتعديل وحذف الأدوار والصلاحيات')
ON CONFLICT (code) DO NOTHING;

-- منح جميع الصلاحيات لدور المدير
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions
ON CONFLICT DO NOTHING;

-- إضافة صلاحية خاصة للمستخدم admin للوصول إلى لوحة التحكم
-- التأكد من وجود جدول صلاحيات المستخدمين
CREATE TABLE IF NOT EXISTS user_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    is_granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, permission_id)
);

-- إضافة صلاحية الوصول إلى لوحة التحكم للمستخدم admin
INSERT INTO user_permissions (user_id, permission_id, is_granted)
SELECT 
    (SELECT id FROM users WHERE username = 'admin'), 
    (SELECT id FROM permissions WHERE code = 'view_dashboard'),
    TRUE
ON CONFLICT (user_id, permission_id) 
DO UPDATE SET is_granted = TRUE;

-- إضافة جميع الصلاحيات للمستخدم admin
INSERT INTO user_permissions (user_id, permission_id, is_granted)
SELECT 
    (SELECT id FROM users WHERE username = 'admin'), 
    id,
    TRUE
FROM permissions
ON CONFLICT (user_id, permission_id) 
DO UPDATE SET is_granted = TRUE;
