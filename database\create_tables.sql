-- ========================================
-- Yemen Maps - Database Tables Creation
-- ========================================

-- الاتصال بقاعدة البيانات الموجودة
\c yemen_gps;

-- تفعيل PostGIS إذا لم يكن مفعل
CREATE EXTENSION IF NOT EXISTS postgis;

-- ========================================
-- جدول المحافظات والمدن (إذا لم يكن موجود)
-- ========================================
CREATE TABLE IF NOT EXISTS locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    type VARCHAR(50) NOT NULL, -- 'governorate', 'city', 'district'
    parent_id INTEGER REFERENCES locations(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    bounds TEXT, -- JSON للحدود الجغرافية
    population INTEGER,
    area_km2 DECIMAL(10, 2),
    data_status VARCHAR(50) DEFAULT 'pending',
    places_count INTEGER DEFAULT 0,
    photos_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- جدول فئات الأماكن (إذا لم يكن موجود)
-- ========================================
CREATE TABLE IF NOT EXISTS place_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    parent_id INTEGER REFERENCES place_categories(id),
    icon VARCHAR(100),
    color VARCHAR(7), -- hex color
    description TEXT,
    description_ar TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- تحديث جدول الأماكن الموجود
-- ========================================

-- إضافة أعمدة جديدة إذا لم تكن موجودة
DO $$ 
BEGIN
    -- إضافة عمود location_point للبيانات الجغرافية
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='location_point') THEN
        ALTER TABLE places ADD COLUMN location_point GEOMETRY(POINT, 4326);
    END IF;
    
    -- إضافة عمود governorate_id
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='governorate_id') THEN
        ALTER TABLE places ADD COLUMN governorate_id INTEGER REFERENCES locations(id);
    END IF;
    
    -- إضافة عمود source
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='source') THEN
        ALTER TABLE places ADD COLUMN source VARCHAR(50) DEFAULT 'imported';
    END IF;
    
    -- إضافة عمود is_active
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='is_active') THEN
        ALTER TABLE places ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
    
    -- إضافة عمود is_verified
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='is_verified') THEN
        ALTER TABLE places ADD COLUMN is_verified BOOLEAN DEFAULT TRUE;
    END IF;
    
    -- إضافة عمود place_id إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='place_id') THEN
        ALTER TABLE places ADD COLUMN place_id VARCHAR(255) UNIQUE;
    END IF;
    
    -- إضافة عمود category إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='category') THEN
        ALTER TABLE places ADD COLUMN category VARCHAR(100) DEFAULT 'general';
    END IF;
    
    -- إضافة عمود subcategory إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='subcategory') THEN
        ALTER TABLE places ADD COLUMN subcategory VARCHAR(100);
    END IF;
    
    -- إضافة عمود rating إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='rating') THEN
        ALTER TABLE places ADD COLUMN rating DECIMAL(3, 2);
    END IF;
    
    -- إضافة عمود phone إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='phone') THEN
        ALTER TABLE places ADD COLUMN phone VARCHAR(50);
    END IF;
    
    -- إضافة عمود website إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='website') THEN
        ALTER TABLE places ADD COLUMN website TEXT;
    END IF;
    
    -- إضافة عمود address إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='address') THEN
        ALTER TABLE places ADD COLUMN address TEXT;
    END IF;
    
    -- إضافة عمود name_ar إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name='places' AND column_name='name_ar') THEN
        ALTER TABLE places ADD COLUMN name_ar VARCHAR(500);
    END IF;
    
END $$;

-- ========================================
-- جدول صور الأماكن (إذا لم يكن موجود)
-- ========================================
CREATE TABLE IF NOT EXISTS place_photos (
    id SERIAL PRIMARY KEY,
    place_id VARCHAR(255) NOT NULL,
    photo_reference VARCHAR(500),
    photo_path VARCHAR(500) NOT NULL,
    photo_url TEXT,
    width INTEGER,
    height INTEGER,
    file_size INTEGER,
    file_format VARCHAR(10),
    photo_type VARCHAR(50) DEFAULT 'general',
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INTEGER DEFAULT 0,
    source VARCHAR(50) NOT NULL DEFAULT 'imported',
    attribution TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by VARCHAR(100)
);

-- ========================================
-- جدول إحصائيات التحميل
-- ========================================
CREATE TABLE IF NOT EXISTS download_stats (
    id SERIAL PRIMARY KEY,
    location_id INTEGER REFERENCES locations(id),
    location_name VARCHAR(255),
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(50) DEFAULT 'in_progress',
    places_found INTEGER DEFAULT 0,
    places_downloaded INTEGER DEFAULT 0,
    photos_downloaded INTEGER DEFAULT 0,
    api_calls_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- إنشاء الفهارس للأداء
-- ========================================

-- فهارس جغرافية
CREATE INDEX IF NOT EXISTS idx_places_location ON places USING GIST (location_point);
CREATE INDEX IF NOT EXISTS idx_places_lat_lng ON places (latitude, longitude);

-- فهارس البحث
CREATE INDEX IF NOT EXISTS idx_places_name ON places (name);
CREATE INDEX IF NOT EXISTS idx_places_name_ar ON places (name_ar);
CREATE INDEX IF NOT EXISTS idx_places_category ON places (category);
CREATE INDEX IF NOT EXISTS idx_places_governorate ON places (governorate_id);

-- فهارس المصدر والحالة
CREATE INDEX IF NOT EXISTS idx_places_source ON places (source);
CREATE INDEX IF NOT EXISTS idx_places_active ON places (is_active);
CREATE INDEX IF NOT EXISTS idx_places_verified ON places (is_verified);
CREATE INDEX IF NOT EXISTS idx_places_place_id ON places (place_id);

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_place_id ON place_photos (place_id);
CREATE INDEX IF NOT EXISTS idx_photos_primary ON place_photos (is_primary);
CREATE INDEX IF NOT EXISTS idx_photos_active ON place_photos (is_active);

-- ========================================
-- إدراج البيانات الأساسية
-- ========================================

-- المحافظات اليمنية (إدراج فقط إذا لم تكن موجودة)
INSERT INTO locations (name, name_ar, name_en, type, latitude, longitude) 
SELECT * FROM (VALUES
    ('أمانة العاصمة', 'أمانة العاصمة', 'Amanat Al Asimah', 'governorate', 15.3694, 44.1910),
    ('عدن', 'عدن', 'Aden', 'governorate', 12.7797, 45.0365),
    ('تعز', 'تعز', 'Taiz', 'governorate', 13.5795, 44.0207),
    ('الحديدة', 'الحديدة', 'Al Hudaydah', 'governorate', 14.7978, 42.9545),
    ('إب', 'إب', 'Ibb', 'governorate', 13.9670, 44.1839),
    ('ذمار', 'ذمار', 'Dhamar', 'governorate', 14.5426, 44.4054),
    ('صنعاء', 'صنعاء', 'Sanaa', 'governorate', 15.3694, 44.1910),
    ('لحج', 'لحج', 'Lahij', 'governorate', 13.0582, 44.8819),
    ('أبين', 'أبين', 'Abyan', 'governorate', 13.9500, 45.3667),
    ('حضرموت', 'حضرموت', 'Hadramaut', 'governorate', 15.9500, 48.6167),
    ('البيضاء', 'البيضاء', 'Al Bayda', 'governorate', 13.9850, 45.5733),
    ('مأرب', 'مأرب', 'Marib', 'governorate', 15.4694, 45.3222),
    ('الجوف', 'الجوف', 'Al Jawf', 'governorate', 16.6000, 45.5000),
    ('صعدة', 'صعدة', 'Saada', 'governorate', 16.9400, 43.7639),
    ('حجة', 'حجة', 'Hajjah', 'governorate', 15.6944, 43.6056),
    ('المحويت', 'المحويت', 'Al Mahwit', 'governorate', 15.4700, 43.5500),
    ('عمران', 'عمران', 'Amran', 'governorate', 15.6594, 43.9444),
    ('ريمة', 'ريمة', 'Raymah', 'governorate', 14.6000, 43.7000),
    ('الضالع', 'الضالع', 'Ad Dali', 'governorate', 13.7000, 44.7333),
    ('شبوة', 'شبوة', 'Shabwah', 'governorate', 14.5333, 46.8333),
    ('المهرة', 'المهرة', 'Al Mahrah', 'governorate', 16.7167, 51.8500),
    ('جزيرة سقطرى', 'جزيرة سقطرى', 'Socotra', 'governorate', 12.5000, 53.8167)
) AS v(name, name_ar, name_en, type, latitude, longitude)
WHERE NOT EXISTS (SELECT 1 FROM locations WHERE name_ar = v.name_ar);

-- فئات الأماكن الأساسية (إدراج فقط إذا لم تكن موجودة)
INSERT INTO place_categories (name, name_ar, name_en, icon, color) 
SELECT * FROM (VALUES
    ('restaurant', 'مطعم', 'Restaurant', 'restaurant', '#FF6B6B'),
    ('hospital', 'مستشفى', 'Hospital', 'local_hospital', '#4ECDC4'),
    ('school', 'مدرسة', 'School', 'school', '#45B7D1'),
    ('mosque', 'مسجد', 'Mosque', 'place', '#96CEB4'),
    ('bank', 'بنك', 'Bank', 'account_balance', '#FFEAA7'),
    ('gas_station', 'محطة وقود', 'Gas Station', 'local_gas_station', '#DDA0DD'),
    ('shopping_mall', 'مركز تجاري', 'Shopping Mall', 'shopping_cart', '#98D8C8'),
    ('hotel', 'فندق', 'Hotel', 'hotel', '#F7DC6F'),
    ('pharmacy', 'صيدلية', 'Pharmacy', 'local_pharmacy', '#BB8FCE'),
    ('university', 'جامعة', 'University', 'account_balance', '#85C1E9'),
    ('general', 'عام', 'General', 'place', '#95A5A6')
) AS v(name, name_ar, name_en, icon, color)
WHERE NOT EXISTS (SELECT 1 FROM place_categories WHERE name = v.name);

-- ========================================
-- تحديث البيانات الموجودة
-- ========================================

-- تحديث place_id للأماكن الموجودة إذا لم يكن موجود
UPDATE places 
SET place_id = 'imported_' || id::text 
WHERE place_id IS NULL;

-- تحديث location_point للأماكن الموجودة
UPDATE places 
SET location_point = ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)
WHERE location_point IS NULL AND latitude IS NOT NULL AND longitude IS NOT NULL;

-- تحديث governorate_id للأماكن في صنعاء
UPDATE places 
SET governorate_id = (SELECT id FROM locations WHERE name_ar = 'أمانة العاصمة' LIMIT 1)
WHERE governorate_id IS NULL;

-- تحديث name_ar إذا كان فارغ
UPDATE places 
SET name_ar = name 
WHERE name_ar IS NULL AND name IS NOT NULL;

-- تحديث category للأماكن الموجودة
UPDATE places 
SET category = 'general' 
WHERE category IS NULL;

-- ========================================
-- انتهاء إعداد قاعدة البيانات
-- ========================================
