-- تحديث جدول المواقع لإضافة أعمدة المستخدم والعميل
-- Yemen Maps - Places Table Update

-- إضافة عمود رقم المستخدم (اختياري)
ALTER TABLE places 
ADD COLUMN IF NOT EXISTS added_by_user_id INTEGER REFERENCES users(id) ON DELETE SET NULL;

-- إضافة عمود رقم العميل (اختياري)  
ALTER TABLE places 
ADD COLUMN IF NOT EXISTS added_by_client_id INTEGER REFERENCES clients(id) ON DELETE SET NULL;

-- إضافة تعليقات للأعمدة الجديدة
COMMENT ON COLUMN places.added_by_user_id IS 'رقم المستخدم الذي أضاف الموقع (اختياري)';
COMMENT ON COLUMN places.added_by_client_id IS 'رقم العميل الذي أضاف الموقع (اختياري)';

-- إنشاء فهارس للأعمدة الجديدة لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_places_added_by_user ON places(added_by_user_id);
CREATE INDEX IF NOT EXISTS idx_places_added_by_client ON places(added_by_client_id);

-- إنشاء فهرس مركب للبحث السريع
CREATE INDEX IF NOT EXISTS idx_places_search ON places(name_ar, name_en, id);

-- عرض هيكل الجدول المحدث
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'places' 
ORDER BY ordinal_position;

-- اختبار الاستعلام الجديد مع الربط
SELECT 
    p.id,
    p.name_ar,
    p.name_en,
    p.category_id,
    p.governorate_id,
    p.latitude,
    p.longitude,
    p.is_active,
    p.added_by_user_id,
    p.added_by_client_id,
    u.full_name as added_by_user_name,
    u.username as added_by_username,
    c.name as added_by_client_name,
    cat.name as category_name,
    g.name_ar as governorate_name
FROM places p
LEFT JOIN users u ON p.added_by_user_id = u.id
LEFT JOIN clients c ON p.added_by_client_id = c.id
LEFT JOIN categories cat ON p.category_id = cat.id
LEFT JOIN governorates g ON p.governorate_id = g.id
ORDER BY p.created_at DESC
LIMIT 10;
