// فحص الجداول الموجودة في قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'yemen_gps',
    user: 'yemen',
    password: 'admin'
});

async function checkTables() {
    try {
        console.log('🔍 فحص الجداول الموجودة في قاعدة البيانات...\n');
        
        // الحصول على قائمة الجداول
        const tablesQuery = `
            SELECT table_name, table_type
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        `;
        
        const result = await pool.query(tablesQuery);
        
        if (result.rows.length === 0) {
            console.log('❌ لا توجد جداول في قاعدة البيانات');
            console.log('💡 يجب إنشاء الجداول أولاً');
        } else {
            console.log(`✅ تم العثور على ${result.rows.length} جدول:\n`);
            
            for (const row of result.rows) {
                console.log(`📋 ${row.table_name} (${row.table_type})`);
                
                // الحصول على عدد السجلات في كل جدول
                try {
                    const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${row.table_name}`);
                    console.log(`   📊 عدد السجلات: ${countResult.rows[0].count}`);
                } catch (err) {
                    console.log(`   ❌ خطأ في عد السجلات: ${err.message}`);
                }
                console.log('');
            }
        }
        
        // فحص الامتدادات المثبتة
        console.log('\n🔧 فحص الامتدادات المثبتة:');
        const extensionsQuery = `
            SELECT extname, extversion
            FROM pg_extension
            ORDER BY extname;
        `;
        
        const extensionsResult = await pool.query(extensionsQuery);
        if (extensionsResult.rows.length > 0) {
            extensionsResult.rows.forEach(ext => {
                console.log(`   🔌 ${ext.extname} (${ext.extversion})`);
            });
        } else {
            console.log('   ❌ لا توجد امتدادات مثبتة');
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص قاعدة البيانات:', error.message);
    } finally {
        await pool.end();
    }
}

checkTables();
