// ملف الاتصال بقاعدة البيانات PostgreSQL
// PostgreSQL Database Connection

const { Pool } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    host: 'localhost',
    port: 5432,
    database: 'yemen_gps',
    user: 'yemen',
    password: 'admin',
    // إعدادات إضافية
    max: 20, // الحد الأقصى للاتصالات في المجموعة
    idleTimeoutMillis: 30000, // مهلة انتظار الاتصال الخامل
    connectionTimeoutMillis: 2000, // مهلة انتظار الاتصال
    ssl: false // تعطيل SSL للاتصال المحلي
};

// إنشاء مجموعة الاتصالات
const pool = new Pool(dbConfig);

// معالج الأخطاء
pool.on('error', (err, client) => {
    console.error('خطأ غير متوقع في عميل قاعدة البيانات:', err);
    process.exit(-1);
});

// دالة اختبار الاتصال
async function testConnection() {
    try {
        const client = await pool.connect();
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
        
        // اختبار استعلام بسيط
        const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
        console.log('⏰ الوقت الحالي:', result.rows[0].current_time);
        console.log('📊 إصدار PostgreSQL:', result.rows[0].pg_version);
        
        client.release();
        return true;
    } catch (err) {
        console.error('❌ فشل في الاتصال بقاعدة البيانات:', err.message);
        return false;
    }
}

// دالة تنفيذ استعلام
async function query(text, params) {
    const start = Date.now();
    try {
        const res = await pool.query(text, params);
        const duration = Date.now() - start;
        console.log('🔍 تم تنفيذ الاستعلام:', { text, duration, rows: res.rowCount });
        return res;
    } catch (err) {
        console.error('❌ خطأ في تنفيذ الاستعلام:', err.message);
        throw err;
    }
}

// دالة الحصول على عميل من المجموعة
async function getClient() {
    return await pool.connect();
}

// دالة إغلاق المجموعة
async function closePool() {
    await pool.end();
    console.log('🔒 تم إغلاق مجموعة الاتصالات');
}

// تصدير الوظائف
module.exports = {
    pool,
    query,
    getClient,
    testConnection,
    closePool
};

// اختبار الاتصال عند تحميل الملف
if (require.main === module) {
    testConnection().then(success => {
        if (success) {
            console.log('🎉 اختبار الاتصال نجح!');
        } else {
            console.log('💥 اختبار الاتصال فشل!');
        }
        process.exit(success ? 0 : 1);
    });
}
