-- ========================================
-- Yemen Maps Complete - Database Schema
-- ========================================

-- إنشاء قاعدة بيانات جديدة للمشروع
CREATE DATABASE yemen_maps_complete 
WITH 
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- الاتصال بقاعدة البيانات الجديدة
\c yemen_maps_complete;

-- تفعيل PostGIS للبيانات الجغرافية
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;

-- ========================================
-- جدول المحافظات والمدن
-- ========================================
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    type VARCHAR(50) NOT NULL, -- 'governorate', 'city', 'district'
    parent_id INTEGER REFERENCES locations(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    bounds TEXT, -- JSON للحدود الجغرافية
    population INTEGER,
    area_km2 DECIMAL(10, 2),
    data_status VARCHAR(50) DEFAULT 'pending', -- 'complete', 'partial', 'pending', 'in_progress'
    places_count INTEGER DEFAULT 0,
    photos_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- جدول الأماكن الرئيسي
-- ========================================
CREATE TABLE places (
    id SERIAL PRIMARY KEY,
    place_id VARCHAR(255) UNIQUE NOT NULL, -- Google Place ID أو معرف محلي
    name VARCHAR(500) NOT NULL,
    name_ar VARCHAR(500),
    name_en VARCHAR(500),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location_point GEOMETRY(POINT, 4326), -- PostGIS point
    
    -- معلومات الموقع
    governorate_id INTEGER REFERENCES locations(id),
    city VARCHAR(255),
    district VARCHAR(255),
    address TEXT,
    address_ar TEXT,
    postal_code VARCHAR(20),
    
    -- تصنيف المكان
    category VARCHAR(100) NOT NULL, -- restaurant, hospital, school, etc.
    subcategory VARCHAR(100),
    place_type VARCHAR(100),
    business_status VARCHAR(50) DEFAULT 'OPERATIONAL',
    
    -- معلومات الاتصال
    phone VARCHAR(50),
    international_phone VARCHAR(50),
    website TEXT,
    email VARCHAR(255),
    
    -- معلومات التقييم
    rating DECIMAL(3, 2),
    user_ratings_total INTEGER DEFAULT 0,
    price_level INTEGER, -- 0-4 scale
    
    -- أوقات العمل
    opening_hours TEXT, -- JSON format
    opening_hours_ar TEXT,
    
    -- معلومات إضافية
    description TEXT,
    description_ar TEXT,
    amenities TEXT, -- JSON array
    accessibility TEXT, -- JSON for accessibility info
    
    -- معلومات المصدر
    source VARCHAR(50) NOT NULL, -- 'google', 'manual', 'imported', 'osm'
    google_place_id VARCHAR(255),
    osm_id VARCHAR(100),
    
    -- معلومات النظام
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- ========================================
-- جدول صور الأماكن
-- ========================================
CREATE TABLE place_photos (
    id SERIAL PRIMARY KEY,
    place_id VARCHAR(255) NOT NULL REFERENCES places(place_id) ON DELETE CASCADE,
    photo_reference VARCHAR(500), -- Google photo reference
    photo_path VARCHAR(500) NOT NULL, -- مسار الصورة المحلي
    photo_url TEXT, -- الرابط الأصلي
    
    -- معلومات الصورة
    width INTEGER,
    height INTEGER,
    file_size INTEGER, -- بالبايت
    file_format VARCHAR(10), -- jpg, png, webp
    
    -- تصنيف الصورة
    photo_type VARCHAR(50) DEFAULT 'general', -- 'exterior', 'interior', 'food', 'menu', 'general'
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INTEGER DEFAULT 0,
    
    -- معلومات المصدر
    source VARCHAR(50) NOT NULL, -- 'google', 'manual', 'user'
    attribution TEXT,
    
    -- معلومات النظام
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by VARCHAR(100)
);

-- ========================================
-- جدول فئات الأماكن
-- ========================================
CREATE TABLE place_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    parent_id INTEGER REFERENCES place_categories(id),
    icon VARCHAR(100),
    color VARCHAR(7), -- hex color
    description TEXT,
    description_ar TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- جدول إحصائيات التحميل
-- ========================================
CREATE TABLE download_stats (
    id SERIAL PRIMARY KEY,
    location_id INTEGER REFERENCES locations(id),
    location_name VARCHAR(255),
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(50) DEFAULT 'in_progress', -- 'completed', 'failed', 'in_progress'
    places_found INTEGER DEFAULT 0,
    places_downloaded INTEGER DEFAULT 0,
    photos_downloaded INTEGER DEFAULT 0,
    api_calls_used INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ========================================
-- إنشاء الفهارس للأداء
-- ========================================

-- فهارس جغرافية
CREATE INDEX idx_places_location ON places USING GIST (location_point);
CREATE INDEX idx_places_lat_lng ON places (latitude, longitude);

-- فهارس البحث
CREATE INDEX idx_places_name ON places (name);
CREATE INDEX idx_places_name_ar ON places (name_ar);
CREATE INDEX idx_places_category ON places (category);
CREATE INDEX idx_places_governorate ON places (governorate_id);

-- فهارس المصدر والحالة
CREATE INDEX idx_places_source ON places (source);
CREATE INDEX idx_places_active ON places (is_active);
CREATE INDEX idx_places_verified ON places (is_verified);

-- فهارس الصور
CREATE INDEX idx_photos_place_id ON place_photos (place_id);
CREATE INDEX idx_photos_primary ON place_photos (is_primary);
CREATE INDEX idx_photos_active ON place_photos (is_active);

-- ========================================
-- إدراج البيانات الأساسية
-- ========================================

-- المحافظات اليمنية
INSERT INTO locations (name, name_ar, name_en, type, latitude, longitude) VALUES
('أمانة العاصمة', 'أمانة العاصمة', 'Amanat Al Asimah', 'governorate', 15.3694, 44.1910),
('عدن', 'عدن', 'Aden', 'governorate', 12.7797, 45.0365),
('تعز', 'تعز', 'Taiz', 'governorate', 13.5795, 44.0207),
('الحديدة', 'الحديدة', 'Al Hudaydah', 'governorate', 14.7978, 42.9545),
('إب', 'إب', 'Ibb', 'governorate', 13.9670, 44.1839),
('ذمار', 'ذمار', 'Dhamar', 'governorate', 14.5426, 44.4054),
('صنعاء', 'صنعاء', 'Sanaa', 'governorate', 15.3694, 44.1910),
('لحج', 'لحج', 'Lahij', 'governorate', 13.0582, 44.8819),
('أبين', 'أبين', 'Abyan', 'governorate', 13.9500, 45.3667),
('حضرموت', 'حضرموت', 'Hadramaut', 'governorate', 15.9500, 48.6167),
('البيضاء', 'البيضاء', 'Al Bayda', 'governorate', 13.9850, 45.5733),
('مأرب', 'مأرب', 'Marib', 'governorate', 15.4694, 45.3222),
('الجوف', 'الجوف', 'Al Jawf', 'governorate', 16.6000, 45.5000),
('صعدة', 'صعدة', 'Saada', 'governorate', 16.9400, 43.7639),
('حجة', 'حجة', 'Hajjah', 'governorate', 15.6944, 43.6056),
('المحويت', 'المحويت', 'Al Mahwit', 'governorate', 15.4700, 43.5500),
('عمران', 'عمران', 'Amran', 'governorate', 15.6594, 43.9444),
('ريمة', 'ريمة', 'Raymah', 'governorate', 14.6000, 43.7000),
('الضالع', 'الضالع', 'Ad Dali', 'governorate', 13.7000, 44.7333),
('شبوة', 'شبوة', 'Shabwah', 'governorate', 14.5333, 46.8333),
('المهرة', 'المهرة', 'Al Mahrah', 'governorate', 16.7167, 51.8500),
('جزيرة سقطرى', 'جزيرة سقطرى', 'Socotra', 'governorate', 12.5000, 53.8167);

-- فئات الأماكن الأساسية
INSERT INTO place_categories (name, name_ar, name_en, icon, color) VALUES
('restaurant', 'مطعم', 'Restaurant', 'restaurant', '#FF6B6B'),
('hospital', 'مستشفى', 'Hospital', 'local_hospital', '#4ECDC4'),
('school', 'مدرسة', 'School', 'school', '#45B7D1'),
('mosque', 'مسجد', 'Mosque', 'place', '#96CEB4'),
('bank', 'بنك', 'Bank', 'account_balance', '#FFEAA7'),
('gas_station', 'محطة وقود', 'Gas Station', 'local_gas_station', '#DDA0DD'),
('shopping_mall', 'مركز تجاري', 'Shopping Mall', 'shopping_cart', '#98D8C8'),
('hotel', 'فندق', 'Hotel', 'hotel', '#F7DC6F'),
('pharmacy', 'صيدلية', 'Pharmacy', 'local_pharmacy', '#BB8FCE'),
('university', 'جامعة', 'University', 'account_balance', '#85C1E9');

-- تحديث الطوابع الزمنية تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_places_updated_at BEFORE UPDATE ON places
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- إنشاء مستخدم للتطبيق
-- ========================================
CREATE USER yemen_maps_user WITH PASSWORD 'YemenMaps2024!';
GRANT ALL PRIVILEGES ON DATABASE yemen_maps_complete TO yemen_maps_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yemen_maps_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yemen_maps_user;

-- ========================================
-- انتهاء إعداد قاعدة البيانات
-- ========================================
