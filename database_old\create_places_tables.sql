-- إنشاء جداول الأماكن والصور ومعلومات التواصل
-- Creating Places, Images and Contact Information Tables

-- جدول المحافظات اليمنية (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS governorates (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المديريات (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS districts (
    id SERIAL PRIMARY KEY,
    governorate_id INTEGER NOT NULL REFERENCES governorates(id) ON DELETE CASCADE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(15) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فئات الأماكن الجديدة
CREATE TABLE IF NOT EXISTS place_categories (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    icon VARCHAR(100),
    color VARCHAR(7) DEFAULT '#FF0000',
    parent_id INTEGER REFERENCES place_categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأماكن الرئيسي
CREATE TABLE IF NOT EXISTS places (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    category_id INTEGER NOT NULL REFERENCES place_categories(id),
    governorate_id INTEGER REFERENCES governorates(id),
    district_id INTEGER REFERENCES districts(id),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address_ar TEXT,
    address_en TEXT,
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(200),
    facebook VARCHAR(200),
    instagram VARCHAR(200),
    twitter VARCHAR(200),
    whatsapp VARCHAR(20),
    rating DECIMAL(2,1) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    reviews_count INTEGER DEFAULT 0,
    price_range VARCHAR(4) CHECK (price_range IN ('$', '$$', '$$$', '$$$$')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس للأماكن
CREATE INDEX IF NOT EXISTS idx_places_lat_lng ON places (latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_places_category ON places (category_id);
CREATE INDEX IF NOT EXISTS idx_places_rating ON places (rating DESC);
CREATE INDEX IF NOT EXISTS idx_places_governorate ON places (governorate_id);

-- جدول صور الأماكن
CREATE TABLE IF NOT EXISTS place_images (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    image_url VARCHAR(500) NOT NULL,
    image_type VARCHAR(20) DEFAULT 'gallery' CHECK (image_type IN ('main', 'gallery', 'menu', 'interior', 'exterior')),
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_place_images_place_type ON place_images (place_id, image_type);

-- جدول أوقات العمل
CREATE TABLE IF NOT EXISTS opening_hours (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    day_of_week SMALLINT NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    open_time TIME,
    close_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    is_24_hours BOOLEAN DEFAULT FALSE,
    notes_ar VARCHAR(200),
    notes_en VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (place_id, day_of_week)
);

-- جدول المرافق والخدمات
CREATE TABLE IF NOT EXISTS amenities (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    icon VARCHAR(100),
    category VARCHAR(20) DEFAULT 'general' CHECK (category IN ('general', 'hotel', 'restaurant', 'hospital', 'education', 'transport')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط الأماكن بالمرافق
CREATE TABLE IF NOT EXISTS place_amenities (
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    amenity_id INTEGER NOT NULL REFERENCES amenities(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (place_id, amenity_id)
);

-- جدول التقييمات والمراجعات للأماكن
CREATE TABLE IF NOT EXISTS place_reviews (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(100),
    rating SMALLINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    comment_ar TEXT,
    comment_en TEXT,
    visit_date DATE,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_place_reviews_place_rating ON place_reviews (place_id, rating);
CREATE INDEX IF NOT EXISTS idx_place_reviews_date ON place_reviews (created_at DESC);

-- جدول قوائم الطعام (للمطاعم)
CREATE TABLE IF NOT EXISTS menu_categories (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر القائمة
CREATE TABLE IF NOT EXISTS menu_items (
    id SERIAL PRIMARY KEY,
    category_id INTEGER NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    price DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    image_url VARCHAR(500),
    is_available BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الغرف (للفنادق)
CREATE TABLE IF NOT EXISTS hotel_rooms (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    room_type_ar VARCHAR(100) NOT NULL,
    room_type_en VARCHAR(100),
    description_ar TEXT,
    description_en TEXT,
    price_per_night DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    max_occupancy INTEGER DEFAULT 2,
    room_count INTEGER DEFAULT 1,
    amenities JSONB,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المفضلة للمستخدمين
CREATE TABLE IF NOT EXISTS user_place_favorites (
    id SERIAL PRIMARY KEY,
    user_session VARCHAR(100) NOT NULL,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_session, place_id)
);

CREATE INDEX IF NOT EXISTS idx_user_place_favorites_session ON user_place_favorites (user_session);

-- جدول إحصائيات زيارات الأماكن
CREATE TABLE IF NOT EXISTS place_visits (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    visit_date DATE NOT NULL,
    views_count INTEGER DEFAULT 1,
    directions_count INTEGER DEFAULT 0,
    calls_count INTEGER DEFAULT 0,
    website_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (place_id, visit_date)
);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق trigger على الجداول الجديدة
DROP TRIGGER IF EXISTS update_places_updated_at ON places;
CREATE TRIGGER update_places_updated_at
    BEFORE UPDATE ON places
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_place_reviews_updated_at ON place_reviews;
CREATE TRIGGER update_place_reviews_updated_at
    BEFORE UPDATE ON place_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_menu_items_updated_at ON menu_items;
CREATE TRIGGER update_menu_items_updated_at
    BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_hotel_rooms_updated_at ON hotel_rooms;
CREATE TRIGGER update_hotel_rooms_updated_at
    BEFORE UPDATE ON hotel_rooms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_place_visits_updated_at ON place_visits;
CREATE TRIGGER update_place_visits_updated_at
    BEFORE UPDATE ON place_visits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- رسالة النجاح
SELECT 'تم إنشاء جداول الأماكن بنجاح - Places tables created successfully' as status;
