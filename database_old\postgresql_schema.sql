-- قاعدة بيانات نظام الخرائط اليمني - PostgreSQL
-- Yemen GPS Database Schema - PostgreSQL

-- إنشاء قاعدة البيانات والاتصال
-- CREATE DATABASE yemen_gps WITH ENCODING 'UTF8' LC_COLLATE='ar_YE.UTF-8' LC_CTYPE='ar_YE.UTF-8';
-- \c yemen_gps;

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- جدول المحافظات
CREATE TABLE governorates (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المديريات
CREATE TABLE districts (
    id SERIAL PRIMARY KEY,
    governorate_id INTEGER NOT NULL REFERENCES governorates(id) ON DELETE CASCADE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(15) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فئات الأماكن
CREATE TABLE place_categories (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    icon VARCHAR(100),
    color VARCHAR(7) DEFAULT '#FF0000',
    parent_id INTEGER REFERENCES place_categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأماكن الرئيسي
CREATE TABLE places (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    category_id INTEGER NOT NULL REFERENCES place_categories(id),
    governorate_id INTEGER NOT NULL REFERENCES governorates(id),
    district_id INTEGER REFERENCES districts(id),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for spatial queries
    address_ar TEXT,
    address_en TEXT,
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(200),
    facebook VARCHAR(200),
    instagram VARCHAR(200),
    twitter VARCHAR(200),
    whatsapp VARCHAR(20),
    rating DECIMAL(2,1) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
    reviews_count INTEGER DEFAULT 0,
    price_range VARCHAR(4) CHECK (price_range IN ('$', '$$', '$$$', '$$$$')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهرس مكاني للموقع
CREATE INDEX idx_places_location ON places USING GIST (location);
CREATE INDEX idx_places_lat_lng ON places (latitude, longitude);
CREATE INDEX idx_places_category ON places (category_id);
CREATE INDEX idx_places_rating ON places (rating DESC);
CREATE INDEX idx_places_search ON places USING GIN (to_tsvector('arabic', name_ar || ' ' || COALESCE(description_ar, '')));

-- جدول صور الأماكن
CREATE TABLE place_images (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    image_url VARCHAR(500) NOT NULL,
    image_type VARCHAR(20) DEFAULT 'gallery' CHECK (image_type IN ('main', 'gallery', 'menu', 'interior', 'exterior')),
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_place_images_place_type ON place_images (place_id, image_type);

-- جدول أوقات العمل
CREATE TABLE opening_hours (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    day_of_week SMALLINT NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=الأحد, 1=الاثنين, ... 6=السبت
    open_time TIME,
    close_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    is_24_hours BOOLEAN DEFAULT FALSE,
    notes_ar VARCHAR(200),
    notes_en VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (place_id, day_of_week)
);

-- جدول المرافق والخدمات
CREATE TABLE amenities (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    icon VARCHAR(100),
    category VARCHAR(20) DEFAULT 'general' CHECK (category IN ('general', 'hotel', 'restaurant', 'hospital', 'education', 'transport')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط الأماكن بالمرافق
CREATE TABLE place_amenities (
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    amenity_id INTEGER NOT NULL REFERENCES amenities(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (place_id, amenity_id)
);

-- جدول التقييمات والمراجعات
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(100),
    rating SMALLINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    comment_ar TEXT,
    comment_en TEXT,
    visit_date DATE,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_reviews_place_rating ON reviews (place_id, rating);
CREATE INDEX idx_reviews_date ON reviews (created_at DESC);

-- جدول قوائم الطعام (للمطاعم)
CREATE TABLE menu_categories (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر القائمة
CREATE TABLE menu_items (
    id SERIAL PRIMARY KEY,
    category_id INTEGER NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    price DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    image_url VARCHAR(500),
    is_available BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الغرف (للفنادق)
CREATE TABLE hotel_rooms (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    room_type_ar VARCHAR(100) NOT NULL,
    room_type_en VARCHAR(100),
    description_ar TEXT,
    description_en TEXT,
    price_per_night DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    max_occupancy INTEGER DEFAULT 2,
    room_count INTEGER DEFAULT 1,
    amenities JSONB,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأحداث والفعاليات
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    place_id INTEGER REFERENCES places(id) ON DELETE SET NULL,
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    event_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    price DECIMAL(10, 2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'YER',
    max_attendees INTEGER,
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_events_date ON events (event_date);

-- جدول الإعلانات المدفوعة
CREATE TABLE advertisements (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    image_url VARCHAR(500),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    budget DECIMAL(10, 2),
    clicks_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_advertisements_dates ON advertisements (start_date, end_date);

-- جدول المفضلة للمستخدمين
CREATE TABLE user_favorites (
    id SERIAL PRIMARY KEY,
    user_session VARCHAR(100) NOT NULL,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_session, place_id)
);

CREATE INDEX idx_user_favorites_session ON user_favorites (user_session);

-- جدول إحصائيات الزيارات
CREATE TABLE place_visits (
    id SERIAL PRIMARY KEY,
    place_id INTEGER NOT NULL REFERENCES places(id) ON DELETE CASCADE,
    visit_date DATE NOT NULL,
    views_count INTEGER DEFAULT 1,
    directions_count INTEGER DEFAULT 0,
    calls_count INTEGER DEFAULT 0,
    website_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (place_id, visit_date)
);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق trigger على الجداول المطلوبة
CREATE TRIGGER update_governorates_updated_at BEFORE UPDATE ON governorates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_districts_updated_at BEFORE UPDATE ON districts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_places_updated_at BEFORE UPDATE ON places FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_hotel_rooms_updated_at BEFORE UPDATE ON hotel_rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_advertisements_updated_at BEFORE UPDATE ON advertisements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_place_visits_updated_at BEFORE UPDATE ON place_visits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إنشاء trigger لتحديث الموقع الجغرافي تلقائياً
CREATE OR REPLACE FUNCTION update_location_point()
RETURNS TRIGGER AS $$
BEGIN
    NEW.location = ST_SetSRID(ST_MakePoint(NEW.longitude, NEW.latitude), 4326);
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_places_location BEFORE INSERT OR UPDATE ON places FOR EACH ROW EXECUTE FUNCTION update_location_point();
