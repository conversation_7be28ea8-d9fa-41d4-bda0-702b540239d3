// تنفيذ ملفات SQL
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'yemen_gps',
    user: 'yemen',
    password: 'admin'
});

async function runSQLFile(filename) {
    try {
        console.log(`🔄 تنفيذ ملف: ${filename}`);

        const sqlPath = path.join(__dirname, filename);
        const sql = fs.readFileSync(sqlPath, 'utf8');

        const result = await pool.query(sql);
        console.log(`✅ تم تنفيذ ${filename} بنجاح`);

        if (result.rows && result.rows.length > 0) {
            console.log('📋 النتيجة:', result.rows[0]);
        }

        return true;
    } catch (error) {
        console.error(`❌ خطأ في تنفيذ ${filename}:`, error.message);
        return false;
    }
}

async function main() {
    try {
        console.log('🚀 بدء إنشاء جداول الأماكن...\n');

        // تنفيذ ملف إنشاء الجداول
        await runSQLFile('create_places_tables.sql');

        // تنفيذ ملف البيانات الأولية
        await runSQLFile('insert_initial_data.sql');

        // تنفيذ ملف تفاصيل الأماكن
        await runSQLFile('insert_place_details.sql');

        console.log('\n🎉 تم الانتهاء من إنشاء الجداول!');

    } catch (error) {
        console.error('❌ خطأ عام:', error.message);
    } finally {
        await pool.end();
    }
}

main();
