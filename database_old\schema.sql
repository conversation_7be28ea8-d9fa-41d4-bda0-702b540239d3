-- قاعدة بيانات نظام الخرائط اليمني
-- Yemen GPS Database Schema

-- جدول المحافظات
CREATE TABLE governorates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المديريات
CREATE TABLE districts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    governorate_id INT NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(15) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (governorate_id) REFERENCES governorates(id) ON DELETE CASCADE
);

-- جدول فئات الأماكن
CREATE TABLE place_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(50) NOT NULL,
    name_en VARCHAR(50) NOT NULL,
    icon VARCHAR(100),
    color VARCHAR(7) DEFAULT '#FF0000',
    parent_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES place_categories(id) ON DELETE SET NULL
);

-- جدول الأماكن الرئيسي
CREATE TABLE places (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    category_id INT NOT NULL,
    governorate_id INT NOT NULL,
    district_id INT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address_ar TEXT,
    address_en TEXT,
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(200),
    facebook VARCHAR(200),
    instagram VARCHAR(200),
    twitter VARCHAR(200),
    whatsapp VARCHAR(20),
    rating DECIMAL(2,1) DEFAULT 0.0,
    reviews_count INT DEFAULT 0,
    price_range ENUM('$', '$$', '$$$', '$$$$') NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES place_categories(id),
    FOREIGN KEY (governorate_id) REFERENCES governorates(id),
    FOREIGN KEY (district_id) REFERENCES districts(id),
    INDEX idx_location (latitude, longitude),
    INDEX idx_category (category_id),
    INDEX idx_rating (rating),
    FULLTEXT idx_search (name_ar, name_en, description_ar, description_en)
);

-- جدول صور الأماكن
CREATE TABLE place_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('main', 'gallery', 'menu', 'interior', 'exterior') DEFAULT 'gallery',
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    INDEX idx_place_type (place_id, image_type)
);

-- جدول أوقات العمل
CREATE TABLE opening_hours (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    day_of_week TINYINT NOT NULL, -- 0=الأحد, 1=الاثنين, ... 6=السبت
    open_time TIME,
    close_time TIME,
    is_closed BOOLEAN DEFAULT FALSE,
    is_24_hours BOOLEAN DEFAULT FALSE,
    notes_ar VARCHAR(200),
    notes_en VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_place_day (place_id, day_of_week)
);

-- جدول المرافق والخدمات
CREATE TABLE amenities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    icon VARCHAR(100),
    category ENUM('general', 'hotel', 'restaurant', 'hospital', 'education', 'transport') DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ربط الأماكن بالمرافق
CREATE TABLE place_amenities (
    place_id INT NOT NULL,
    amenity_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (place_id, amenity_id),
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    FOREIGN KEY (amenity_id) REFERENCES amenities(id) ON DELETE CASCADE
);

-- جدول التقييمات والمراجعات
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(100),
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title_ar VARCHAR(200),
    title_en VARCHAR(200),
    comment_ar TEXT,
    comment_en TEXT,
    visit_date DATE,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    helpful_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    INDEX idx_place_rating (place_id, rating),
    INDEX idx_date (created_at)
);

-- جدول قوائم الطعام (للمطاعم)
CREATE TABLE menu_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE
);

-- جدول عناصر القائمة
CREATE TABLE menu_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    name_ar VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    price DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    image_url VARCHAR(500),
    is_available BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES menu_categories(id) ON DELETE CASCADE
);

-- جدول الغرف (للفنادق)
CREATE TABLE hotel_rooms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    room_type_ar VARCHAR(100) NOT NULL,
    room_type_en VARCHAR(100),
    description_ar TEXT,
    description_en TEXT,
    price_per_night DECIMAL(10, 2),
    currency VARCHAR(3) DEFAULT 'YER',
    max_occupancy INT DEFAULT 2,
    room_count INT DEFAULT 1,
    amenities JSON,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE
);

-- جدول الأحداث والفعاليات
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT,
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    event_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    price DECIMAL(10, 2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'YER',
    max_attendees INT,
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE SET NULL,
    INDEX idx_event_date (event_date)
);

-- جدول الإعلانات المدفوعة
CREATE TABLE advertisements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    title_ar VARCHAR(200) NOT NULL,
    title_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    image_url VARCHAR(500),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    budget DECIMAL(10, 2),
    clicks_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    INDEX idx_dates (start_date, end_date)
);

-- جدول المفضلة للمستخدمين
CREATE TABLE user_favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_session VARCHAR(100) NOT NULL,
    place_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_place (user_session, place_id),
    INDEX idx_user (user_session)
);

-- جدول إحصائيات الزيارات
CREATE TABLE place_visits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    place_id INT NOT NULL,
    visit_date DATE NOT NULL,
    views_count INT DEFAULT 1,
    directions_count INT DEFAULT 0,
    calls_count INT DEFAULT 0,
    website_clicks INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (place_id) REFERENCES places(id) ON DELETE CASCADE,
    UNIQUE KEY unique_place_date (place_id, visit_date)
);
