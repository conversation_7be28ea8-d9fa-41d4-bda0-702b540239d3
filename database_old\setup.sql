-- إعد<PERSON> قاعدة البيانات الكاملة
-- Complete Database Setup

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS yemen_gps_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE yemen_gps_db;

-- تشغيل ملفات الإعداد بالترتيب
SOURCE schema.sql;
SOURCE initial_data.sql;
SOURCE sample_images_data.sql;
SOURCE reviews_data.sql;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_places_name_search ON places(name_ar(50), name_en(50));
CREATE INDEX idx_places_location_category ON places(latitude, longitude, category_id);
CREATE INDEX idx_places_rating_reviews ON places(rating DESC, reviews_count DESC);
CREATE INDEX idx_reviews_place_date ON reviews(place_id, created_at DESC);
CREATE INDEX idx_images_place_type ON place_images(place_id, image_type, sort_order);

-- إن<PERSON>ا<PERSON> views مفيدة
CREATE VIEW places_with_details AS
SELECT 
    p.*,
    g.name_ar as governorate_name_ar,
    g.name_en as governorate_name_en,
    d.name_ar as district_name_ar,
    d.name_en as district_name_en,
    pc.name_ar as category_name_ar,
    pc.name_en as category_name_en,
    pc.icon as category_icon,
    pc.color as category_color,
    (SELECT image_url FROM place_images pi WHERE pi.place_id = p.id AND pi.image_type = 'main' LIMIT 1) as main_image,
    (SELECT COUNT(*) FROM place_images pi WHERE pi.place_id = p.id) as images_count,
    (SELECT COUNT(*) FROM place_amenities pa WHERE pa.place_id = p.id) as amenities_count
FROM places p
LEFT JOIN governorates g ON p.governorate_id = g.id
LEFT JOIN districts d ON p.district_id = d.id
LEFT JOIN place_categories pc ON p.category_id = pc.id
WHERE p.is_active = TRUE;

-- view للأماكن الشائعة
CREATE VIEW popular_places AS
SELECT 
    p.*,
    g.name_ar as governorate_name_ar,
    pc.name_ar as category_name_ar,
    pc.icon as category_icon,
    (SELECT image_url FROM place_images pi WHERE pi.place_id = p.id AND pi.image_type = 'main' LIMIT 1) as main_image
FROM places p
LEFT JOIN governorates g ON p.governorate_id = g.id
LEFT JOIN place_categories pc ON p.category_id = pc.id
WHERE p.is_active = TRUE 
AND p.rating >= 4.0 
AND p.reviews_count >= 50
ORDER BY p.rating DESC, p.reviews_count DESC;

-- view للمطاعم مع قوائم الطعام
CREATE VIEW restaurants_with_menu AS
SELECT 
    p.*,
    g.name_ar as governorate_name_ar,
    pc.name_ar as category_name_ar,
    (SELECT image_url FROM place_images pi WHERE pi.place_id = p.id AND pi.image_type = 'main' LIMIT 1) as main_image,
    (SELECT COUNT(*) FROM menu_categories mc WHERE mc.place_id = p.id) as menu_categories_count,
    (SELECT COUNT(*) FROM menu_categories mc 
     JOIN menu_items mi ON mc.id = mi.category_id 
     WHERE mc.place_id = p.id) as menu_items_count
FROM places p
LEFT JOIN governorates g ON p.governorate_id = g.id
LEFT JOIN place_categories pc ON p.category_id = pc.id
WHERE p.is_active = TRUE 
AND pc.name_en LIKE '%Restaurant%'
ORDER BY p.rating DESC;

-- إنشاء stored procedures مفيدة

-- البحث في الأماكن
DELIMITER //
CREATE PROCEDURE SearchPlaces(
    IN search_query VARCHAR(200),
    IN category_filter INT,
    IN governorate_filter INT,
    IN min_rating DECIMAL(2,1),
    IN limit_count INT
)
BEGIN
    SELECT 
        p.id,
        p.name_ar,
        p.name_en,
        p.description_ar,
        p.latitude,
        p.longitude,
        p.rating,
        p.reviews_count,
        p.price_range,
        g.name_ar as governorate_name_ar,
        pc.name_ar as category_name_ar,
        pc.icon as category_icon,
        (SELECT image_url FROM place_images pi WHERE pi.place_id = p.id AND pi.image_type = 'main' LIMIT 1) as main_image
    FROM places p
    LEFT JOIN governorates g ON p.governorate_id = g.id
    LEFT JOIN place_categories pc ON p.category_id = pc.id
    WHERE p.is_active = TRUE
    AND (search_query IS NULL OR 
         MATCH(p.name_ar, p.name_en, p.description_ar, p.description_en) AGAINST(search_query IN NATURAL LANGUAGE MODE))
    AND (category_filter IS NULL OR p.category_id = category_filter)
    AND (governorate_filter IS NULL OR p.governorate_id = governorate_filter)
    AND (min_rating IS NULL OR p.rating >= min_rating)
    ORDER BY p.rating DESC, p.reviews_count DESC
    LIMIT limit_count;
END //
DELIMITER ;

-- الحصول على الأماكن القريبة
DELIMITER //
CREATE PROCEDURE GetNearbyPlaces(
    IN user_lat DECIMAL(10,8),
    IN user_lng DECIMAL(11,8),
    IN radius_km INT,
    IN category_filter INT,
    IN limit_count INT
)
BEGIN
    SELECT 
        p.id,
        p.name_ar,
        p.name_en,
        p.latitude,
        p.longitude,
        p.rating,
        p.reviews_count,
        pc.name_ar as category_name_ar,
        pc.icon as category_icon,
        (SELECT image_url FROM place_images pi WHERE pi.place_id = p.id AND pi.image_type = 'main' LIMIT 1) as main_image,
        (6371 * acos(cos(radians(user_lat)) * cos(radians(p.latitude)) * 
         cos(radians(p.longitude) - radians(user_lng)) + 
         sin(radians(user_lat)) * sin(radians(p.latitude)))) AS distance_km
    FROM places p
    LEFT JOIN place_categories pc ON p.category_id = pc.id
    WHERE p.is_active = TRUE
    AND (category_filter IS NULL OR p.category_id = category_filter)
    HAVING distance_km <= radius_km
    ORDER BY distance_km ASC, p.rating DESC
    LIMIT limit_count;
END //
DELIMITER ;

-- تحديث تقييم المكان
DELIMITER //
CREATE PROCEDURE UpdatePlaceRating(IN place_id_param INT)
BEGIN
    DECLARE avg_rating DECIMAL(2,1);
    DECLARE total_reviews INT;
    
    SELECT AVG(rating), COUNT(*) 
    INTO avg_rating, total_reviews
    FROM reviews 
    WHERE place_id = place_id_param AND is_active = TRUE;
    
    UPDATE places 
    SET rating = COALESCE(avg_rating, 0.0), 
        reviews_count = total_reviews,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = place_id_param;
END //
DELIMITER ;

-- إنشاء triggers لتحديث التقييمات تلقائياً
DELIMITER //
CREATE TRIGGER update_rating_after_review_insert
AFTER INSERT ON reviews
FOR EACH ROW
BEGIN
    CALL UpdatePlaceRating(NEW.place_id);
END //

CREATE TRIGGER update_rating_after_review_update
AFTER UPDATE ON reviews
FOR EACH ROW
BEGIN
    CALL UpdatePlaceRating(NEW.place_id);
END //

CREATE TRIGGER update_rating_after_review_delete
AFTER DELETE ON reviews
FOR EACH ROW
BEGIN
    CALL UpdatePlaceRating(OLD.place_id);
END //
DELIMITER ;

-- إنشاء مستخدم للتطبيق
CREATE USER IF NOT EXISTS 'yemen_gps_user'@'localhost' IDENTIFIED BY 'yemen_gps_2024!';
GRANT SELECT, INSERT, UPDATE, DELETE ON yemen_gps_db.* TO 'yemen_gps_user'@'localhost';
GRANT EXECUTE ON yemen_gps_db.* TO 'yemen_gps_user'@'localhost';
FLUSH PRIVILEGES;

-- إعدادات الأمان والأداء
SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET GLOBAL innodb_buffer_pool_size = 268435456; -- 256MB
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL max_connections = 200;

-- رسالة النجاح
SELECT 'تم إعداد قاعدة البيانات بنجاح - Database setup completed successfully' as status;
