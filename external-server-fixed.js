#!/usr/bin/env node
/**
 * 🗺️ خادم خرائط اليمن الخارجي المحسن - HTTPS
 * Enhanced Yemen Maps External Server - HTTPS
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const config = {
    httpsPort: 8443,
    httpPort: 8080,
    host: '0.0.0.0',
    domain: 'yemenmaps.com',
    ssl: {
        key: "D:/yemen-maps/ssl/yemenmaps.com.key",
        cert: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.crt",
        ca: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.ca-bundle"
    },
    templatesPath: "D:/yemen-maps/templates",
    staticPaths: {
        "/templates": "D:/yemen-maps/templates",
        "/tiles": "D:/yemen-maps/templates/yemen",
        "/fonts": "D:/yemen-maps/templates/fonts",
        "/js": "D:/yemen-maps/js",
        "/public": "D:/yemen-maps/public",
        "/static": "D:/yemen-maps/static"
    }
};

let stats = {
    total: 0,
    external: 0,
    https: 0,
    http: 0,
    startTime: new Date()
};

// فحص ملفات SSL
function checkSSLFiles() {
    console.log('🔐 فحص ملفات SSL...');
    
    const files = [config.ssl.key, config.ssl.cert, config.ssl.ca];
    const missing = [];
    
    files.forEach(file => {
        if (!fs.existsSync(file)) {
            missing.push(file);
        } else {
            console.log(`✅ موجود: ${path.basename(file)}`);
        }
    });
    
    if (missing.length > 0) {
        console.log('❌ ملفات SSL مفقودة:');
        missing.forEach(file => console.log(`   - ${file}`));
        return false;
    }
    
    console.log('✅ جميع ملفات SSL موجودة');
    return true;
}

// دالة تسجيل الطلبات
function logRequest(req, res, protocol = 'HTTPS') {
    const timestamp = new Date().toISOString();
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);
    
    const isExternal = !clientIP || (!clientIP.includes('127.0.0.1') && !clientIP.includes('::1') && !clientIP.includes('localhost'));
    
    stats.total++;
    if (isExternal) stats.external++;
    if (protocol === 'HTTPS') stats.https++;
    if (protocol === 'HTTP') stats.http++;
    
    console.log(`[${timestamp}] ${protocol} ${req.method} ${req.url} - ${clientIP || 'unknown'} ${isExternal ? '(خارجي)' : '(محلي)'}`);
}

// دالة معالجة الطلبات
function handleRequest(req, res, protocol = 'HTTPS') {
    logRequest(req, res, protocol);
    
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // الصفحة الرئيسية
    if (pathname === '/') {
        const indexPath = path.join(config.templatesPath, 'index.html');
        if (fs.existsSync(indexPath)) {
            try {
                const content = fs.readFileSync(indexPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة index.html:', error.message);
            }
        }
    }
    
    // صفحة الإدارة
    if (pathname === '/admin') {
        const adminPath = path.join(config.templatesPath, 'admin.html');
        if (fs.existsSync(adminPath)) {
            try {
                const content = fs.readFileSync(adminPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة admin.html:', error.message);
            }
        }
    }
    
    // API للحالة
    if (pathname === '/api/status') {
        const status = {
            server: "Yemen Maps HTTPS Server Enhanced",
            status: "running",
            protocol: protocol,
            httpsPort: config.httpsPort,
            httpPort: config.httpPort,
            domain: config.domain,
            ssl: protocol === 'HTTPS',
            stats: {
                total_requests: stats.total,
                external_requests: stats.external,
                https_requests: stats.https,
                http_requests: stats.http,
                uptime_seconds: Math.floor((Date.now() - stats.startTime.getTime()) / 1000)
            },
            timestamp: new Date().toISOString()
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify(status, null, 2));
        return;
    }
    
    // إعادة توجيه HTTP إلى HTTPS
    if (protocol === 'HTTP' && pathname !== '/api/status') {
        const httpsUrl = `https://${config.domain}:${config.httpsPort}${req.url}`;
        res.writeHead(301, { 'Location': httpsUrl });
        res.end(`Redirecting to HTTPS: ${httpsUrl}`);
        return;
    }
    
    // خدمة الملفات الثابتة
    for (const [route, localPath] of Object.entries(config.staticPaths)) {
        if (pathname.startsWith(route)) {
            const relativePath = pathname.substring(route.length);
            const filePath = path.join(localPath, relativePath);
            
            if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
                try {
                    const content = fs.readFileSync(filePath);
                    const ext = path.extname(filePath).toLowerCase();
                    const contentType = getContentType(ext);
                    
                    res.writeHead(200, { 'Content-Type': contentType });
                    res.end(content);
                    return;
                } catch (error) {
                    console.log(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
                }
            }
        }
    }
    
    // صفحة 404
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>404 - الصفحة غير موجودة</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                h1 { color: #e74c3c; }
                a { color: #3498db; text-decoration: none; }
            </style>
        </head>
        <body>
            <h1>404 - الصفحة غير موجودة</h1>
            <p>الصفحة المطلوبة غير متاحة</p>
            <a href="/">العودة للصفحة الرئيسية</a>
        </body>
        </html>
    `);
}

// تحديد نوع المحتوى
function getContentType(ext) {
    const types = {
        '.html': 'text/html; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.js': 'application/javascript; charset=utf-8',
        '.json': 'application/json; charset=utf-8',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject'
    };
    return types[ext] || 'application/octet-stream';
}

// بدء الخوادم
function startServers() {
    console.log('🚀 بدء خوادم خرائط اليمن المحسنة...');
    console.log('=' * 50);
    
    // فحص ملفات SSL
    if (!checkSSLFiles()) {
        console.log('❌ لا يمكن بدء خادم HTTPS بدون ملفات SSL صحيحة');
        console.log('🔄 سيتم بدء خادم HTTP فقط...');
        
        // خادم HTTP فقط
        const httpServer = http.createServer((req, res) => {
            handleRequest(req, res, 'HTTP');
        });
        
        httpServer.listen(config.httpPort, config.host, () => {
            console.log(`🌐 خادم HTTP يعمل على: http://${config.domain}:${config.httpPort}`);
            console.log(`🏠 الوصول المحلي: http://localhost:${config.httpPort}`);
        });
        
        return;
    }
    
    try {
        // قراءة ملفات SSL
        const sslOptions = {
            key: fs.readFileSync(config.ssl.key),
            cert: fs.readFileSync(config.ssl.cert),
            ca: fs.readFileSync(config.ssl.ca)
        };
        
        // خادم HTTPS
        const httpsServer = https.createServer(sslOptions, (req, res) => {
            handleRequest(req, res, 'HTTPS');
        });
        
        httpsServer.listen(config.httpsPort, config.host, () => {
            console.log(`🔒 خادم HTTPS يعمل على: https://${config.domain}:${config.httpsPort}`);
            console.log(`🏠 الوصول المحلي: https://localhost:${config.httpsPort}`);
        });
        
        // خادم HTTP للإعادة توجيه
        const httpServer = http.createServer((req, res) => {
            handleRequest(req, res, 'HTTP');
        });
        
        httpServer.listen(config.httpPort, config.host, () => {
            console.log(`🔄 خادم HTTP (إعادة توجيه) يعمل على: http://${config.domain}:${config.httpPort}`);
        });
        
        console.log('=' * 50);
        console.log('✅ جميع الخوادم تعمل بنجاح!');
        console.log(`📊 إحصائيات: http://localhost:${config.httpsPort}/api/status`);
        
    } catch (error) {
        console.error('❌ خطأ في بدء خادم HTTPS:', error.message);
        console.log('🔄 محاولة بدء خادم HTTP فقط...');
        
        // خادم HTTP كبديل
        const httpServer = http.createServer((req, res) => {
            handleRequest(req, res, 'HTTP');
        });
        
        httpServer.listen(config.httpPort, config.host, () => {
            console.log(`🌐 خادم HTTP (بديل) يعمل على: http://${config.domain}:${config.httpPort}`);
        });
    }
}

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخوادم...');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error.message);
});

// بدء الخوادم
startServers();
