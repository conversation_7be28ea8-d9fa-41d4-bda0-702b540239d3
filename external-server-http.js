#!/usr/bin/env node
/**
 * 🗺️ خادم خرائط اليمن الخارجي - HTTP البديل
 * Yemen Maps External Server - HTTP Alternative
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const config = {
    port: 8080,
    host: '0.0.0.0',
    domain: 'yemenmaps.com',
    templatesPath: "D:/yemen-maps/templates",
    staticPaths: {
        "/templates": "D:/yemen-maps/templates",
        "/tiles": "D:/yemen-maps/templates/yemen",
        "/fonts": "D:/yemen-maps/templates/fonts",
        "/js": "D:/yemen-maps/js",
        "/public": "D:/yemen-maps/public",
        "/static": "D:/yemen-maps/static"
    }
};

let stats = {
    total: 0,
    external: 0,
    startTime: new Date()
};

// دالة تسجيل الطلبات
function logRequest(req, res) {
    const timestamp = new Date().toISOString();
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.headers['x-real-ip'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);
    
    const isExternal = !clientIP || (!clientIP.includes('127.0.0.1') && !clientIP.includes('::1') && !clientIP.includes('localhost'));
    
    stats.total++;
    if (isExternal) stats.external++;
    
    console.log(`[${timestamp}] HTTP ${req.method} ${req.url} - ${clientIP || 'unknown'} ${isExternal ? '(خارجي)' : '(محلي)'}`);
}

// دالة معالجة الطلبات
function handleRequest(req, res) {
    logRequest(req, res);
    
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // الصفحة الرئيسية
    if (pathname === '/') {
        const indexPath = path.join(config.templatesPath, 'index.html');
        if (fs.existsSync(indexPath)) {
            try {
                const content = fs.readFileSync(indexPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة index.html:', error.message);
            }
        }
    }
    
    // صفحة الإدارة
    if (pathname === '/admin') {
        const adminPath = path.join(config.templatesPath, 'admin.html');
        if (fs.existsSync(adminPath)) {
            try {
                const content = fs.readFileSync(adminPath);
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(content);
                return;
            } catch (error) {
                console.log('❌ خطأ في قراءة admin.html:', error.message);
            }
        }
    }
    
    // API للحالة
    if (pathname === '/api/status') {
        const status = {
            server: "Yemen Maps HTTP Server",
            status: "running",
            protocol: "HTTP",
            port: config.port,
            domain: config.domain,
            ssl: false,
            stats: {
                total_requests: stats.total,
                external_requests: stats.external,
                uptime_seconds: Math.floor((Date.now() - stats.startTime.getTime()) / 1000)
            },
            timestamp: new Date().toISOString(),
            note: "HTTP alternative server - no SSL required"
        };
        
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify(status, null, 2));
        return;
    }
    
    // خدمة الملفات الثابتة
    for (const [route, localPath] of Object.entries(config.staticPaths)) {
        if (pathname.startsWith(route)) {
            const relativePath = pathname.substring(route.length);
            const filePath = path.join(localPath, relativePath);
            
            if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
                try {
                    const content = fs.readFileSync(filePath);
                    const ext = path.extname(filePath).toLowerCase();
                    const contentType = getContentType(ext);
                    
                    res.writeHead(200, { 'Content-Type': contentType });
                    res.end(content);
                    return;
                } catch (error) {
                    console.log(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
                }
            }
        }
    }
    
    // صفحة 404
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>404 - الصفحة غير موجودة</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                h1 { color: #e74c3c; margin-bottom: 20px; }
                p { color: #666; margin-bottom: 30px; }
                a { color: #3498db; text-decoration: none; background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; display: inline-block; }
                a:hover { background: #2980b9; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>404 - الصفحة غير موجودة</h1>
                <p>الصفحة المطلوبة غير متاحة على الخادم</p>
                <a href="/">العودة للصفحة الرئيسية</a>
            </div>
        </body>
        </html>
    `);
}

// تحديد نوع المحتوى
function getContentType(ext) {
    const types = {
        '.html': 'text/html; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.js': 'application/javascript; charset=utf-8',
        '.json': 'application/json; charset=utf-8',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.woff': 'font/woff',
        '.woff2': 'font/woff2',
        '.ttf': 'font/ttf',
        '.eot': 'application/vnd.ms-fontobject'
    };
    return types[ext] || 'application/octet-stream';
}

// بدء الخادم
const server = http.createServer(handleRequest);

server.listen(config.port, config.host, () => {
    console.log('🚀 بدء خادم خرائط اليمن HTTP البديل...');
    console.log('=' * 50);
    console.log(`🌐 خادم HTTP يعمل على: http://${config.domain}:${config.port}`);
    console.log(`🏠 الوصول المحلي: http://localhost:${config.port}`);
    console.log(`📊 حالة الخادم: http://localhost:${config.port}/api/status`);
    console.log('=' * 50);
    console.log('✅ الخادم جاهز للاستخدام!');
    console.log('💡 هذا خادم HTTP بديل بدون SSL');
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error.message);
});
