#!/usr/bin/env node
/**
 * 🔒 خادم HTTPS النهائي - خرائط اليمن
 * Final HTTPS Server - Yemen Maps
 */

const https = require('https');
const fs = require('fs');

const config = {
    port: 8443,
    host: '0.0.0.0',
    domain: 'yemenmaps.com',
    ssl: {
        key: "D:/yemen-maps/ssl/yemenmaps.com.key",
        cert: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.crt",
        ca: "D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.ca-bundle"
    }
};

let stats = {
    total: 0,
    external: 0,
    startTime: new Date()
};

// دالة تسجيل الطلبات
function logRequest(req, res) {
    const timestamp = new Date().toISOString();
    const clientIP = req.headers['x-forwarded-for'] ||
                     req.headers['x-real-ip'] ||
                     req.connection.remoteAddress ||
                     'unknown';

    const isExternal = !clientIP.startsWith('127.') &&
                      !clientIP.startsWith('192.168.') &&
                      !clientIP.startsWith('10.') &&
                      clientIP !== '::1' &&
                      clientIP !== 'unknown';

    stats.total++;
    if (isExternal) stats.external++;

    const indicator = isExternal ? '🌍 EXTERNAL' : '🏠 LOCAL';
    console.log(`${indicator} HTTPS ${timestamp} | ${clientIP} | ${req.method} ${req.url}`);
    console.log(`   Host: ${req.headers.host}`);
    console.log(`   User-Agent: ${(req.headers['user-agent'] || '').substring(0, 60)}...`);

    if (isExternal) {
        console.log('🚨🚨🚨 EXTERNAL HTTPS REQUEST DETECTED! 🚨🚨🚨');
        console.log('🎉 SUCCESS! HTTPS Domain access working!');

        // كتابة في ملف السجل
        if (!fs.existsSync('logs')) {
            fs.mkdirSync('logs', { recursive: true });
        }

        const logEntry = {
            timestamp,
            clientIP,
            method: req.method,
            url: req.url,
            host: req.headers.host,
            userAgent: req.headers['user-agent']
        };

        fs.appendFileSync('logs/external-https-success.log', JSON.stringify(logEntry) + '\n');
    }

    console.log('=====================================');
}

// دالة معالجة الطلبات
function handleRequest(req, res) {
    logRequest(req, res);

    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const isExternal = !clientIP.startsWith('127.') &&
                      !clientIP.startsWith('192.168.') &&
                      !clientIP.startsWith('10.') &&
                      clientIP !== '::1';

    res.writeHead(200);
    res.end(`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن - HTTPS آمن</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 900px;
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #FFD700;
            margin-bottom: 30px;
            font-size: 3em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .success {
            background: rgba(255, 215, 0, 0.3);
            color: #FFD700;
            font-size: 26px;
            margin: 20px 0;
            padding: 30px;
            border-radius: 15px;
            border: 3px solid #FFD700;
            animation: ${isExternal ? 'pulse 2s infinite' : 'none'};
        }
        .info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
            text-align: left;
        }
        .emoji { font-size: 1.5em; }
        .ssl-badge {
            background: rgba(0, 184, 148, 0.4);
            border: 2px solid #00b894;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            display: inline-block;
            font-size: 18px;
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7); }
            70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 215, 0, 0); }
            100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 215, 0, 0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🗺️</span> خرائط اليمن</h1>

        <div class="ssl-badge">
            <span class="emoji">🔒</span> اتصال آمن ومشفر - SSL نشط
        </div>

        <div class="success">
            <span class="emoji">${isExternal ? '🌍🎉' : '🏠🔒'}</span>
            ${isExternal ? 'نجح الوصول الخارجي عبر HTTPS!' : 'اتصال HTTPS محلي آمن'}
        </div>

        ${isExternal ? `
        <div class="success" style="background: rgba(255, 0, 0, 0.3); border-color: #FF6B6B; color: #FF6B6B;">
            <span class="emoji">🎯🔥</span> مبروك! الدومين يعمل بنجاح!<br>
            <strong>https://yemenmaps.com:8443</strong><br>
            الوصول الخارجي عبر HTTPS يعمل بشكل مثالي!
        </div>
        ` : ''}

        <div class="info">
            <h3><span class="emoji">🔐</span> معلومات الاتصال الآمن:</h3>
            <p><strong>البروتوكول:</strong> HTTPS (مشفر بالكامل)</p>
            <p><strong>المنفذ:</strong> ${config.port}</p>
            <p><strong>الشهادة:</strong> صالحة ونشطة ✅</p>
            <p><strong>عنوان IP:</strong> ${clientIP}</p>
            <p><strong>نوع الاتصال:</strong> ${isExternal ? 'خارجي 🌍' : 'محلي 🏠'}</p>
            <p><strong>Host Header:</strong> ${req.headers.host}</p>
            <p><strong>الوقت:</strong> ${new Date().toLocaleString('ar-EG')}</p>
        </div>

        <div class="info">
            <h3><span class="emoji">📈</span> إحصائيات الخادم:</h3>
            <p><strong>إجمالي الطلبات:</strong> ${stats.total}</p>
            <p><strong>الطلبات الخارجية:</strong> ${stats.external}</p>
            <p><strong>وقت البدء:</strong> ${stats.startTime.toLocaleString('ar-EG')}</p>
            <p><strong>مدة التشغيل:</strong> ${Math.floor((Date.now() - stats.startTime.getTime()) / 1000)} ثانية</p>
        </div>

        <div class="info">
            <h3><span class="emoji">🔗</span> الروابط الآمنة:</h3>
            <p><strong>الدومين الآمن:</strong> <a href="https://${config.domain}:${config.port}" style="color: #87CEEB;">https://${config.domain}:${config.port}</a></p>
            <p><strong>API الحالة:</strong> <a href="/api/status" style="color: #87CEEB;">/api/status</a></p>
        </div>

        ${isExternal ? `
        <div class="success" style="background: rgba(0, 255, 0, 0.2); border-color: #00FF00; color: #00FF00;">
            <span class="emoji">✅🎊</span> تم حل مشكلة الشهادة بنجاح!<br>
            الآن يمكن الوصول للموقع من أي مكان في العالم!
        </div>
        ` : ''}
    </div>
</body>
</html>
    `);
}

// إنشاء خادم HTTPS
console.log('🔒 بدء خادم HTTPS النهائي - خرائط اليمن');
console.log('=====================================');

try {
    console.log('🔍 فحص شهادات SSL...');

    if (!fs.existsSync(config.ssl.key)) {
        throw new Error('المفتاح الخاص غير موجود');
    }

    if (!fs.existsSync(config.ssl.cert)) {
        throw new Error('الشهادة غير موجودة');
    }

    console.log('✅ المفتاح الخاص موجود');
    console.log('✅ الشهادة موجودة');

    const sslOptions = {
        key: fs.readFileSync(config.ssl.key),
        cert: fs.readFileSync(config.ssl.cert)
    };

    // إضافة CA bundle إذا كان موجوداً
    if (fs.existsSync(config.ssl.ca)) {
        console.log('✅ إضافة CA bundle');
        sslOptions.ca = fs.readFileSync(config.ssl.ca);
    }

    const httpsServer = https.createServer(sslOptions, handleRequest);

    httpsServer.listen(config.port, config.host, () => {
        console.log('🔒 خادم HTTPS يعمل بنجاح على:');
        console.log(`   الدومين: https://${config.domain}:${config.port}`);
        console.log(`   المحلي: https://localhost:${config.port}`);
        console.log('');
        console.log('🎯 جرب الآن: https://yemenmaps.com:8443');
        console.log('');
        console.log('🔍 مراقبة الطلبات نشطة...');
        console.log('🚨 سيتم تمييز الطلبات الخارجية بوضوح!');
        console.log('=====================================');
    });

    httpsServer.on('error', (err) => {
        console.log('❌ خطأ في خادم HTTPS:', err.message);
    });

} catch (error) {
    console.log('❌ خطأ في إنشاء خادم HTTPS:', error.message);
    process.exit(1);
}

// معالجة الإيقاف
process.on('SIGINT', () => {
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   إجمالي الطلبات: ${stats.total}`);
    console.log(`   الطلبات الخارجية: ${stats.external}`);
    console.log('\n🛑 إيقاف خادم HTTPS...');
    process.exit(0);
});
