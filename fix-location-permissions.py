#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة صلاحيات الموقع في النسخة المتميزة
"""

import re

def fix_location_permissions():
    print("🔧 إصلاح مشكلة صلاحيات الموقع...")
    
    # قراءة النسخة المتميزة
    with open('templates/index-premium-local.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إضافة كود محسن لصلاحيات الموقع
    location_fix = '''
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    '''
    
    # إضافة الكود المحسن قبل نهاية الـ script
    content = content.replace('</script>', location_fix + '\n</script>')
    
    # تحديث دالة زر الموقع
    content = content.replace(
        'onclick="getCurrentLocation()"',
        'onclick="getCurrentLocationFixed()"'
    )
    
    # حفظ النسخة المحسنة
    with open('templates/index-premium-local-fixed.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إنشاء templates/index-premium-local-fixed.html")
    print("🔧 تم إصلاح مشكلة صلاحيات الموقع")
    print("🌐 الرابط: http://localhost:5000/index-premium-local-fixed.html")

if __name__ == "__main__":
    fix_location_permissions()
