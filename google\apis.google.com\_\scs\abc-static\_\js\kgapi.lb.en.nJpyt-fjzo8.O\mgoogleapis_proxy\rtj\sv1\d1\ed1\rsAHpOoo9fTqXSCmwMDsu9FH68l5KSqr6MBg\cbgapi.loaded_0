gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ha,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.l2=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.l2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Aa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.Af=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.zP=function(h){if(this.Af==null){this.Af=[];var k=this;this.AP(function(){k.V8()})}this.Af.push(h)};var d=_.na.setTimeout;b.prototype.AP=function(h){d(h,0)};b.prototype.V8=function(){for(;this.Af&&this.Af.length;){var h=this.Af;this.Af=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.Af=null};b.prototype.Yp=function(h){this.AP(function(){throw h;
})};var e=function(h){this.Ca=0;this.qf=void 0;this.Fr=[];this.aW=!1;var k=this.BF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.BF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.dfa),reject:h(this.qK)}};e.prototype.dfa=function(h){if(h===this)this.qK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Iga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.cfa(h):this.XS(h)}};e.prototype.cfa=function(h){var k=void 0;try{k=h.then}catch(l){this.qK(l);return}typeof k=="function"?this.Jga(k,h):this.XS(h)};e.prototype.qK=function(h){this.Y_(2,h)};e.prototype.XS=function(h){this.Y_(1,h)};e.prototype.Y_=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.qf=k;this.Ca===2&&this.sfa();this.W8()};e.prototype.sfa=function(){var h=this;d(function(){if(h.pda()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.qf)}},
1)};e.prototype.pda=function(){if(this.aW)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.qf;return l(h)};e.prototype.W8=function(){if(this.Fr!=null){for(var h=0;h<this.Fr.length;++h)f.zP(this.Fr[h]);
this.Fr=null}};var f=new b;e.prototype.Iga=function(h){var k=this.BF();h.wy(k.resolve,k.reject)};e.prototype.Jga=function(h,k){var l=this.BF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.wy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.wy=function(h,k){function l(){switch(m.Ca){case 1:h(m.qf);
break;case 2:k(m.qf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Fr==null?f.zP(l):this.Fr.push(l);this.aW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Aa(h),n=m.next();!n.done;n=m.next())c(n.value).wy(k,l)})};e.all=function(h){var k=_.Aa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).wy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Aa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Aa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Aa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.Ve?m.Ve.value=l:(m.Ve={next:this[1],Lk:this[1].Lk,head:this[1],key:k,value:l},m.list.push(m.Ve),this[1].Lk.next=m.Ve,this[1].Lk=m.Ve,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.Ve&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.Ve.Lk.next=k.Ve.next,k.Ve.next.Lk=
k.Ve.Lk,k.Ve.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Lk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).Ve};c.prototype.get=function(k){return(k=d(this,k).Ve)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,Ve:p}}return{id:m,list:n,index:-1,Ve:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Lk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Lk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Aa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Aa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{aV:e,SD:f}}return{aV:-1,SD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).SD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).aV}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.mt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.rZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&4096),Pb=!!(_.$a[0]&8192),Qb=!!(_.$a[0]&16),Rb=!!(_.$a[0]>>15&1);_.Sb=Ob?Pb:xb(610401301);_.Ub=Ob?Qb:xb(**********);_.Vb=Ob?Rb:xb(651175828);_.Wb=function(a){_.Wb[" "](a);return a};_.Wb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,pc,Ac,Lc,Zc,id;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.NY;throw Error("j");};_.hc=function(a){return new _.gc(a)};_.jc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.ic(a)};_.kc=function(a){if(a instanceof _.ic)return a.OY;throw Error("j");};_.mc=function(a){return a instanceof _.lc};_.nc=function(a){if(_.mc(a))return a.QY;throw Error("j");};pc=function(a){return new _.oc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.rc=function(a){if(qc.test(a))return a};
_.sc=function(a){return a instanceof _.lc?_.nc(a):_.rc(a)};_.tc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.msa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.rea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.nsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.vc=function(a){var b=_.uc.apply(1,arguments);if(b.length===0)return _.jc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.jc(c)};_.wc=function(a,b){return a.lastIndexOf(b,0)==0};_.xc=function(a){return/^[\s\xa0]*$/.test(a)};_.yc=function(a,b){return a.indexOf(b)!=-1};
_.Dc=function(a,b){var c=0;a=(0,_.zc)(String(a)).split(".");b=(0,_.zc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Ac(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Ac(f[2].length==0,h[2].length==0)||Ac(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Ac=function(a,b){return a<b?-1:a>b?1:0};_.Ec=function(a,b){b=_.sc(b);b!==void 0&&(a.href=b)};_.Fc=function(a,b,c,d){b=_.sc(b);return b!==void 0?a.open(b,c,d):null};_.Gc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Hc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Jc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Lc=function(a){if(!_.Sb||!_.Kc)return!1;for(var b=0;b<_.Kc.brands.length;b++){var c=_.Kc.brands[b].brand;if(c&&_.yc(c,a))return!0}return!1};_.Mc=function(a){return _.yc(_.Jc(),a)};_.Nc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Oc=function(){return _.Sb?!!_.Kc&&_.Kc.brands.length>0:!1};_.Pc=function(){return _.Oc()?!1:_.Mc("Opera")};
_.Qc=function(){return _.Oc()?!1:_.Mc("Trident")||_.Mc("MSIE")};_.Sc=function(){return _.Oc()?!1:_.Mc("Edge")};_.Tc=function(){return _.Oc()?Lc("Microsoft Edge"):_.Mc("Edg/")};_.Uc=function(){return _.Oc()?Lc("Opera"):_.Mc("OPR")};_.Vc=function(){return _.Mc("Firefox")||_.Mc("FxiOS")};_.Wc=function(){return _.Oc()?Lc("Chromium"):(_.Mc("Chrome")||_.Mc("CriOS"))&&!_.Sc()||_.Mc("Silk")};
_.Xc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.Yc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
Zc=function(){return _.Sb?!!_.Kc&&!!_.Kc.platform:!1};_.$c=function(){return Zc()?_.Kc.platform==="Android":_.Mc("Android")};_.ad=function(){return _.Mc("iPhone")&&!_.Mc("iPod")&&!_.Mc("iPad")};_.bd=function(){return _.ad()||_.Mc("iPad")||_.Mc("iPod")};_.cd=function(){return Zc()?_.Kc.platform==="macOS":_.Mc("Macintosh")};_.dd=function(){return Zc()?_.Kc.platform==="Windows":_.Mc("Windows")};_.ed=function(){return Zc()?_.Kc.platform==="Chrome OS":_.Mc("CrOS")};
_.fd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.gd=function(a){return _.fd(a,a)};_.uc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.jd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.kd=function(a){var b=_.jd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.ld=function(){return Date.now()};var md=globalThis.trustedTypes,$b=md,bc;_.dc=function(a){this.NY=a};_.dc.prototype.toString=function(){return this.NY+""};_.nd=function(){return new _.dc(md?md.emptyHTML:"")}();_.gc=function(a){this.PY=a};_.gc.prototype.toString=function(){return this.PY};_.ic=function(a){this.OY=a};_.ic.prototype.toString=function(){return this.OY+""};_.lc=function(a){this.QY=a};_.lc.prototype.toString=function(){return this.QY};_.od=new _.lc("about:invalid#zClosurez");var qc;_.oc=function(a){this.xj=a};_.pd=[pc("data"),pc("http"),pc("https"),pc("mailto"),pc("ftp"),new _.oc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.qd=function(){return typeof URL==="function"}();qc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.rd=function(a,b){this.width=a;this.height=b};_.sd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.rd.prototype;_.g.clone=function(){return new _.rd(this.width,this.height)};_.g.by=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.by()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.zc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.td=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.ud=Math.random()*2147483648|0;var vd;vd=_.Xa.navigator;_.Kc=vd?vd.userAgentData||null:null;var Nd,Od,Wd;_.xd=_.Pc();_.yd=_.Qc();_.zd=_.Mc("Edge");_.Ad=_.zd||_.yd;_.Bd=_.Mc("Gecko")&&!(_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge"))&&!(_.Mc("Trident")||_.Mc("MSIE"))&&!_.Mc("Edge");_.Cd=_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge");_.Dd=_.Cd&&_.Mc("Mobile");_.Ed=_.cd();_.Fd=_.dd();_.Gd=(Zc()?_.Kc.platform==="Linux":_.Mc("Linux"))||_.ed();_.Id=_.$c();_.Jd=_.ad();_.Kd=_.Mc("iPad");_.Ld=_.Mc("iPod");_.Md=_.bd();Nd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Pd="",Td=function(){var a=_.Jc();if(_.Bd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.zd)return/Edge\/([\d\.]+)/.exec(a);if(_.yd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/WebKit\/(\S+)/.exec(a);if(_.xd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Td&&(Pd=Td?Td[1]:"");if(_.yd){var Ud=Nd();if(Ud!=null&&Ud>parseFloat(Pd)){Od=String(Ud);break a}}Od=Pd}_.Vd=Od;if(_.Xa.document&&_.yd){var Xd=Nd();Wd=Xd?Xd:parseInt(_.Vd,10)||void 0}else Wd=void 0;_.Yd=Wd;var de,ke,je;_.ae=function(a){return a?new _.Zd(_.$d(a)):id||(id=new _.Zd)};_.be=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.ce=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.ee=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:de.hasOwnProperty(d)?a.setAttribute(de[d],c):_.wc(d,"aria-")||_.wc(d,"data-")?a.setAttribute(d,c):a[d]=c})};de={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ge=function(a){return _.fe(a||window)};
_.fe=function(a){a=a.document;a=_.he(a)?a.documentElement:a.body;return new _.rd(a.clientWidth,a.clientHeight)};_.ie=function(a){return a?a.defaultView:window};_.le=function(a,b){var c=b[1],d=je(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.ee(d,c));b.length>2&&ke(a,d,b,2);return d};
ke=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.kd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Yb(f):f,e)}}};_.me=function(a){return je(document,a)};
je=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.he=function(a){return a.compatMode=="CSS1Compat"};_.ne=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.oe=function(a,b){ke(_.$d(a),a,arguments,1)};_.pe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.qe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.re=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.se=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.te=function(a){return _.vb(a)&&a.nodeType==1};
_.ue=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.$d=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.ve=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.pe(a),a.appendChild(_.$d(a).createTextNode(String(b)))};_.Zd=function(a){this.Bc=a||_.Xa.document||document};_.g=_.Zd.prototype;_.g.Ha=_.ae;_.g.uL=_.jb(0);_.g.ub=function(){return this.Bc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Bc).getElementsByTagName(String(a))};
_.g.uH=_.jb(2);_.g.wa=function(a,b,c){return _.le(this.Bc,arguments)};_.g.createElement=function(a){return je(this.Bc,a)};_.g.createTextNode=function(a){return this.Bc.createTextNode(String(a))};_.g.getWindow=function(){return this.Bc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.oe;_.g.canHaveChildren=_.ne;_.g.ne=_.pe;_.g.xV=_.qe;_.g.removeNode=_.re;_.g.EG=_.se;_.g.isElement=_.te;_.g.contains=_.ue;_.g.XG=_.$d;_.g.vj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.we=function(a){return a===null?"null":a===void 0?"undefined":a};_.xe=window;_.ye=document;_.ze=_.xe.location;_.Ae=/\[native code\]/;_.Be=function(a,b,c){return a[b]=a[b]||c};_.Ce=function(){var a;if((a=Object.create)&&_.Ae.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.De=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Ee=function(a,b){a=a||{};for(var c in a)_.De(a,c)&&(b[c]=a[c])};_.Fe=_.Be(_.xe,"gapi",{});_.Ge=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.He=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ie=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Je=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Le=function(a,b,c){_.Ke(a,b,c,"add","at")};_.Ke=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Me={};_.Me=_.Be(_.xe,"___jsl",_.Ce());_.Be(_.Me,"I",0);_.Be(_.Me,"hel",10);var Ne,Pe,Qe,Re,Ue,Se,Te,Ve,We;Ne=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Pe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Qe=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Re=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Qe(a[d])&&!Qe(b[d])?Re(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Qe(b[d])?[]:{},Re(a[d],b[d])):a[d]=b[d])};
Ue=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ne("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Se())if(e=Te(c),d.push(25),typeof e===
"object")return e;return{}}};Se=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Te=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Re(c,b);a.push(c)};
We=function(a){Pe(!0);var b=window.___gcfg,c=Ne("cu"),d=window.___gu;b&&b!==d&&(Ve(c,b),window.___gu=b);b=Ne("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ne("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Ue(f,h))&&b.push(f));a&&Ve(c,a);d=Ne("cd");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);d=Ne("ci");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Re(Pe(),c[a],!0)};_.Xe=function(a,b){var c=Pe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ye=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;We(c)};var Ze=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Be(_.Me,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Ze&&Ze();We();_.t("gapi.config.get",_.Xe);_.t("gapi.config.update",_.Ye);
_.$e=function(a){a=_.we(a);return _.ec(a)};
_.Lg=window.googleapis&&window.googleapis.server||{};
_.af=_.af||{};
_.af=_.af||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.af.Rg=a;a()})();_.t("gadgets.util.getUrlParameters",_.af.Rg);
_.ef=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ye(a());return{register:function(b,c,d){d&&d(_.Xe())},get:function(b){return _.Xe(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ye(b)},init:function(){}}}();_.t("gadgets.config.register",_.ef.register);_.t("gadgets.config.get",_.ef.get);_.t("gadgets.config.init",_.ef.init);_.t("gadgets.config.update",_.ef.update);
var ff,gf,hf,jf,lf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Nf,Of,Pf,Sf,Tf;hf=void 0;jf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};lf=function(a){return Object.prototype.toString.call(a)};nf=lf(0);of=lf(new Date(0));pf=lf(!0);qf=lf("");rf=lf({});sf=lf([]);
tf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=lf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==sf||a.constructor!==Array&&a.constructor!==Object)&&(e!==rf||a.constructor!==Array&&a.constructor!==Object)&&e!==qf&&e!==nf&&e!==pf&&e!==of))return tf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===nf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===pf)b[b.length]=String(!!Number(a));else{if(e===of)return tf(a.toISOString.call(a),c);if(e===sf&&lf(a.length)===nf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=tf(a[f],c)||"null";b[b.length]="]"}else if(e==qf&&lf(a.length)===nf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=tf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=tf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};uf=/[\0-\x07\x0b\x0e-\x1f]/;
vf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;wf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;yf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;zf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Af=/[ \t\n\r]+/g;Bf=/[^"]:/;Df=/""/g;Gf=/true|false|null/g;Hf=/00/;If=/[\{]([^0\}]|0[^:])/;Jf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Kf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(uf.test(a)||vf.test(a)||wf.test(a)||xf.test(a))return!1;var b=a.replace(yf,'""');b=b.replace(zf,"0");b=b.replace(Af,"");if(Bf.test(b))return!1;b=b.replace(Df,"0");b=b.replace(Gf,"0");if(Hf.test(b)||If.test(b)||Jf.test(b)||Kf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=hf?[jf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((ff===void 0||hf===void 0||gf!==a)&&gf!==-1){ff=hf=!1;gf=-1;try{try{hf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&jf("true")===!0&&jf('[{"a":3}]')[0].a===3}catch(b){}ff=hf&&!jf("[00]")&&!jf('"\u0007"')&&!jf('"\\0"')&&!jf('"\\v"')}finally{gf=a}}};_.Qf=function(a){if(gf===-1)return!1;Pf();return(ff?jf:Of)(a)};
_.Rf=function(a){if(gf!==-1)return Pf(),hf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):tf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
_.t("gadgets.json.stringify",_.Rf);_.t("gadgets.json.parse",_.Qf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.bf=function(e){a(2,e)};_.cf=function(e){a(3,e)};_.df=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.af=_.af||{};(function(){var a=[];_.af.rsa=function(b){a.push(b)};_.af.Fsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
var Uf=function(){this.Eg=window.console};Uf.prototype.log=function(a){this.Eg&&this.Eg.log&&this.Eg.log(a)};Uf.prototype.error=function(a){this.Eg&&(this.Eg.error?this.Eg.error(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.warn=function(a){this.Eg&&(this.Eg.warn?this.Eg.warn(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Wf=function(){var a=_.ye.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.xe.addEventListener?(_.xe.addEventListener("load",c,!1),_.xe.addEventListener("DOMContentLoaded",c,!1)):_.xe.attachEvent&&(_.xe.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.xe.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Be(_.Me,"watt",_.Ce());_.Be(c,a,b)};_.Ge(_.xe.location.href,"rpctoken")&&_.Le(_.ye,"unload",function(){});var Zf=Zf||{};Zf.HZ=null;Zf.rX=null;Zf.FA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.TN||(Zf.TN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.df();var k=_.$f.co(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.cf("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{nT:function(){return"wpm"},nca:function(){return!0},init:function(f,h){_.ef.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.co(f),m=_.$f.MO(f);l?window.setTimeout(function(){var n=_.Rf(k);_.df();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.cf("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.jZ=_.$f.registerDefault,_.$f.A1=_.$f.unregisterDefault,_.$f.SS=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Gu=_.$f.getRelayUrl,_.$f.Oj=_.$f.setRelayUrl,_.$f.OC=_.$f.setAuthToken,_.$f.Iw=_.$f.setupReceiver,_.$f.Pn=_.$f.getAuthToken,_.$f.vK=_.$f.removeReceiver,_.$f.NT=_.$f.getRelayChannel,_.$f.eZ=_.$f.receive,
_.$f.fZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.co=_.$f.getTargetOrigin,_.$f.MO=_.$f._getTargetWin,_.$f.N6=_.$f._parseSiblingId);else{_.$f=function(){function a(I,ka){if(!T[I]){var ma=cb;ka||(ma=Oa);T[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var U=ka[Fa];U.t=E[I];ma.call(I,U.f,U)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,U){E[ka]&&E[ka]===ma||(_.cf("Invalid gadgets.rpc token. "+E[ka]+" vs "+ma),qb(ka,2));U.onunload=function(){R[ka]&&!Mb&&(qb(ka,1),_.$f.vK(ka))};b();Fa=_.Qf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(E[I.f]&&E[I.f]!==I.t&&(_.cf("Invalid gadgets.rpc token. "+E[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.$f.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var U=e(Fa)}catch(Ga){}Fa&&U==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.$f.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var U=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&U!=="80"||I==="https"&&U!=="443")ma=":"+U}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(R[I]!==!0){typeof R[I]===
"undefined"&&(R[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?R[I]!==!0&&R[I]++<10?window.setTimeout(function(){l(I,ka)},500):(T[I]=Oa,R[I]=!0):R[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(D[I]=!!ma)}function p(I,ka){ka=ka||"";E[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);O=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Zf.FA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var U=Fa.parentRelayUrl||"";U=e(aa.parent||ka)+U;n("..",U,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!aa.parent&&
ka?ma({}):_.ef.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||aa.rpctoken||aa.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.af)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.af.Rg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},D={},E={},N=0,H={},R={},aa={},T={},K={},O=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{nT:function(){return"noop"},nca:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.af&&(aa=_.af.Rg());var Mb=!1,Hb=!1,cb=function(){if(aa.rpctx=="rmr")return Zf.HZ;var I=typeof window.postMessage==="function"?Zf.TN:typeof window.postMessage==="object"?Zf.TN:window.ActiveXObject?Zf.rX?Zf.rX:Zf.FA:navigator.userAgent.indexOf("WebKit")>0?Zf.HZ:navigator.product==="Gecko"?Zf.frameElement:Zf.FA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=H[I];ma&&(delete H[I],ma.call(this,ka))};return{config:function(I){typeof I.VZ==="function"&&(qb=I.VZ)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},jZ:function(I){x[""]=I},A1:function(){delete x[""]},SS:function(){},call:function(I,ka,ma,Fa){I=I||"..";var U="..";I===".."?U=La:I.charAt(0)=="/"&&(U=
e(window.location.href),U="/"+La+(U?"|"+U:""));++N;ma&&(H[N]=ma);var Ga={s:ka,f:U,c:ma?N:0,a:Array.prototype.slice.call(arguments,3),t:E[I],l:!!D[I]};a:if(O==="bidir"||O==="c2p"&&I===".."||O==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=T[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:N),Ga.g=!0,Ga.r=U,Ha?(D[I]&&(Ha=Zf.FA),Ha.call(I,U,Ga)===!1&&(T[I]=Oa,cb.call(I,U,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Gu:m,Oj:n,OC:p,Iw:u,Pn:function(I){return E[I]},vK:function(I){delete A[I];delete D[I];delete E[I];delete R[I];delete T[I]},NT:function(){return cb.nT()},eZ:function(I,ka){I.length>4?cb.Qpa(I,d):c.apply(null,I.concat(ka))},fZ:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,co:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=aa.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.ef.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},MO:h,N6:f,Wha:"__ack",Wma:La||"..",gna:0,fna:1,ena:2}}();_.$f.init()};_.$f.config({VZ:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.$f.config);_.t("gadgets.rpc.register",_.$f.register);_.t("gadgets.rpc.unregister",_.$f.unregister);_.t("gadgets.rpc.registerDefault",_.$f.jZ);_.t("gadgets.rpc.unregisterDefault",_.$f.A1);_.t("gadgets.rpc.forceParentVerifiable",_.$f.SS);_.t("gadgets.rpc.call",_.$f.call);_.t("gadgets.rpc.getRelayUrl",_.$f.Gu);_.t("gadgets.rpc.setRelayUrl",_.$f.Oj);_.t("gadgets.rpc.setAuthToken",_.$f.OC);_.t("gadgets.rpc.setupReceiver",_.$f.Iw);_.t("gadgets.rpc.getAuthToken",_.$f.Pn);
_.t("gadgets.rpc.removeReceiver",_.$f.vK);_.t("gadgets.rpc.getRelayChannel",_.$f.NT);_.t("gadgets.rpc.receive",_.$f.eZ);_.t("gadgets.rpc.receiveSameDomain",_.$f.fZ);_.t("gadgets.rpc.getOrigin",_.$f.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.$f.co);
var Xg={iia:"Authorization",P2:"Content-ID",Hia:"Content-Transfer-Encoding",Iia:"Content-Type",oja:"Date",fma:"OriginToken",Cka:"hotrod-board-name",Dka:"hotrod-chrome-cpu-model",Eka:"hotrod-chrome-processors",woa:"User-Agent",Roa:"WWW-Authenticate",Toa:"X-Ad-Manager-Impersonation",Soa:"X-Ad-Manager-Debug-Info",Voa:"X-ClientDetails",Woa:"X-Cloudaicompanion-Trace-Id",Xoa:"X-Compass-Routing-Destination",apa:"X-Goog-AuthUser",fpa:"X-Goog-Encode-Response-If-Executable",Yoa:"X-Google-Consent",Zoa:"X-Google-EOM",
hpa:"X-Goog-Meeting-ABR",ipa:"X-Goog-Meeting-Botguardid",jpa:"X-Goog-Meeting-Bot-Info",kpa:"X-Goog-Meeting-ClientInfo",lpa:"X-Goog-Meeting-ClientVersion",mpa:"X-Goog-Meeting-Debugid",npa:"X-Goog-Meeting-Identifier",opa:"X-Goog-Meeting-Interop-Cohorts",ppa:"X-Goog-Meeting-Interop-Type",qpa:"X-Goog-Meeting-OidcIdToken",rpa:"X-Goog-Meeting-RtcClient",spa:"X-Goog-Meeting-StartSource",tpa:"X-Goog-Meeting-Token",upa:"X-Goog-Meeting-Viewer-Token",vpa:"X-Goog-PageId",xpa:"X-Goog-Safety-Content-Type",ypa:"X-Goog-Safety-Encoding",
cpa:"X-Goog-Drive-Client-Version",dpa:"X-Goog-Drive-Resource-Keys",zpa:"X-HTTP-Method-Override",Apa:"X-JavaScript-User-Agent",Bpa:"X-Origin",Cpa:"X-Referer",Dpa:"X-Requested-With",Gpa:"X-Use-HTTP-Status-Code-Override",Epa:"X-Server-Timeout",gpa:"X-Goog-First-Party-Reauth",Fpa:"X-Server-Token",bpa:"x-goog-chat-space-id",wpa:"x-goog-pan-request-context",Uoa:"X-AppInt-Credentials",epa:"X-Goog-Earth-Gcp-Project"},Yg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding User-Agent Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-223261916-bin x-goog-ext-*********-bin x-goog-ext-233818517-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project".split(" "),
Zg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination x-goog-ext-*********-bin x-goog-ext-*********-bin".split(" ");var $g,ah,bh,ch,eh,fh,gh,hh,ih,jh,kh,lh;$g=null;ah=null;bh=null;ch=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.dh=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};eh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
fh={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
gh=function(a){if(!_.kd(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();ch(d,e)&&(b[e]=d)}}for(var f in Xg)Object.prototype.hasOwnProperty.call(Xg,f)&&(a=Xg[f],c=a.toLowerCase(),ch(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};hh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");ih=/[ \t]*(\r?\n[ \t]+)+/g;jh=/^[ \t]+|[ \t]+$/g;
kh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=kh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(ih," "),a=a.replace(jh,""),a.replace(hh,"")==""&&a))return a};lh=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.mh=function(a){if(typeof a!=="string"||!a||!a.match(lh))return null;a=a.toLowerCase();if(bh==null){var b=[],c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);(c=_.Xe("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);for(var d in Xg)Object.prototype.hasOwnProperty.call(Xg,d)&&b.push(Xg[d]);bh=gh(b)}return bh!=null&&bh.hasOwnProperty(a)?bh[a]:a};
_.nh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||fh[a])return null;if($g==null){b=[];var c=_.Xe("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);$g=gh(b)}return $g!=null&&$g.hasOwnProperty(a)?$g[a]:null};
_.oh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(eh[a])return null;if(ah==null){b=[];var c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);ah=gh(b)}return ah!=null&&ah.hasOwnProperty(a)?a:null};
_.ph=function(a,b){if(_.mh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&ch(d,b)){var e=kh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.qh=function(a,b,c,d){var e=_.mh(b);if(e){c&&(c=kh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&ch(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.rh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.mh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=kh(f))if(k=_.oh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.ph(c,k),h!==void 0&&(f=h+", "+f),_.qh(c,k,f,!0)}}}return c};
var th;_.sh=function(a){_.Xa.setTimeout(function(){throw a;},0)};th=0;_.uh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++th)};
_.vh=function(){return _.Mc("Safari")&&!(_.Wc()||(_.Oc()?0:_.Mc("Coast"))||_.Pc()||_.Sc()||_.Tc()||_.Uc()||_.Vc()||_.Mc("Silk")||_.Mc("Android"))};_.wh=function(){return _.Mc("Android")&&!(_.Wc()||_.Vc()||_.Pc()||_.Mc("Silk"))};_.yh=_.Vc();_.zh=_.ad()||_.Mc("iPod");_.Ah=_.Mc("iPad");_.Bh=_.wh();_.Ch=_.Wc();_.Dh=_.vh()&&!_.bd();
_.Eh=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.kd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.uh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Gh=function(a){for(var b in a)return!1;return!0};
_.Hh=function(a,b){a.src=_.kc(b);(b=_.Gc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ih=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Jh,Kh,Mh;Jh={};Kh=null;_.Lh=_.Bd||_.Cd||!_.Dh&&typeof _.Xa.atob=="function";_.Nh=function(a,b){b===void 0&&(b=0);Mh();b=Jh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Oh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Kh[m];if(n!=null)return n;if(!_.xc(m))throw Error("w`"+m);}return l}Mh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Mh=function(){if(!Kh){Kh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Jh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Kh[f]===void 0&&(Kh[f]=e)}}}};
_.Ph=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Qh=function(a){return a==null?"":String(a)};_.Rh=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Sh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Th=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Uh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Uh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Vh=function(a){var b=[],c;for(c in a)_.Uh(c,a[c],b);return b.join("&")};
_.Wh=function(a,b){b=_.Vh(b);return _.Th(a,b)};
var Xh,Yh=function(){try{return new XMLHttpRequest}catch(a){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(a){}return null},Zh=function(a){var b=_.dh(a);if(String(a)!=b)throw Error("x");(a=b)&&a.charAt(a.length-1)=="/"||(a=(a||"")+"/");_.$f.register("init",function(){Zh(a)});Xh=a;_.af.Rg(window.location.href)},$h=function(a,b,c,d){var e={};if(b)for(var f in b)if(Object.prototype.hasOwnProperty.call(b,f)){var h=_.ph(b,f),k=_.oh(f,h);k&&h!==void 0&&_.qh(e,k,h,!0)}return{body:a,headers:e,status:typeof c===
"number"?c:void 0,statusText:d||void 0}},ai=function(a,b){a={error:{code:-1,message:a}};if(b.url=="/rpc"){b=b.body;for(var c=[],d=0;d<b.length;d++){var e=_.Rf(a);e=_.Qf(e);e.id=b[d].id;c.push(e)}a=c}return _.Rf(a)},bi=function(a,b,c,d){a=a||{};var e=a.headers||{},f=a.httpMethod||"GET",h=String(a.url||""),k=a.urlParams||null,l=a.body||null;c=c||null;d=d||null;h=_.dh(h);h=Xh+String(h||"/").substr(1);h=_.Wh(h,k);var m=[];k=[];for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){m.push(n);var p=
_.ph(e,n);p!==void 0&&(n=_.nh(n,p))&&k.push([n,p])}for(;m.length;)delete e[m.pop()];for(;k.length;)n=k.pop(),_.qh(e,n[0],n[1]);_.qh(e,"X-Origin",c||void 0);_.qh(e,"X-Referer",d||void 0);_.qh(e,"X-Goog-Encode-Response-If-Executable","base64");l&&typeof l==="object"&&(l=_.Rf(l));var q=Yh();if(!q)throw Error("y");q.open(f,h);q.onreadystatechange=function(){if(q.readyState==4&&q.status!==0){var w=q.responseText;var u=q.getAllResponseHeaders();u=_.rh(u,!0);w=$h(w,u,q.status,q.statusText);b(w)}};q.onerror=
function(){var w=ai("A network error occurred, and the request could not be completed.",a);w=$h(w);b(w)};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(f=e[r],q.setRequestHeader(unescape(encodeURIComponent(r)),unescape(encodeURIComponent(f))));q.send(l?l:null)},ci=function(a,b,c,d){var e={},f=0;if(a.length==0)b(e);else{var h=function(k){var l=k.key;k=k.params;try{bi(k,function(n){e[l]={data:n};f++;a.length==f?b(_.Rf(e)):h(a[f])},c,d)}catch(n){var m="";n&&(m+=" [",n.name&&(m+=n.name+": "),
m+=n.message||String(n),m+="]");k=ai("An error occurred, and the request could not be completed."+m,k);k=$h(k);e[l]={data:k};f++;a.length==f?b(_.Rf(e)):h(a[f])}};h(a[f])}};_.Lg=_.Lg||{};_.Lg.Hea=function(){_.$f.register("makeHttpRequests",function(a){this.f==".."&&this.t==_.$f.Pn("..")&&this.origin==_.$f.co("..")&&ci.call(this,a,this.callback,this.origin,this.referer)})};
_.Lg.init=function(){var a=String(window.location.pathname);a.length>=18&&a.substr(a.length-18)=="/static/proxy.html"&&(a=a.substr(0,a.length-18));a||(a="/");_.Lg.sV(a)};_.Lg.sV=function(a){var b=_.dh(a);if(String(a)!=b)throw Error("x");_.Lg.Hea();Zh(a);_.$f.call("..","ready:"+_.$f.Pn(".."))};_.t("googleapis.ApiServer.makeHttpRequests",ci);_.t("googleapis.ApiServer.initWithPath",Zh);_.t("googleapis.server.init",_.Lg.init);_.t("googleapis.server.initWithPath",_.Lg.sV);
});
// Google Inc.
