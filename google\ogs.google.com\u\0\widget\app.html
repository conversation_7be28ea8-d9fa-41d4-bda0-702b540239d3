<!doctype html>
<html lang="ar" dir="rtl">

<head>
    <base href="https://ogs.google.com/u/0/">
    <link rel="preconnect" href="//www.gstatic.com">
    <meta name="referrer" content="origin">
    <link rel="canonical" href="https://ogs.google.com/widget/app">
    <link rel="preconnect" href="https://www.gstatic.com">
    <link rel="preconnect" href="https://ssl.gstatic.com">
    <script data-id="_gd" nonce="s24H2YaCsCRB820x9S0AJg">
        window.WIZ_global_data = {
            "DpimGf": false,
            "EP1ykd": ["/_/*"],
            "FdrFJe": "5759266633976213504",
            "Im6cmf": "/u/0/_/OneGoogleWidgetUi",
            "LVIXXb": 1,
            "LoQv7e": true,
            "MT7f9b": [],
            "MUE6Ne": "OneGoogleWidgetUi",
            "NrSucd": false,
            "OwAJ6e": false,
            "QrtxK": "0",
            "Rf2tsb": 0,
            "S06Grb": "112981580452882507747",
            "S6lZl": 122505695,
            "SNlM0e": "ALHxRM_4xP-lN979aRWaVpasWYd-:*************",
            "TSDtV": "%.@.[[null,[[45459555,null,false,null,null,null,\"Imeoqb\"]],\"CAMSDh0L2eicEJbkAbyXzxAA\"]]]",
            "UUFaWc": "%.@.null,1000,2]",
            "Vvafkd": false,
            "W3Yyqf": "112981580452882507747",
            "WZsZ1e": "bQoG1qkF1wPwM61L/A4B1xxvKBLRN5U-c9",
            "Yllh3e": "%.@.1748560023512964,122286769,3726864064]",
            "ZwjLXe": 113,
            "b5W2zf": "default_OneGoogleWidgetUi",
            "cfb2h": "boq_onegooglehttpserver_20250525.03_p0",
            "eptZe": "/u/0/_/OneGoogleWidgetUi/",
            "fPDxwd": [48691166, 48802160, 98218420],
            "gGcLoe": false,
            "iCzhFc": false,
            "nQyAE": {},
            "oPEP7c": "<EMAIL>",
            "p9hQne": "https://www.gstatic.com/_/boq-one-google/_/r/",
            "qDCSke": "112981580452882507747",
            "qwAQke": "OneGoogleWidgetUi",
            "rtQCxc": -180,
            "u4g7r": "%.@.null,1,2]",
            "vJQk6": false,
            "w2btAe": "%.@.\"112981580452882507747\",\"112981580452882507747\",\"0\",true,null,null,true,false]",
            "xn5OId": false,
            "xnI9P": true,
            "xwAfE": true,
            "y2FhP": "prod",
            "yFnxrf": 1884,
            "zChJod": "%.@.]"
        };
    </script>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        (function() {
            'use strict';
            var a = window,
                d = a.performance,
                l = k();
            a.cc_latency_start_time = d && d.now ? 0 : d && d.timing && d.timing.navigationStart ? d.timing.navigationStart : l;

            function k() {
                return d && d.now ? d.now() : (new Date).getTime()
            }

            function n(e) {
                if (d && d.now && d.mark) {
                    var g = d.mark(e);
                    if (g) return g.startTime;
                    if (d.getEntriesByName && (e = d.getEntriesByName(e).pop())) return e.startTime
                }
                return k()
            }
            a.onaft = function() {
                n("aft")
            };
            a._isLazyImage = function(e) {
                return e.hasAttribute("data-src") || e.hasAttribute("data-ils") || e.getAttribute("loading") === "lazy"
            };
            a.l = function(e) {
                function g(b) {
                    var c = {};
                    c[b] = k();
                    a.cc_latency.push(c)
                }

                function m(b) {
                    var c = n("iml");
                    b.setAttribute("data-iml", c);
                    return c
                }
                a.cc_aid = e;
                a.iml_start = a.cc_latency_start_time;
                a.css_size = 0;
                a.cc_latency = [];
                a.ccTick = g;
                a.onJsLoad = function() {
                    g("jsl")
                };
                a.onCssLoad = function() {
                    g("cssl")
                };
                a._isVisible = function(b, c) {
                    if (!c || c.style.display == "none") return !1;
                    var f = b.defaultView;
                    if (f && f.getComputedStyle && (f = f.getComputedStyle(c), f.height == "0px" || f.width == "0px" || f.visibility == "hidden")) return !1;
                    if (!c.getBoundingClientRect) return !0;
                    var h = c.getBoundingClientRect();
                    c = h.left + a.pageXOffset;
                    f = h.top + a.pageYOffset;
                    if (f + h.height < 0 || c + h.width < 0 || h.height <= 0 || h.width <= 0) return !1;
                    b = b.documentElement;
                    return f <= (a.innerHeight || b.clientHeight) && c <= (a.innerWidth || b.clientWidth)
                };
                a._recordImlEl = m;
                document.documentElement.addEventListener("load", function(b) {
                    b = b.target;
                    var c;
                    b.tagName != "IMG" || b.hasAttribute("data-iid") || a._isLazyImage(b) || b.hasAttribute("data-noaft") || (c = m(b));
                    if (a.aft_counter && (b = a.aft_counter.indexOf(b), b !== -1 && (b = a.aft_counter.splice(b,
                            1).length === 1, a.aft_counter.length === 0 && b && c))) a.onaft(c)
                }, !0);
                a.prt = -1;
                a.wiz_tick = function() {
                    var b = n("prt");
                    a.prt = b
                }
            };
        }).call(this);
        l('qBzSPd')
    </script>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        var _F_cssRowKey = 'boq-one-google.OneGoogleWidgetUi.AE4cR6X1KGk.R.B1.O';
        var _F_combinedSignature = 'AM-SdHvS7NiX7vj_3QwXwgLdnHa1hIegqw';

        function _DumpException(e) {
            throw e;
        }
    </script>
    <style data-href="https://www.gstatic.com/_/mss/boq-one-google/_/ss/k=boq-one-google.OneGoogleWidgetUi.AE4cR6X1KGk.R.B1.O/am=KAABAAbsAwAAAQ/d=1/ed=1/rs=AM-SdHszI3_kTN5SZ8Q4uu48u26q4TidJg/m=appwidgetauthview,_b,_tp" nonce="YskSaovbS0tK60ZYl86vVQ">
        html,
        body {
            height: 100%;
            overflow: hidden
        }

        body {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: rgba(0, 0, 0, .87);
            font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
            margin: 0;
            text-size-adjust: 100%
        }

        textarea {
            font-family: Roboto, RobotoDraft, Helvetica, Arial, sans-serif
        }

        a {
            text-decoration: none;
            color: #2962ff
        }

        img {
            border: none
        }

        * {
            -webkit-tap-highlight-color: transparent
        }

        #apps-debug-tracers {
            display: none
        }

        c-wiz {
            contain: style
        }

        c-wiz>c-data {
            display: none
        }

        c-wiz.rETSD {
            contain: none
        }

        c-wiz.Ubi8Z {
            contain: layout style
        }

        /*# sourceMappingURL=chrome.css.map */

        .MCcOAc {
            bottom: 0;
            right: 0;
            position: absolute;
            left: 0;
            top: 0;
            overflow: hidden;
            z-index: 1
        }

        .MCcOAc>.pGxpHc {
            flex-shrink: 0;
            flex-grow: 0
        }

        .IqBfM>.HLlAHb {
            align-items: center;
            display: flex;
            height: 60px;
            position: absolute;
            left: 16px;
            top: 0;
            z-index: 9999
        }

        .VUoKZ {
            display: none;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            height: 3px;
            z-index: 1001
        }

        .TRHLAc {
            position: absolute;
            top: 0;
            right: 0;
            width: 25%;
            height: 100%;
            background: #68e;
            transform: scaleX(0)
        }

        .TRHLAc {
            transform-origin: 0 0
        }

        .mIM26c .VUoKZ {
            display: block
        }

        .mIM26c .TRHLAc {
            animation: boqChromeapiPageProgressAnimation 1s infinite;
            animation-timing-function: cubic-bezier(.4, 0, 1, 1);
            animation-delay: .1s
        }

        .ghyPEc .VUoKZ {
            position: fixed
        }

        @keyframes boqChromeapiPageProgressAnimation {
            0% {
                transform: scaleX(0)
            }
            50% {
                transform: scaleX(5)
            }
            100% {
                transform: scaleX(5) translateX(-100%)
            }
        }

        .kFwPee {
            height: 100%
        }

        .ydMMEb {
            width: 100%
        }

        .SSPGKf {
            display: block;
            overflow-y: hidden;
            z-index: 1
        }

        .eejsDc {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch
        }

        .T4LgNb {
            bottom: 0;
            right: 0;
            top: 0;
            left: 0;
            position: absolute;
            z-index: 1
        }

        .QMEh5b {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            z-index: 3
        }

        .AOq4tb {
            height: 56px
        }

        .kFwPee {
            position: relative;
            z-index: 1;
            height: 100%
        }

        .ydMMEb {
            height: 56px;
            width: 100%
        }

        .SSPGKf {
            overflow-y: hidden;
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            top: 0
        }

        .ecJEib .AOq4tb,
        .ecJEib .ydMMEb {
            height: 64px
        }

        .e2G3Fb.EWZcud .AOq4tb,
        .e2G3Fb.EWZcud .ydMMEb {
            height: 48px
        }

        .e2G3Fb.b30Rkd .AOq4tb,
        .e2G3Fb.b30Rkd .ydMMEb {
            height: 56px
        }

        .qWuU9c {
            box-sizing: border-box;
            height: 100%;
            padding: 3px 10px 14px
        }

        .EHzcec {
            background: #e9eef6;
            background: var(--gm3-sys-color-surface-container-high, #e9eef6);
            border-radius: 28px;
            border: none;
            box-shadow: 0 4px 8px 3px rgba(0, 0, 0, .15), 0 1px 3px rgba(0, 0, 0, .3);
            box-sizing: border-box;
            display: block;
            height: 100%;
            margin: 3px auto;
            overflow-x: hidden;
            outline: 1px solid transparent;
            padding: 8px;
            position: relative;
            width: 344px
        }

        .nz9sqb.EHzcec {
            background: #282a2c;
            background: var(--gm3-sys-color-surface-container-high, #282a2c);
            box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, .3), 0 -2px 6px 2px rgba(0, 0, 0, .15)
        }

        .nz9sqb.EHzcec .LVal7b {
            background: #1b1b1b;
            background: var(--gm3-sys-color-surface-container-low, #1b1b1b);
            color: #c4c7c5;
            color: var(--gm3-sys-color-on-surface-variant, #c4c7c5)
        }

        .ngVsM {
            box-sizing: content-box;
            margin: 0;
            padding: 0
        }

        .v7bWUd,
        .o83JEf {
            width: 100%
        }

        .LVal7b {
            background: #f8fafd;
            background: var(--gm3-sys-color-surface-container-low, #f8fafd);
            color: #444746;
            color: var(--gm3-sys-color-on-surface-variant, #444746);
            border-radius: 24px;
            box-sizing: border-box;
            margin-bottom: 10px;
            padding: 24px 20px;
            width: 328px
        }

        .o83JEf .LVal7b {
            border-radius: 4px;
            margin-bottom: 4px
        }

        .o83JEf .LVal7b:first-child {
            border-radius: 24px 24px 4px 4px
        }

        .o83JEf .LVal7b:last-child {
            border-radius: 4px 4px 24px 24px;
            margin-bottom: 10px
        }

        .u4RcUd {
            padding-top: 0
        }

        .L2gNYe {
            padding-bottom: 10px
        }

        .j1ei8c {
            display: inline-block;
            height: 84px;
            list-style-type: none;
            padding: 6px;
            position: relative;
            transition: transform .2s cubic-bezier(.333, 0, 0, 1);
            vertical-align: top;
            width: 84px
        }

        .tX9u1b {
            margin: 0;
            outline: none;
            position: absolute;
            text-align: center;
            text-decoration: none;
            width: 84px
        }

        .tX9u1b:hover .Rq5Gcb,
        .QgddUc .tX9u1b:focus .Rq5Gcb,
        .tX9u1b:active .Rq5Gcb {
            overflow-wrap: break-word;
            white-space: normal;
            word-wrap: break-word
        }

        .tX9u1b:hover {
            background-color: rgb(232, 240, 254);
            border: none;
            margin: 0;
            z-index: 1
        }

        .tX9u1b:hover .Rq5Gcb {
            background-color: rgb(232, 240, 254);
            text-decoration: none
        }

        .nz9sqb .tX9u1b:hover,
        .nz9sqb .tX9u1b:hover .Rq5Gcb {
            background-color: #28292c
        }

        .QgddUc .tX9u1b:focus,
        .QgddUc .tX9u1b:hover:focus,
        .QgddUc .tX9u1b:hover:focus .Rq5Gcb {
            background-color: #e3edfe;
            outline: 1px solid rgb(23, 78, 166);
            z-index: 1
        }

        .nz9sqb.QgddUc .tX9u1b:focus,
        .nz9sqb.QgddUc .tX9u1b:hover:focus {
            background-color: #38393c;
            border: 1px solid #e8eaed;
            margin: -1px
        }

        .nz9sqb.QgddUc .tX9u1b:hover:focus .Rq5Gcb {
            background-color: #38393c
        }

        .tX9u1b:active,
        .tX9u1b:active:focus,
        .tX9u1b:active:hover .Rq5Gcb {
            background-color: #e3edfe;
            z-index: 1
        }

        .nz9sqb .tX9u1b:active,
        .nz9sqb .tX9u1b:active:focus {
            background-color: #434447;
            border-color: transparent;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .3), 0 2px 6px 2px rgba(0, 0, 0, .15)
        }

        .nz9sqb .tX9u1b:active:hover .Rq5Gcb {
            background-color: #434447
        }

        .o07G5 .tX9u1b:active,
        .o07G5 .tX9u1b:active:focus,
        .o07G5 .tX9u1b:active .Rq5Gcb,
        .o07G5 .tX9u1b:active:hover .Rq5Gcb {
            background-color: #fff;
            z-index: 1
        }

        .nz9sqb.o07G5 .tX9u1b:active,
        .nz9sqb.o07G5 .tX9u1b:active:focus,
        .nz9sqb.o07G5 .tX9u1b:active .Rq5Gcb,
        .nz9sqb.o07G5 .tX9u1b:active:hover .Rq5Gcb {
            background-color: #2d2e30;
            border-color: transparent;
            opacity: .8
        }

        .tX9u1b[draggable=false] {
            -webkit-touch-callout: none;
            user-select: none
        }

        .MrEfLc {
            display: inline-block;
            height: 53px;
            vertical-align: top;
            width: 53px
        }

        .CgwTDb {
            height: 57px;
            margin-top: 5px
        }

        .Rq5Gcb {
            color: rgb(32, 33, 36);
            display: inline-block;
            font-family: "Google Sans", Roboto, Helvetica, Arial, sans-serif;
            font-size: 14px;
            letter-spacing: .09px;
            line-height: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 76px
        }

        .nz9sqb .Rq5Gcb {
            color: rgb(232, 234, 237)
        }

        .dGrefb {
            border-bottom: 1px solid rgb(232, 234, 237);
            display: block;
            right: 0;
            margin-bottom: 6px;
            margin-top: 6px;
            position: relative;
            width: 100%
        }

        .nz9sqb .dGrefb {
            border-bottom: 1px solid #5f6368
        }

        .MrEfLc.dOs7We {
            background-size: 53px
        }

        .NQV3m {
            background-color: #fff;
            border: 1px solid rgb(218, 220, 224);
            border-radius: 4px;
            color: rgb(26, 115, 232);
            font: 500 14px/16px "Google Sans", Roboto, Helvetica, Arial, sans-serif;
            margin: 16px 0 20px 0;
            max-width: 265px;
            outline: 0;
            width: auto
        }

        .nz9sqb .NQV3m {
            background-color: transparent;
            border-color: rgb(95, 99, 104);
            color: rgb(138, 180, 248)
        }

        .WwFbJd {
            text-align: center
        }

        .NQV3m:hover {
            background-color: #f8fbff;
            border-color: #cce0fc
        }

        @media (forced-colors:active) {
            .NQV3m:focus,
            .tX9u1b:focus {
                outline: 2px solid transparent
            }
        }

        .NQV3m:focus,
        .NQV3m:hover:focus {
            background-color: #f4f8ff;
            border-color: #c9ddfc;
            text-decoration: none
        }

        .QgddUc .NQV3m:focus {
            outline: 1px solid rgb(23, 78, 166)
        }

        .nz9sqb .QgddUc .NQV3m:focus {
            outline: 1px solid rgb(210, 227, 252)
        }

        .nz9sqb .NQV3m:hover {
            background-color: rgb(60, 64, 67);
            border-color: rgb(95, 99, 104);
            text-decoration: none
        }

        .nz9sqb .NQV3m:focus,
        .nz9sqb .NQV3m:hover:focus,
        .nz9sqb .NQV3m:active,
        .nz9sqb .NQV3m:active:focus {
            background-color: #2d323d;
            border-color: rgb(138, 180, 248);
            box-shadow: none;
            text-decoration: none
        }

        .NQV3m:active,
        .NQV3m:active:focus {
            background-color: #ecf3fe;
            border-color: transparent;
            box-shadow: 0 1px 2px 0 rgba(60, 64, 67, .3), 0 2px 6px 2px rgba(60, 64, 67, .15)
        }

        .PZRdre {
            background-color: rgb(241, 243, 244);
            display: none;
            border-radius: 2px;
            height: 40px;
            right: 14px;
            margin: 8px;
            position: absolute;
            top: 0;
            width: 40px
        }

        .XiBjH {
            display: none;
            height: 24px;
            right: 8px;
            position: absolute;
            top: 8px;
            width: 24px
        }

        .EHzcec::-webkit-scrollbar {
            width: 16px
        }

        .EHzcec::-webkit-scrollbar-thumb {
            background: rgb(218, 220, 224);
            background-clip: padding-box;
            border: 4px solid transparent;
            border-radius: 8px;
            box-shadow: none;
            min-height: 50px
        }

        .nz9sqb.EHzcec::-webkit-scrollbar-thumb {
            background-color: rgb(95, 99, 104)
        }

        .EHzcec::-webkit-scrollbar-track,
        .EHzcec::-webkit-scrollbar-track:hover {
            background: none;
            border: none
        }

        .jFV0n {
            height: 40px;
            margin: 8px;
            width: 40px
        }

        .nz9sqb .jFV0n {
            position: relative
        }

        .OunZ9c {
            background: #fff;
            border: 1px solid #e5e5e5;
            box-shadow: 0 1px 2px rgba(0, 0, 0, .1);
            cursor: -webkit-grabbing;
            cursor: grabbing;
            opacity: .8;
            z-index: 1000
        }

        .zun25e {
            width: 40px
        }

        .nz9sqb .PZRdre,
        .FQaMnb .PZRdre,
        .FQaMnb .XiBjH {
            display: block
        }

        .nz9sqb .PZRdre {
            background-color: white
        }

        .FQaMnb .PZRdre {
            position: relative
        }

        .FQaMnb .jFV0n {
            display: none
        }

        .FQaMnb .Rq5Gcb {
            margin-top: 4px
        }

        .pPUwub,
        .dKVyP,
        .NcWGte,
        .ajYF5e {
            forced-color-adjust: none;
            height: 0;
            position: absolute;
            width: 0
        }

        .pPUwub {
            border-bottom: 5px solid transparent;
            border-left: 5px solid #4273db;
            border-top: 5px solid transparent;
            float: right;
            right: 0;
            top: 23.5px
        }

        .dKVyP {
            border-bottom: 5px solid transparent;
            border-right: 5px solid #4273db;
            border-top: 5px solid transparent;
            float: left;
            right: 79px;
            top: 23.5px
        }

        .ajYF5e {
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
            border-top: 5px solid #4273db;
            right: 33.5px;
            top: 52px
        }

        .NcWGte {
            border-bottom: 5px solid #4273db;
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
            right: 33.5px;
            top: 0
        }

        .pPUwub[aria-hidden=true],
        .dKVyP[aria-hidden=true],
        .NcWGte[aria-hidden=true],
        .ajYF5e[aria-hidden=true] {
            visibility: hidden
        }

        .NPEKs {
            background: #0b57d0;
            border-radius: 10px;
            color: white;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: .1px;
            line-height: 12px;
            max-width: 76px;
            overflow: hidden;
            padding: 2px 4px;
            position: absolute;
            left: 0;
            text-align: left;
            text-overflow: ellipsis
        }

        .nz9sqb .NPEKs {
            background: #a8c7fa;
            color: black
        }

        .MrEfLc.nxzZDf {
            background-image: url(//ssl.gstatic.com/gb/images/a/f5cdd88b65.png)
        }

        @media (min-resolution:144dpi) {
            .MrEfLc.nxzZDf {
                background-image: url(//ssl.gstatic.com/gb/images/a/133fc21e88.png)
            }
        }

        .kibP6b,
        .lHtSbd,
        .AfRWqe {
            align-items: center
        }

        .rn8xOd,
        .EuVUud {
            min-width: 24px
        }

        .mOLCvc {
            position: relative;
            flex: 0 0 fit-content;
            padding: 7px 0 7px 7px;
            width: 24px;
            height: 24px
        }

        .F6Urce,
        .bQMRfd {
            flex: 1 1 fit-content;
            overflow: hidden;
            max-height: 62px;
            max-width: 223px;
            text-overflow: ellipsis
        }

        .Bvt9Ob {
            flex: 0 0 fit-content;
            align-items: center;
            display: flex;
            height: 38px;
            justify-content: center;
            margin: 0 -5px;
            position: relative;
            min-width: 48px
        }

        .gKQpke {
            fill: transparent;
            position: absolute
        }

        .bOwcqf {
            align-items: center
        }

        .EHzcec .tX9u1b:hover,
        .EHzcec .tX9u1b:focus,
        .nz9sqb.EHzcec .tX9u1b:hover,
        .nz9sqb.EHzcec .tX9u1b:focus,
        .QgddUc.EHzcec .tX9u1b:hover,
        .QgddUc.EHzcec .tX9u1b:focus {
            outline: none
        }

        .EHzcec .tX9u1b:hover .Rq5Gcb,
        .EHzcec .tX9u1b:hover .Rq5Gcb:hover,
        .EHzcec .tX9u1b:hover .Rq5Gcb:focus,
        .EHzcec .tX9u1b:hover .Rq5Gcb:active,
        .EHzcec .tX9u1b:focus .Rq5Gcb,
        .EHzcec .tX9u1b:focus .Rq5Gcb:hover,
        .EHzcec .tX9u1b:focus .Rq5Gcb:focus,
        .EHzcec .tX9u1b:focus .Rq5Gcb:active,
        .nz9sqb.EHzcec .tX9u1b:hover .Rq5Gcb,
        .nz9sqb.EHzcec .tX9u1b:hover .Rq5Gcb:hover,
        .nz9sqb.EHzcec .tX9u1b:hover .Rq5Gcb:focus,
        .nz9sqb.EHzcec .tX9u1b:hover .Rq5Gcb:active,
        .nz9sqb.EHzcec .tX9u1b:focus .Rq5Gcb,
        .nz9sqb.EHzcec .tX9u1b:focus .Rq5Gcb:hover,
        .nz9sqb.EHzcec .tX9u1b:focus .Rq5Gcb:focus,
        .nz9sqb.EHzcec .tX9u1b:focus .Rq5Gcb:active,
        .QgddUc.EHzcec .tX9u1b:hover .Rq5Gcb,
        .QgddUc.EHzcec .tX9u1b:hover .Rq5Gcb:hover,
        .QgddUc.EHzcec .tX9u1b:hover .Rq5Gcb:focus,
        .QgddUc.EHzcec .tX9u1b:hover .Rq5Gcb:active,
        .QgddUc.EHzcec .tX9u1b:focus .Rq5Gcb,
        .QgddUc.EHzcec .tX9u1b:focus .Rq5Gcb:hover,
        .QgddUc.EHzcec .tX9u1b:focus .Rq5Gcb:focus,
        .QgddUc.EHzcec .tX9u1b:focus .Rq5Gcb:active {
            background-color: transparent;
            outline: none;
            text-decoration: none
        }

        .tX9u1b:hover {
            background-color: #e9eef6;
            background-color: var(--gm3-sys-color-surface-container-high, #e9eef6);
            border-radius: 16px
        }

        .tX9u1b:active,
        .tX9u1b:active:focus {
            background-color: #dde3ea;
            background-color: var(--gm3-sys-color-surface-container-highest, #dde3ea);
            border-radius: 16px;
            border: none
        }

        .QgddUc .tX9u1b:focus,
        .QgddUc .tX9u1b:hover:focus {
            border: 1px solid;
            border-color: #0b57d0;
            border-color: var(--gm3-sys-color-primary, #0b57d0);
            background-color: #dde3ea;
            background-color: var(--gm3-sys-color-surface-container-highest, #dde3ea);
            outline: none
        }

        .NQV3m {
            border-radius: 100px;
            border: 1px solid;
            border-color: #747775;
            border-color: var(--gm3-sys-color-outline, #747775);
            background: none;
            box-sizing: border-box;
            color: #0b57d0;
            color: var(--gm3-sys-color-primary, #0b57d0);
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            min-height: 40px;
            outline: none;
            padding: 10px 24px;
            text-align: center;
            text-decoration: none;
            white-space: normal;
            line-height: 18px;
            position: relative
        }

        .NQV3m::before {
            content: " ";
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            border-radius: 100px;
            transition: opacity .5s ease-out
        }

        .NQV3m:hover {
            cursor: pointer
        }

        .NQV3m:hover::before {
            opacity: .08
        }

        .NQV3m:active::before,
        .NQV3m:active:focus::before {
            opacity: .12
        }

        .NQV3m:focus-visible::before {
            opacity: .12
        }

        .NQV3m:focus-visible {
            outline-style: solid;
            outline-color: #0b57d0;
            outline-color: var(--gm3-sys-color-primary, #0b57d0);
            animation: focus-animation-2px .3s ease-in-out forwards
        }

        @media (forced-colors:active) {
            .NQV3m {
                border: 1px solid;
                border-color: #747775;
                border-color: var(--gm3-sys-color-outline, #747775)
            }
            .NQV3m:focus-visible {
                outline: 2px solid transparent
            }
        }

        .nz9sqb .NQV3m {
            border-radius: 100px;
            border: 1px solid;
            border-color: #8e918f;
            border-color: var(--gm3-sys-color-outline, #8e918f);
            background: none;
            box-sizing: border-box;
            color: #a8c7fa;
            color: var(--gm3-sys-color-primary, #a8c7fa);
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            min-height: 40px;
            outline: none;
            padding: 10px 24px;
            text-align: center;
            text-decoration: none;
            white-space: normal;
            line-height: 18px;
            position: relative
        }

        @keyframes focus-animation-2px {
            0% {
                outline-color: transparent;
                outline-offset: 0;
                outline-width: 0
            }
            100% {
                outline-offset: 2px;
                outline-width: 1px
            }
        }

        .nz9sqb .NQV3m::before {
            content: " ";
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            border-radius: 100px;
            transition: opacity .5s ease-out
        }

        .nz9sqb .NQV3m:hover {
            cursor: pointer;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .3), 0 1px 3px 1px rgba(0, 0, 0, .15)
        }

        .nz9sqb .NQV3m:hover::before {
            opacity: .08
        }

        .nz9sqb .NQV3m:active,
        .nz9sqb .NQV3m:active:focus {
            box-shadow: none
        }

        .nz9sqb .NQV3m:active::before,
        .nz9sqb .NQV3m:active:focus::before {
            opacity: .12
        }

        .nz9sqb .NQV3m:focus-visible {
            box-shadow: none
        }

        .nz9sqb .NQV3m:focus-visible::before {
            opacity: .12
        }

        .nz9sqb .NQV3m:focus-visible {
            outline-style: solid;
            outline-color: #0b57d0;
            outline-color: var(--gm3-sys-color-primary, #0b57d0);
            animation: focus-animation-2px .3s ease-in-out forwards
        }

        @media (forced-colors:active) {
            .nz9sqb .NQV3m {
                border: 1px solid;
                border-color: #747775;
                border-color: var(--gm3-sys-color-outline, #747775)
            }
            .nz9sqb .NQV3m:focus-visible {
                outline: 2px solid transparent
            }
        }

        .nz9sqb .tX9u1b:hover {
            background-color: #282a2c;
            background-color: var(--gm3-sys-color-surface-container-high, #282a2c)
        }

        .nz9sqb .tX9u1b:active,
        .nz9sqb .tX9u1b:active:focus {
            background-color: #333537;
            background-color: var(--gm3-sys-color-surface-container-highest, #333537)
        }

        .nz9sqb.QgddUc .tX9u1b:focus,
        .nz9sqb.QgddUc .tX9u1b:hover:focus {
            border-color: #a8c7fa;
            border-color: var(--gm3-sys-color-primary, #a8c7fa)
        }

        .EHzcec::-webkit-scrollbar {
            width: 8px
        }

        .EHzcec::-webkit-scrollbar-thumb {
            background-clip: padding-box;
            background-color: rgba(31, 31, 31, .16);
            border-radius: 8px;
            border: 1px solid transparent;
            box-shadow: none;
            height: 185px;
            max-height: 33%
        }

        .EHzcec::-webkit-scrollbar-thumb:hover {
            background-color: rgba(31, 31, 31, .24)
        }

        .EHzcec::-webkit-scrollbar-track {
            background: transparent;
            border: none;
            margin-bottom: 34px;
            margin-top: 34px
        }

        .OMHKdd {
            -webkit-overflow-scrolling: touch;
            overflow-y: overlay
        }

        .nz9sqb.EHzcec::-webkit-scrollbar-thumb {
            background-color: rgba(227, 227, 227, .16)
        }

        .nz9sqb.EHzcec::-webkit-scrollbar-thumb:hover {
            background-color: rgba(227, 227, 227, .24)
        }

        .tX9u1b {
            border-radius: 16px
        }

        .kibP6b,
        .lHtSbd {
            align-items: center;
            box-sizing: border-box;
            border: 1px solid transparent;
            background: #f8fafd;
            background: var(--gm3-sys-color-surface-container-low, #f8fafd);
            border-radius: 30px;
            display: flex;
            color: #0b57d0;
            color: var(--gm3-sys-color-primary, #0b57d0);
            font-family: "Google Sans", Roboto;
            font-size: 14px;
            font-weight: 500;
            gap: 12px;
            margin: 0 0 8px;
            outline: none;
            overflow: hidden;
            text-decoration: none;
            width: 328px;
            max-height: 84px;
            padding: 10px 12px
        }

        .lHtSbd {
            color: #444746;
            color: var(--gm3-sys-color-on-surface-variant, #444746);
            gap: 10px
        }

        .lHtSbd .zun25e {
            width: 40px
        }

        .kibP6b:hover {
            background-color: rgba(68, 71, 70, .08)
        }

        .kibP6b:focus:active {
            background-color: rgba(68, 71, 70, .12)
        }

        .EuVUud,
        .rn8xOd {
            flex: 0 0 fit-content;
            fill: #0b57d0;
            fill: var(--gm3-sys-color-primary, #0b57d0);
            min-width: 24px
        }

        .EuVUud,
        .rn8xOd {
            transform: rotate(180deg)
        }

        .rn8xOd {
            fill: #444746;
            fill: var(--gm3-sys-color-on-surface-variant, #444746)
        }

        .JI4QMc {
            position: absolute;
            stroke: rgba(17, 87, 206, .16)
        }

        .wlszAe {
            fill: transparent;
            position: absolute;
            transform: rotate(-90deg)
        }

        .sss8dc {
            position: absolute;
            stroke: #1ea446
        }

        .kibP6b:hover .JI4QMc {
            stroke: rgba(17, 87, 206, .16)
        }

        .kibP6b:hover .sss8dc {
            stroke: #198639
        }

        .p37w9e {
            color: #444746;
            color: var(--gm3-sys-color-on-surface-variant, #444746);
            font-size: 12px;
            font-weight: 700;
            overflow: hidden;
            max-width: 30px
        }

        .bOwcqf {
            background-color: #0b57d0;
            background-color: var(--gm3-sys-color-primary, #0b57d0);
            color: #f8fafd;
            color: var(--gm3-sys-color-surface-container-low, #f8fafd);
            border-radius: 10px;
            display: flex;
            font-size: 12px;
            font-weight: 700;
            height: 11px;
            justify-content: center;
            right: 13px;
            min-width: 12px;
            padding: 2px 3px;
            position: absolute;
            top: 1px;
            border: .5px solid;
            border-color: #f8fafd;
            border-color: var(--gm3-sys-color-surface-container-low, #f8fafd)
        }

        .QgddUc .kibP6b:focus,
        .QgddUc .lHtSbd:focus {
            border: 1px solid;
            background: #dde3ea;
            background: var(--gm3-sys-color-surface-container-highest, #dde3ea);
            border-color: #0b57d0;
            border-color: var(--gm3-sys-color-primary, #0b57d0)
        }

        .nz9sqb .kibP6b,
        .nz9sqb .lHtSbd {
            background: #1b1b1b;
            background: var(--gm3-sys-color-surface-container-low, #1b1b1b);
            color: #a8c7fa;
            color: var(--gm3-sys-color-primary, #a8c7fa)
        }

        .nz9sqb .kibP6b:hover,
        .nz9sqb .lHtSbd:hover {
            background-color: #28292c
        }

        .nz9sqb .kibP6b:focus:active,
        .nz9sqb .lHtSbd:focus:active {
            background-color: rgba(227, 227, 227, .12)
        }

        .nz9sqb .EuVUud,
        .nz9sqb .rn8xOd {
            fill: #a8c7fa;
            fill: var(--gm3-sys-color-primary, #a8c7fa)
        }

        .nz9sqb .p37w9e {
            color: #e3e3e3;
            color: var(--gm3-sys-color-on-background, #e3e3e3)
        }

        .nz9sqb .JI4QMc {
            stroke: #444746;
            stroke: var(--gm3-sys-color-surface-variant, #444746)
        }

        .nz9sqb .bOwcqf {
            color: #000;
            background-color: #a8c7fa;
            background-color: var(--gm3-sys-color-primary, #a8c7fa);
            border-color: #1e1f20;
            border-color: var(--gm3-sys-color-surface-container, #1e1f20)
        }

        .nz9sqb.QgddUc .kibP6b:focus,
        .nz9sqb.QgddUc .lHtSbd:focus {
            border-color: #a8c7fa;
            border-color: var(--gm3-sys-color-primary, #a8c7fa);
            background-color: rgba(227, 227, 227, .12)
        }

        sentinel {}
    </style>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        onCssLoad();
    </script>
    <style nonce="YskSaovbS0tK60ZYl86vVQ">
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu72xKOzY.woff2)format('woff2');
            unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu5mxKOzY.woff2)format('woff2');
            unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu7mxKOzY.woff2)format('woff2');
            unicode-range: U+1F00-1FFF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu4WxKOzY.woff2)format('woff2');
            unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu7WxKOzY.woff2)format('woff2');
            unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu7GxKOzY.woff2)format('woff2');
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu4mxK.woff2)format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fCRc4EsA.woff2)format('woff2');
            unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fABc4EsA.woff2)format('woff2');
            unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fCBc4EsA.woff2)format('woff2');
            unicode-range: U+1F00-1FFF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fBxc4EsA.woff2)format('woff2');
            unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fCxc4EsA.woff2)format('woff2');
            unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fChc4EsA.woff2)format('woff2');
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/roboto/v18/KFOlCnqEu92Fr1MmEU9fBBc4.woff2)format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rgCIlsw.woff2)format('woff2');
            unicode-range: U+0308, U+0530-058F, U+2010, U+2024, U+25CC, U+FB13-FB17;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rACIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0980-09FE, U+1CD0, U+1CD2, U+1CD5-1CD6, U+1CD8, U+1CE1, U+1CEA, U+1CED, U+1CF2, U+1CF5-1CF7, U+200C-200D, U+20B9, U+25CC, U+A8F1;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2kQCIlsw.woff2)format('woff2');
            unicode-range: U+02C7, U+02D8-02D9, U+02DB, U+0307, U+1400-167F, U+18B0-18F5, U+25CC, U+11AB0-11ABF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2swCIlsw.woff2)format('woff2');
            unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2ugCIlsw.woff2)format('woff2');
            unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vwCIlsw.woff2)format('woff2');
            unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rwCIlsw.woff2)format('woff2');
            unicode-range: U+030E, U+1200-1399, U+2D80-2DDE, U+AB01-AB2E, U+1E7E0-1E7E6, U+1E7E8-1E7EB, U+1E7ED-1E7EE, U+1E7F0-1E7FE;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2oQCIlsw.woff2)format('woff2');
            unicode-range: U+0589, U+10A0-10FF, U+1C90-1CBA, U+1CBD-1CBF, U+205A, U+2D00-2D2F, U+2E31;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sgCIlsw.woff2)format('woff2');
            unicode-range: U+1F00-1FFF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vQCIlsw.woff2)format('woff2');
            unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pQCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0A80-0AFF, U+200C-200D, U+20B9, U+25CC, U+A830-A839;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2nQCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0A01-0A76, U+200C-200D, U+20B9, U+25CC, U+262C, U+A830-A839;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vACIlsw.woff2)format('woff2');
            unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2tQCIlsw.woff2)format('woff2');
            unicode-range: U+1780-17FF, U+19E0-19FF, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2twCIlsw.woff2)format('woff2');
            unicode-range: U+0E81-0EDF, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pgCIlsw.woff2)format('woff2');
            unicode-range: U+0307, U+0323, U+0951-0952, U+0964-0965, U+0D00-0D7F, U+1CDA, U+1CF2, U+200C-200D, U+20B9, U+25CC, U+A830-A832;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pwCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0B01-0B77, U+1CDA, U+1CF2, U+200C-200D, U+20B9, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2owCIlsw.woff2)format('woff2');
            unicode-range: U+0964-0965, U+0D81-0DF4, U+1CF2, U+200C-200D, U+25CC, U+111E1-111F4;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq20ACIlsw.woff2)format('woff2');
            unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2qACIlsw.woff2)format('woff2');
            unicode-range: U+0964-0965, U+0B82-0BFA, U+200C-200D, U+20B9, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2ogCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0C00-0C7F, U+1CDA, U+1CF2, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2qgCIlsw.woff2)format('woff2');
            unicode-range: U+02D7, U+0303, U+0331, U+0E01-0E5B, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sQCIlsw.woff2)format('woff2');
            unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sACIlsw.woff2)format('woff2');
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vgCI.woff2)format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rgCIlsw.woff2)format('woff2');
            unicode-range: U+0308, U+0530-058F, U+2010, U+2024, U+25CC, U+FB13-FB17;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rACIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0980-09FE, U+1CD0, U+1CD2, U+1CD5-1CD6, U+1CD8, U+1CE1, U+1CEA, U+1CED, U+1CF2, U+1CF5-1CF7, U+200C-200D, U+20B9, U+25CC, U+A8F1;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2kQCIlsw.woff2)format('woff2');
            unicode-range: U+02C7, U+02D8-02D9, U+02DB, U+0307, U+1400-167F, U+18B0-18F5, U+25CC, U+11AB0-11ABF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2swCIlsw.woff2)format('woff2');
            unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2ugCIlsw.woff2)format('woff2');
            unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vwCIlsw.woff2)format('woff2');
            unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2rwCIlsw.woff2)format('woff2');
            unicode-range: U+030E, U+1200-1399, U+2D80-2DDE, U+AB01-AB2E, U+1E7E0-1E7E6, U+1E7E8-1E7EB, U+1E7ED-1E7EE, U+1E7F0-1E7FE;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2oQCIlsw.woff2)format('woff2');
            unicode-range: U+0589, U+10A0-10FF, U+1C90-1CBA, U+1CBD-1CBF, U+205A, U+2D00-2D2F, U+2E31;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sgCIlsw.woff2)format('woff2');
            unicode-range: U+1F00-1FFF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vQCIlsw.woff2)format('woff2');
            unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pQCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0A80-0AFF, U+200C-200D, U+20B9, U+25CC, U+A830-A839;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2nQCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0A01-0A76, U+200C-200D, U+20B9, U+25CC, U+262C, U+A830-A839;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vACIlsw.woff2)format('woff2');
            unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2tQCIlsw.woff2)format('woff2');
            unicode-range: U+1780-17FF, U+19E0-19FF, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2twCIlsw.woff2)format('woff2');
            unicode-range: U+0E81-0EDF, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pgCIlsw.woff2)format('woff2');
            unicode-range: U+0307, U+0323, U+0951-0952, U+0964-0965, U+0D00-0D7F, U+1CDA, U+1CF2, U+200C-200D, U+20B9, U+25CC, U+A830-A832;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2pwCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0B01-0B77, U+1CDA, U+1CF2, U+200C-200D, U+20B9, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2owCIlsw.woff2)format('woff2');
            unicode-range: U+0964-0965, U+0D81-0DF4, U+1CF2, U+200C-200D, U+25CC, U+111E1-111F4;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq20ACIlsw.woff2)format('woff2');
            unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2qACIlsw.woff2)format('woff2');
            unicode-range: U+0964-0965, U+0B82-0BFA, U+200C-200D, U+20B9, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2ogCIlsw.woff2)format('woff2');
            unicode-range: U+0951-0952, U+0964-0965, U+0C00-0C7F, U+1CDA, U+1CF2, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2qgCIlsw.woff2)format('woff2');
            unicode-range: U+02D7, U+0303, U+0331, U+0E01-0E5B, U+200C-200D, U+25CC;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sQCIlsw.woff2)format('woff2');
            unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2sACIlsw.woff2)format('woff2');
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Google Sans';
            font-style: normal;
            font-weight: 500;
            src: url(https://fonts.gstatic.com/s/googlesans/v62/4UaRrENHsxJlGDuGo1OIlJfC6l_24rlCK1Yo_Iq2vgCI.woff2)format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Google Sans Display';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesansdisplay/v13/ea8FacM9Wef3EJPWRrHjgE4B6CnlZxHVDvr9oS_a.woff2)format('woff2');
            unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
        }

        @font-face {
            font-family: 'Google Sans Display';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesansdisplay/v13/ea8FacM9Wef3EJPWRrHjgE4B6CnlZxHVDv39oS_a.woff2)format('woff2');
            unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
        }

        @font-face {
            font-family: 'Google Sans Display';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesansdisplay/v13/ea8FacM9Wef3EJPWRrHjgE4B6CnlZxHVDvH9oS_a.woff2)format('woff2');
            unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
        }

        @font-face {
            font-family: 'Google Sans Display';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesansdisplay/v13/ea8FacM9Wef3EJPWRrHjgE4B6CnlZxHVDvD9oS_a.woff2)format('woff2');
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        @font-face {
            font-family: 'Google Sans Display';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/googlesansdisplay/v13/ea8FacM9Wef3EJPWRrHjgE4B6CnlZxHVDv79oQ.woff2)format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }
    </style>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        (function() {
            'use strict';

            function g() {
                var a = k,
                    b = 0;
                return function() {
                    return b < a.length ? {
                        done: !1,
                        value: a[b++]
                    } : {
                        done: !0
                    }
                }
            };
            /*

             Copyright The Closure Library Authors.
             SPDX-License-Identifier: Apache-2.0
            */
            var l = this || self;
            /*

             Copyright 2024 Google, Inc
             SPDX-License-Identifier: MIT
            */
            var m = ["focus", "blur", "error", "load", "toggle"];

            function n(a) {
                return a === "mouseenter" ? "mouseover" : a === "mouseleave" ? "mouseout" : a === "pointerenter" ? "pointerover" : a === "pointerleave" ? "pointerout" : a
            };

            function p(a) {
                this.l = {};
                this.m = {};
                this.i = null;
                this.g = [];
                this.o = a
            }
            p.prototype.handleEvent = function(a, b, c) {
                q(this, {
                    eventType: a,
                    event: b,
                    targetElement: b.target,
                    eic: c,
                    timeStamp: Date.now(),
                    eia: void 0,
                    eirp: void 0,
                    eiack: void 0
                })
            };

            function q(a, b) {
                if (a.i) a.i(b);
                else {
                    b.eirp = !0;
                    var c;
                    (c = a.g) == null || c.push(b)
                }
            }

            function r(a, b, c) {
                if (!(b in a.l) && a.o) {
                    var e = function(f, d, B) {
                        a.handleEvent(f, d, B)
                    };
                    a.l[b] = e;
                    c = n(c || b);
                    if (c !== b) {
                        var h = a.m[c] || [];
                        h.push(b);
                        a.m[c] = h
                    }
                    a.o.addEventListener(c, function(f) {
                        return function(d) {
                            e(b, d, f)
                        }
                    }, void 0)
                }
            }
            p.prototype.j = function(a) {
                return this.l[a]
            };
            p.prototype.ecrd = function(a) {
                this.i = a;
                var b;
                if ((b = this.g) == null ? 0 : b.length) {
                    for (a = 0; a < this.g.length; a++) q(this, this.g[a]);
                    this.g = null
                }
            };
            var t = typeof navigator !== "undefined" && /iPhone|iPad|iPod/.test(navigator.userAgent);

            function u(a) {
                this.g = a;
                this.i = []
            }
            u.prototype.addEventListener = function(a, b, c) {
                t && (this.g.style.cursor = "pointer");
                var e = this.i,
                    h = e.push,
                    f = this.g;
                b = b(this.g);
                var d = !1;
                m.indexOf(a) >= 0 && (d = !0);
                f.addEventListener(a, b, typeof c === "boolean" ? {
                    capture: d,
                    passive: c
                } : d);
                h.call(e, {
                    eventType: a,
                    j: b,
                    capture: d,
                    passive: c
                })
            };
            var k = "click dblclick focus focusin blur error focusout keydown keyup keypress load mouseover mouseout mouseenter mouseleave mousemove submit toggle touchstart touchend touchmove touchcancel auxclick change compositionstart compositionupdate compositionend beforeinput input select textinput copy cut paste mousedown mouseup wheel contextmenu dragover dragenter dragleave drop dragstart dragend pointerdown pointermove pointerup pointercancel pointerenter pointerleave pointerover pointerout gotpointercapture lostpointercapture ended loadedmetadata pagehide pageshow visibilitychange beforematch".split(" ");
            if (!(k instanceof Array)) {
                var v;
                var w = typeof Symbol != "undefined" && Symbol.iterator && k[Symbol.iterator];
                if (w) v = w.call(k);
                else if (typeof k.length == "number") v = {
                    next: g()
                };
                else throw Error(String(k) + " is not an iterable or ArrayLike");
                for (var x, y = []; !(x = v.next()).done;) y.push(x.value)
            };
            var z = function(a) {
                    return {
                        trigger: function(b) {
                            var c = a.j(b.type);
                            c || (r(a, b.type), c = a.j(b.type));
                            var e = b.target || b.srcElement;
                            c && c(b.type, b, e.ownerDocument.documentElement)
                        },
                        configure: function(b) {
                            b(a)
                        }
                    }
                }(function() {
                    var a = window,
                        b = new u(a.document.documentElement),
                        c = new p(b);
                    k.forEach(function(d) {
                        return r(c, d)
                    });
                    var e, h, f;
                    "onwebkitanimationend" in a && (e = "webkitAnimationEnd");
                    r(c, "animationend", e);
                    "onwebkitanimationstart" in a && (h = "webkitAnimationStart");
                    r(c, "animationstart", h);
                    r(c, "animationcancel");
                    "onwebkittransitionend" in
                    a && (f = "webkitTransitionEnd");
                    r(c, "transitionend", f);
                    return {
                        s: c,
                        container: b
                    }
                }().s),
                A = ["BOQ_wizbind"],
                C = window || l,
                D;
            for (; A.length && (D = A.shift());) A.length || z === void 0 ? C[D] && C[D] !== Object.prototype[D] ? C = C[D] : C = C[D] = {} : C[D] = z;
        }).call(this);
    </script>
    <script noCollect src="https://www.gstatic.com/_/mss/boq-one-google/_/js/k=boq-one-google.OneGoogleWidgetUi.ar.mNJ3BKy7U_U.es5.O/am=KAABAAbsAwAAAQ/d=1/excm=_b,_tp,appwidgetauthview/ed=1/dg=0/wt=2/ujg=1/rs=AM-SdHsp50lCEhqKOrz_RK0N7tJ2WqxJ1Q/m=_b,_tp"
        async id="base-js" fetchpriority="high" nonce="s24H2YaCsCRB820x9S0AJg"></script>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        if (window.BOQ_loadedInitialJS) {
            onJsLoad();
        } else {
            document.getElementById('base-js').addEventListener('load', onJsLoad, false);
        }
    </script>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        window['_wjdc'] = function(d) {
            window['_wjdd'] = d
        };
    </script>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        'use strict';

        function h(a) {
            this.data = a
        };

        function k(a) {
            this.h = a
        }

        function n(a, c) {
            p(a, c);
            return new k(a)
        }

        function q(a) {
            var c = new MessageChannel;
            p(c.port1, a);
            return c
        }

        function p(a, c) {
            c && (a.onmessage = function(f) {
                var d = f.data;
                n(f.ports[0]);
                c(new h(d))
            })
        };
        /*

         Copyright The Closure Library Authors.
         SPDX-License-Identifier: Apache-2.0
        */
        var r = /#|$/;

        function v(a) {
            var c = w,
                f = c.search(r);
            a: {
                var d = 0;
                for (var b = a.length;
                    (d = c.indexOf(a, d)) >= 0 && d < f;) {
                    var e = c.charCodeAt(d - 1);
                    if (e == 38 || e == 63)
                        if (e = c.charCodeAt(d + b), !e || e == 61 || e == 38 || e == 35) break a;
                    d += b + 1
                }
                d = -1
            }
            if (d < 0) return null;
            b = c.indexOf("&", d);
            if (b < 0 || b > f) b = f;
            d += a.length + 1;
            return decodeURIComponent(c.slice(d, b !== -1 ? b : 0).replace(/\+/g, " "))
        };
        var w = window.location.href,
            x = "ogi_" + (v("cn") || ""),
            y;

        function z(a, c, f) {
            c = c === void 0 ? {} : c;
            if (!y) {
                var d = v("origin") || "",
                    b = {
                        destination: window.parent,
                        origin: d,
                        g: x,
                        onMessage: void 0
                    };
                d = b.destination;
                var e = b.origin;
                var g = b.i === void 0 ? void 0 : b.i;
                var t = b.g === void 0 ? "ZNWN1d" : b.g;
                b = b.onMessage === void 0 ? void 0 : b.onMessage;
                if (e === "*") throw Error("Sending to wildcard origin not allowed.");
                var u = q(b),
                    l = {};
                g = g ? (l.n = t, l.t = g, l) : t;
                d.postMessage(g, e, [u.port2]);
                y = n(u.port1, b)
            }
            a = {
                event: a,
                data: c
            };
            c = y;
            var m = m === void 0 ? [] : m;
            f = q(f);
            c.h.postMessage(a, [f.port2].concat(m))
        };
        z("_startuploaded", {
            wt: "al"
        }, function(a) {
            window._ed = a.data._ed
        });
        (function(a) {
            document.readyState === "loading" ? document.addEventListener("DOMContentLoaded", function() {
                a()
            }) : a()
        })(function() {
            var a = a === void 0 ? "" : a;
            var c = c === void 0 ? "" : c;
            var f = f === void 0 ? !1 : f;
            var d = d === void 0 ? !1 : d;
            var b = {
                wt: "al"
            };
            if (document.querySelector("[data-ogmv]") != null) {
                var e = window.performance && window.performance.timing;
                b.ttf = e && e.responseEnd && e.fetchStart ? e.responseEnd - e.fetchStart : null;
                a && (b.height = a);
                c && (b.width = c);
                b.icss = f;
                b.dc = d;
                z("_renderstart", b)
            } else z("_renderfailed", b)
        });
    </script>
    <title></title>
    <script nonce="s24H2YaCsCRB820x9S0AJg">
        var AF_initDataKeys = ["ds:0"];
        var AF_dataServiceRequests = {
            'ds:0': {
                id: 'UVycre',
                request: []
            }
        };
        var AF_initDataChunkQueue = [];
        var AF_initDataCallback;
        var AF_initDataInitializeCallback;
        if (AF_initDataInitializeCallback) {
            AF_initDataInitializeCallback(AF_initDataKeys, AF_initDataChunkQueue, AF_dataServiceRequests);
        }
        if (!AF_initDataCallback) {
            AF_initDataCallback = function(chunk) {
                AF_initDataChunkQueue.push(chunk);
            };
        }
    </script>
</head>

<body jscontroller="pjICDe" jsaction="rcuQ6b:npT2md; click:FAbpgf; auxclick:FAbpgf" data-iw="1536" data-ih="742">
    <script aria-hidden="true" nonce="s24H2YaCsCRB820x9S0AJg">
        window.wiz_progress && window.wiz_progress();
    </script>
    <div class="MCcOAc IqBfM ecJEib EWZcud" id="yDmH0d">
        <div class="VUoKZ" aria-hidden="true">
            <div class="TRHLAc"></div>
        </div>
        <c-wiz jsrenderer="YOiC1e" class="SSPGKf" jsdata="deferred-i1" data-p="%.@.]" data-node-index="0;0" jsmodel="hc6Ubd" view c-wiz data-ogpc>
            <div class="T4LgNb eejsDc" jsname="a9kxte">
                <div jsname="qJTHM" class="kFwPee">
                    <c-wiz jsrenderer="IiCRgf" jslog="46975; track:impression;" jsshadow jsdata="deferred-i2" data-p="%.@.]" jscontroller="ORlaSe" jsaction="rcuQ6b:npT2md;" data-node-index="1;0" jsmodel="hc6Ubd kscufb" c-wiz>
                        <style nonce="YskSaovbS0tK60ZYl86vVQ">
                            .MrEfLc {
                                background-image: url('https://ssl.gstatic.com/gb/images/sprites/p_2x_3aa58e71cd98.png');
                                background-size: 53px 2953px;
                            }
                        </style>
                        <div class="qWuU9c" data-ogmv role="complementary">
                            <div class="EHzcec eejsDc mIM26c" jsname="Sx9Kwc" jsaction="kav0L:npT2md(preventDefault=true);qRPDvb:kvzNsb;UOCPhc:FybyJc;agoMJf:CfS0pe;UT22ib:Hp74Ud;GJ7MT:rfjeo;ArGk3:yBvnHf;r3gaLd:cPGk2d;" aria-label="تطبيقات Google">
                                <div class="v7bWUd">
                                    <div jscontroller="Ujeq8" jsaction="yE2nTc:dYPFnf;">
                                        <div class="VUoKZ" aria-hidden="true">
                                            <div class="TRHLAc"></div>
                                        </div>
                                    </div>
                                    <div class="o83JEf" jsname="sLJ6md">
                                        <div class="LVal7b ">
                                            <ul jsname="k77Iif" class="ngVsM u4RcUd">
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://myaccount.google.com/?utm_source=OGB&amp;utm_medium=app&amp;authuser=0" target="_blank" data-pid="192" jslog="46976; 1:192; track:click; index:0" jsname="hSRGPd" aria-label="‫الحساب، الصف رقم 1 من أصل 5 والعمود رقم 1 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc dOs7We" style="background-image: url('https://lh3.googleusercontent.com/a/ACg8ocIplVgYKG8O5Y1RZHJgg18I-wzrY6m63s1QYgXRov_X4kx5NmRs=s128-b16-cc-rp-mo');"></span></div><span jsname="V67aGc"
                                                            data-text='الحساب' class="Rq5Gcb">الحساب</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.google.com/?authuser=0" target="_blank" data-pid="1" jslog="46976; 1:1; track:click; index:1" jsname="hSRGPd" aria-label="‫بحث، الصف رقم 1 من أصل 5 والعمود رقم 2 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -406px;"></span></div><span jsname="V67aGc" data-text='بحث' class="Rq5Gcb">بحث</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://business.google.com/?gmbsrc=ww-ww-ot-gs-z-gmb-l-z-h~z-ogb-u&amp;authuser=0" target="_blank" data-pid="260" jslog="46976; 1:260; track:click; index:2" jsname="hSRGPd" aria-label="‫لوحة إدارة الملفات التجارية، الصف رقم 1 من أصل 5 والعمود رقم 3 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1914px;"></span></div><span jsname="V67aGc" data-text='لوحة إدارة الملفات التجارية' class="Rq5Gcb">لوحة إدارة الملفات التجارية</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://maps.google.com/?authuser=0" target="_blank" data-pid="8" jslog="46976; 1:8; track:click; index:3" jsname="hSRGPd" aria-label="‫خرائط Google، الصف رقم 2 من أصل 5 والعمود رقم 1 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2378px;"></span></div><span jsname="V67aGc" data-text='خرائط Google' class="Rq5Gcb">خرائط Google</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://news.google.com?authuser=0" target="_blank" data-pid="426" jslog="46976; 1:426; track:click; index:4" jsname="hSRGPd" aria-label="‫الأخبار، الصف رقم 2 من أصل 5 والعمود رقم 2 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -580px;"></span></div><span jsname="V67aGc" data-text='الأخبار' class="Rq5Gcb">الأخبار</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://meet.google.com?hs=197&amp;authuser=0" target="_blank" data-pid="411" jslog="46976; 1:411; track:click; index:5" jsname="hSRGPd" aria-label="‫Meet، الصف رقم 2 من أصل 5 والعمود رقم 3 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -638px;"></span></div><span jsname="V67aGc" data-text='Meet' dir="ltr" class="Rq5Gcb">Meet</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://contacts.google.com/?authuser=0" target="_blank" data-pid="53" jslog="46976; 1:53; track:click; index:6" jsname="hSRGPd" aria-label="‫جهات الاتصال، الصف رقم 3 من أصل 5 والعمود رقم 1 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1044px;"></span></div><span jsname="V67aGc" data-text='جهات الاتصال' class="Rq5Gcb">جهات الاتصال</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://drive.google.com/?authuser=0" target="_blank" data-pid="49" jslog="46976; 1:49; track:click; index:7" jsname="hSRGPd" aria-label="‫Drive، الصف رقم 3 من أصل 5 والعمود رقم 2 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1450px;"></span></div><span jsname="V67aGc" data-text='Drive' dir="ltr" class="Rq5Gcb">Drive</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://mail.google.com/mail/?authuser=0" target="_blank" data-pid="23" jslog="46976; 1:23; track:click; index:8" jsname="hSRGPd" aria-label="‫Gmail، الصف رقم 3 من أصل 5 والعمود رقم 3 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2030px;"></span></div><span jsname="V67aGc" data-text='Gmail' dir="ltr" class="Rq5Gcb">Gmail</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://calendar.google.com/calendar?authuser=0" target="_blank" data-pid="24" jslog="46976; 1:24; track:click; index:9" jsname="hSRGPd" aria-label="‫تقويم Google، الصف رقم 4 من أصل 5 والعمود رقم 1 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -870px;"></span></div><span jsname="V67aGc" data-text='تقويم Google' class="Rq5Gcb">تقويم Google</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://translate.google.com/?authuser=0" target="_blank" data-pid="51" jslog="46976; 1:51; track:click; index:10" jsname="hSRGPd" aria-label="‫ترجمة، الصف رقم 4 من أصل 5 والعمود رقم 2 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2088px;"></span></div><span jsname="V67aGc" data-text='ترجمة' class="Rq5Gcb">ترجمة</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://photos.google.com/?authuser=0" target="_blank" data-pid="31" jslog="46976; 1:31; track:click; index:11" jsname="hSRGPd" aria-label="‫الصور، الصف رقم 4 من أصل 5 والعمود رقم 3 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -696px;"></span></div><span jsname="V67aGc" data-text='الصور' class="Rq5Gcb">الصور</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://gemini.google.com?utm_source=app_launcher&amp;utm_medium=owned&amp;utm_campaign=base_all&amp;authuser=0" target="_blank" data-pid="658" jslog="46976; 1:658; track:click; index:12"
                                                        jsname="hSRGPd" aria-label="‫Gemini، الصف رقم 5 من أصل 5 والعمود رقم 1 من أصل 3 في القسم &quot;الأول&quot; (يتم فتح الرابط في علامة تبويب جديدة)." aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2668px;"></span></div><span jsname="V67aGc" data-text='Gemini' dir="ltr" class="Rq5Gcb">Gemini</span></a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="LVal7b ">
                                            <ul jsname="z5C9Gb" class="ngVsM L2gNYe">
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://docs.google.com/document/?usp=docs_alc&amp;authuser=0" target="_blank" data-pid="25" jslog="46976; 1:25; track:click; index:0" jsname="hSRGPd" aria-label="‫مستندات، الصف رقم 1 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -464px;"></span></div><span jsname="V67aGc" data-text='مستندات' class="Rq5Gcb">مستندات</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://docs.google.com/spreadsheets/?usp=sheets_alc&amp;authuser=0" target="_blank" data-pid="283" jslog="46976; 1:283; track:click; index:1" jsname="hSRGPd" aria-label="‫جداول بيانات، الصف رقم 1 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -116px;"></span></div><span jsname="V67aGc" data-text='جداول بيانات' class="Rq5Gcb">جداول بيانات</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://docs.google.com/presentation/?usp=slides_alc&amp;authuser=0" target="_blank" data-pid="281" jslog="46976; 1:281; track:click; index:2" jsname="hSRGPd" aria-label="‫العروض التقديمية من Google، الصف رقم 1 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2610px;"></span></div><span jsname="V67aGc" data-text='العروض التقديمية من Google' class="Rq5Gcb">العروض التقديمية من Google</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://books.google.com/?authuser=0" target="_blank" data-pid="10" jslog="46976; 1:10; track:click; index:3" jsname="hSRGPd" aria-label="‫الكتب، الصف رقم 2 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2784px;"></span></div><span jsname="V67aGc" data-text='الكتب' class="Rq5Gcb">الكتب</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.blogger.com/?authuser=0" target="_blank" data-pid="30" jslog="46976; 1:30; track:click; index:4" jsname="hSRGPd" aria-label="‫Blogger، الصف رقم 2 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2262px;"></span></div><span jsname="V67aGc" data-text='Blogger' dir="ltr" class="Rq5Gcb">Blogger</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://keep.google.com?authuser=0" target="_blank" data-pid="136" jslog="46976; 1:136; track:click; index:5" jsname="hSRGPd" aria-label="‫Keep، الصف رقم 2 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1334px;"></span></div><span jsname="V67aGc" data-text='Keep' dir="ltr" class="Rq5Gcb">Keep</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://classroom.google.com/?authuser=0" target="_blank" data-pid="265" jslog="46976; 1:265; track:click; index:6" jsname="hSRGPd" aria-label="‫فصل دراسي، الصف رقم 3 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2146px;"></span></div><span jsname="V67aGc" data-text='فصل دراسي' class="Rq5Gcb">فصل دراسي</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://earth.google.com/web/?authuser=0" target="_blank" data-pid="429" jslog="46976; 1:429; track:click; index:7" jsname="hSRGPd" aria-label="‫Earth، الصف رقم 3 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2726px;"></span></div><span jsname="V67aGc" data-text='Earth' dir="ltr" class="Rq5Gcb">Earth</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.google.com/save?authuser=0" target="_blank" data-pid="338" jslog="46976; 1:338; track:click; index:8" jsname="hSRGPd" aria-label="‫Google Saved، الصف رقم 3 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1566px;"></span></div><span jsname="V67aGc" data-text='Google Saved' dir="ltr" class="Rq5Gcb">Google Saved</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://artsandculture.google.com/?utm_source=ogs.google.com&amp;utm_medium=referral&amp;authuser=0" target="_blank" data-pid="264" jslog="46976; 1:264; track:click; index:9" jsname="hSRGPd"
                                                        aria-label="‫الفنون والثقافة، الصف رقم 4 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)." aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1218px;"></span></div><span jsname="V67aGc" data-text='الفنون والثقافة' class="Rq5Gcb">الفنون والثقافة</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://ads.google.com/home/<USER>" target="_blank" data-pid="304" jslog="46976; 1:304; track:click; index:10" jsname="hSRGPd" aria-label="‫إعلانات Google، الصف رقم 4 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2900px;"></span></div><span jsname="V67aGc" data-text='إعلانات Google' class="Rq5Gcb">إعلانات Google</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://one.google.com?utm_source=app_launcher&amp;utm_medium=web&amp;utm_campaign=all&amp;utm_content=google_oo&amp;authuser=0" target="_blank" data-pid="459" jslog="46976; 1:459; track:click; index:11"
                                                        jsname="hSRGPd" aria-label="‫Google One، الصف رقم 4 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)." aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -754px;"></span></div><span jsname="V67aGc" data-text='Google One' dir="ltr" class="Rq5Gcb">Google One</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.google.com/shopping?source=og&amp;authuser=0" target="_blank" data-pid="6" jslog="46976; 1:6; track:click; index:12" jsname="hSRGPd" aria-label="‫التسوّق، الصف رقم 5 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -174px;"></span></div><span jsname="V67aGc" data-text='التسوّق' class="Rq5Gcb">التسوّق</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://play.google.com/?authuser=0" target="_blank" data-pid="78" jslog="46976; 1:78; track:click; index:13" jsname="hSRGPd" aria-label="‫Play، الصف رقم 5 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1740px;"></span></div><span jsname="V67aGc" data-text='Play' dir="ltr" class="Rq5Gcb">Play</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.google.com/finance?authuser=0" target="_blank" data-pid="27" jslog="46976; 1:27; track:click; index:14" jsname="hSRGPd" aria-label="‫الأموال، الصف رقم 5 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1508px;"></span></div><span jsname="V67aGc" data-text='الأموال' class="Rq5Gcb">الأموال</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://myadcenter.google.com/?ref=app-launcher&amp;authuser=0" target="_blank" data-pid="644" jslog="46976; 1:644; track:click; index:15" jsname="hSRGPd" aria-label="‫مركز إدارة الإعلانات، الصف رقم 6 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -58px;"></span></div><span jsname="V67aGc" data-text='مركز إدارة الإعلانات' class="Rq5Gcb">مركز إدارة الإعلانات</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://chat.google.com?authuser=0" target="_blank" data-pid="385" jslog="46976; 1:385; track:click; index:16" jsname="hSRGPd" aria-label="‫Chat، الصف رقم 6 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2494px;"></span></div><span jsname="V67aGc" data-text='Chat' dir="ltr" class="Rq5Gcb">Chat</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://merchants.google.com?authuser=0" target="_blank" data-pid="711" jslog="46976; 1:711; track:click; index:17" jsname="hSRGPd" aria-label="‫Merchant Center، الصف رقم 6 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1624px;"></span></div><span jsname="V67aGc" data-text='Merchant Center' dir="ltr" class="Rq5Gcb">Merchant Center</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://www.google.com/travel/?dest_src=al&amp;authuser=0" target="_blank" data-pid="405" jslog="46976; 1:405; track:click; index:18" jsname="hSRGPd" aria-label="‫السفر، الصف رقم 7 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -1160px;"></span></div><span jsname="V67aGc" data-text='السفر' class="Rq5Gcb">السفر</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://docs.google.com/forms/?authuser=0" target="_blank" data-pid="330" jslog="46976; 1:330; track:click; index:19" jsname="hSRGPd" aria-label="‫نماذج، الصف رقم 7 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2436px;"></span></div><span jsname="V67aGc" data-text='نماذج' class="Rq5Gcb">نماذج</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://chrome.google.com/webstore?utm_source=app-launcher&amp;authuser=0" target="_blank" data-pid="421" jslog="46976; 1:421; track:click; index:20" jsname="hSRGPd" aria-label="‫سوق Chrome الإلكتروني، الصف رقم 7 من أصل 8 والعمود رقم 3 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 0;"></span></div><span jsname="V67aGc" data-text='سوق Chrome الإلكتروني' class="Rq5Gcb">سوق Chrome الإلكتروني</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://passwords.google.com?utm_source=OGB&amp;utm_medium=AL&amp;authuser=0" target="_blank" data-pid="674" jslog="46976; 1:674; track:click; index:21" jsname="hSRGPd" aria-label="‫مدير كلمات المرور، الصف رقم 8 من أصل 8 والعمود رقم 1 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)."
                                                        aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2552px;"></span></div><span jsname="V67aGc" data-text='مدير كلمات المرور' class="Rq5Gcb">مدير كلمات المرور</span></a>
                                                </li>
                                                <li class="j1ei8c" jscontroller="noLxJb" jsaction="rcuQ6b:npT2md; keydown:I481le;qUuEUd:rfjeo;j9grLe:Z8TOLc;HUObcd:Hp74Ud;">
                                                    <a class="tX9u1b" href="https://analytics.google.com/analytics/web?utm_source=OGB&amp;utm_medium=app&amp;authuser=0" target="_blank" data-pid="44" jslog="46976; 1:44; track:click; index:22" jsname="hSRGPd"
                                                        aria-label="‫إحصاءات Google، الصف رقم 8 من أصل 8 والعمود رقم 2 من أصل 3 في القسم &quot;الثاني&quot; (يتم فتح الرابط في علامة تبويب جديدة)." aria-grabbed="false" draggable="false">
                                                        <div class="pPUwub" aria-hidden="true"></div>
                                                        <div class="dKVyP" aria-hidden="true"></div>
                                                        <div class="ajYF5e" aria-hidden="true"></div>
                                                        <div class="NcWGte" aria-hidden="true"></div>
                                                        <div class="CgwTDb"><span class="MrEfLc" style="background-position: 0 -2842px;"></span></div><span jsname="V67aGc" data-text='إحصاءات Google' class="Rq5Gcb">إحصاءات Google</span></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="WwFbJd"><a href="https://about.google/products/" class="NQV3m" jsname="TiWzT" data-app-widget-link-name="jcJzye" target="_blank" aria-label="المزيد من Google (يتم فتح الرابط في علامة تبويب جديدة)." jslog="51957; track:click;">المزيد من Google</a></div>
                                </div>
                            </div>
                        </div>
                        <c-data id="i2" jsdata=" wy9EHc;_;1"></c-data>
                    </c-wiz>
                </div>
            </div>
            <c-data id="i1"></c-data>
        </c-wiz>
        <script aria-hidden="true" nonce="s24H2YaCsCRB820x9S0AJg">
            window.wiz_progress && window.wiz_progress();
            window.wiz_tick && window.wiz_tick('YOiC1e');
        </script>
        <script nonce="s24H2YaCsCRB820x9S0AJg">
            (function() {
                'use strict';
                var c = window,
                    d = [];
                c.aft_counter = d;
                var e = [],
                    f = 0;

                function _recordIsAboveFold(a) {
                    if (!c._isLazyImage(a) && !a.hasAttribute("data-noaft") && a.src) {
                        var b = (c._isVisible || function() {})(c.document, a);
                        a.setAttribute("data-atf", b);
                        b && (e.indexOf(a) !== -1 || d.indexOf(a) !== -1 || a.complete || d.push(a), a.hasAttribute("data-iml") && (a = Number(a.getAttribute("data-iml")), a > f && (f = a)))
                    }
                }
                c.initAft = function() {
                    f = 0;
                    e = Array.prototype.slice.call(document.getElementsByTagName("img")).filter(function(a) {
                        return !!a.getAttribute("data-iml")
                    });
                    [].forEach.call(document.getElementsByTagName("img"), function(a) {
                        try {
                            _recordIsAboveFold(a)
                        } catch (b) {
                            throw b.message = a.hasAttribute("data-iid") ? b.message + "\nrecordIsAboveFold error for defer inlined image" : b.message + ("\nrecordIsAboveFold error for img element with <src: " + a.src + ">"), b;
                        }
                    });
                    if (d.length === 0) c.onaft(f)
                };
            }).call(this);
            initAft()
        </script>
        <script id="_ij" nonce="s24H2YaCsCRB820x9S0AJg">
            window.IJ_values = [
                ["112981580452882507747", "112981580452882507747", "0", true, null, null, true, false], '0', 'https:\/\/ogs.google.com\/u\/0\/', null, 'boq_onegooglehttpserver_20250525.03_p0', 'ogs.google.com', 0.0, '', 's24H2YaCsCRB820x9S0AJg', 'YskSaovbS0tK60ZYl86vVQ', 'DEFAULT', '\/u\/0', 2025.0, 'https:\/\/ogs.google.com\/widget\/app', null, 'rtl', false, 'https:\/\/accounts.google.com\/AccountChooser?continue\x3dhttps:\/\/ogs.google.com\/u\/0\/widget\/app?eom%3D1%26awwd%3D1%26gdafe%3D1%26origin%3Dhttps:\/\/www.google.com%26cn%3Dapp%26pid%3D1%26spid%3D113%26hl%3Dar\x26hl\x3dar', 'https:\/\/accounts.google.com\/ServiceLogin?hl\x3dar\x26authuser\x3d0\x26continue\x3dhttps:\/\/ogs.google.com\/u\/0\/widget\/app?eom%3D1%26awwd%3D1%26gdafe%3D1%26origin%3Dhttps:\/\/www.google.com%26cn%3Dapp%26pid%3D1%26spid%3D113%26hl%3Dar', 'https:\/\/accounts.google.com\/SignOutOptions?continue\x3dhttps:\/\/ogs.google.com\/u\/0\/widget\/app?eom%3D1%26awwd%3D1%26gdafe%3D1%26origin%3Dhttps:\/\/www.google.com%26cn%3Dapp%26pid%3D1%26spid%3D113%26hl%3Dar', false, 'https:\/\/www.google.com', false, false, false, true, false, false, 'ar', 'ar', 'ar', 40.0, 'https:\/\/goto2.corp.google.com\/mdtredirect?data_id_filter\x3dogs.google.com\x26system_name\x3done-google-http-server', null, 'ALHxRM_4xP-lN979aRWaVpasWYd-:*************', 'https:\/\/myaccount.google.com\/privacypolicy?hl\x3dar', true, null, false, 'https:\/\/www.gstatic.com\/_\/boq-one-google\/_\/r\/', false, 'https:\/\/myaccount.google.com\/termsofservice?hl\x3dar', 0.0, 'ar', '<EMAIL>', true, '112981580452882507747',
            ];
            window.IJ_valuesCb && window.IJ_valuesCb();
        </script>
        <script class="ds:0" nonce="s24H2YaCsCRB820x9S0AJg">
            AF_initDataCallback({
                key: 'ds:0',
                hash: '1',
                data: [
                    [
                        [
                            [192, "الحساب", "0 -348px", "https://myaccount.google.com/?utm_source\u003dOGB\u0026utm_medium\u003dapp\u0026authuser\u003d0", "_blank", null, null, "https://lh3.googleusercontent.com/a/ACg8ocIplVgYKG8O5Y1RZHJgg18I-wzrY6m63s1QYgXRov_X4kx5NmRs\u003ds128-b16-cc-rp-mo"],
                            [1, "بحث", "0 -406px", "https://www.google.com/?authuser\u003d0", "_blank"],
                            [260, "لوحة إدارة الملفات التجارية", "0 -1914px", "https://business.google.com/?gmbsrc\u003dww-ww-ot-gs-z-gmb-l-z-h~z-ogb-u\u0026authuser\u003d0", "_blank"],
                            [8, "خرائط Google", "0 -2378px", "https://maps.google.com/?authuser\u003d0", "_blank"],
                            [426, "الأخبار", "0 -580px", "https://news.google.com?authuser\u003d0", "_blank"],
                            [411, "Meet", "0 -638px", "https://meet.google.com?hs\u003d197\u0026authuser\u003d0", "_blank"],
                            [53, "جهات الاتصال", "0 -1044px", "https://contacts.google.com/?authuser\u003d0", "_blank"],
                            [49, "Drive", "0 -1450px", "https://drive.google.com/?authuser\u003d0", "_blank"],
                            [23, "Gmail", "0 -2030px", "https://mail.google.com/mail/?authuser\u003d0", "_blank"],
                            [24, "تقويم Google", "0 -870px", "https://calendar.google.com/calendar?authuser\u003d0", "_blank"],
                            [51, "ترجمة", "0 -2088px", "https://translate.google.com/?authuser\u003d0", "_blank"],
                            [31, "الصور", "0 -696px", "https://photos.google.com/?authuser\u003d0", "_blank"],
                            [658, "Gemini", "0 -2668px", "https://gemini.google.com?utm_source\u003dapp_launcher\u0026utm_medium\u003downed\u0026utm_campaign\u003dbase_all\u0026authuser\u003d0", "_blank"]
                        ],
                        [
                            [25, "مستندات", "0 -464px", "https://docs.google.com/document/?usp\u003ddocs_alc\u0026authuser\u003d0", "_blank"],
                            [283, "جداول بيانات", "0 -116px", "https://docs.google.com/spreadsheets/?usp\u003dsheets_alc\u0026authuser\u003d0", "_blank"],
                            [281, "العروض التقديمية من Google", "0 -2610px", "https://docs.google.com/presentation/?usp\u003dslides_alc\u0026authuser\u003d0", "_blank"],
                            [10, "الكتب", "0 -2784px", "https://books.google.com/?authuser\u003d0", "_blank"],
                            [30, "Blogger", "0 -2262px", "https://www.blogger.com/?authuser\u003d0", "_blank"],
                            [136, "Keep", "0 -1334px", "https://keep.google.com?authuser\u003d0", "_blank"],
                            [265, "فصل دراسي", "0 -2146px", "https://classroom.google.com/?authuser\u003d0", "_blank"],
                            [429, "Earth", "0 -2726px", "https://earth.google.com/web/?authuser\u003d0", "_blank"],
                            [338, "Google Saved", "0 -1566px", "https://www.google.com/save?authuser\u003d0", "_blank"],
                            [264, "الفنون والثقافة", "0 -1218px", "https://artsandculture.google.com/?utm_source\u003dogs.google.com\u0026utm_medium\u003dreferral\u0026authuser\u003d0", "_blank"],
                            [304, "إعلانات Google", "0 -2900px", "https://ads.google.com/home/<USER>", "_blank"],
                            [459, "Google One", "0 -754px", "https://one.google.com?utm_source\u003dapp_launcher\u0026utm_medium\u003dweb\u0026utm_campaign\u003dall\u0026utm_content\u003dgoogle_oo\u0026authuser\u003d0", "_blank"],
                            [6, "التسوّق", "0 -174px", "https://www.google.com/shopping?source\u003dog\u0026authuser\u003d0", "_blank"],
                            [78, "Play", "0 -1740px", "https://play.google.com/?authuser\u003d0", "_blank"],
                            [27, "الأموال", "0 -1508px", "https://www.google.com/finance?authuser\u003d0", "_blank"],
                            [644, "مركز إدارة الإعلانات", "0 -58px", "https://myadcenter.google.com/?ref\u003dapp-launcher\u0026authuser\u003d0", "_blank"],
                            [385, "Chat", "0 -2494px", "https://chat.google.com?authuser\u003d0", "_blank"],
                            [711, "Merchant Center", "0 -1624px", "https://merchants.google.com?authuser\u003d0", "_blank"],
                            [405, "السفر", "0 -1160px", "https://www.google.com/travel/?dest_src\u003dal\u0026authuser\u003d0", "_blank"],
                            [330, "نماذج", "0 -2436px", "https://docs.google.com/forms/?authuser\u003d0", "_blank"],
                            [421, "سوق Chrome الإلكتروني", "0 0", "https://chrome.google.com/webstore?utm_source\u003dapp-launcher\u0026authuser\u003d0", "_blank"],
                            [674, "مدير كلمات المرور", "0 -2552px", "https://passwords.google.com?utm_source\u003dOGB\u0026utm_medium\u003dAL\u0026authuser\u003d0", "_blank"],
                            [44, "إحصاءات Google", "0 -2842px", "https://analytics.google.com/analytics/web?utm_source\u003dOGB\u0026utm_medium\u003dapp\u0026authuser\u003d0", "_blank"]
                        ], null, "https://workspace.google.com/marketplace?pann\u003dogb\u0026authuser\u003d0", "المزيد من Google Workspace Marketplace", "https://about.google/products/", null, null, null, null, "https://ssl.gstatic.com/gb/images/sprites/p_2x_3aa58e71cd98.png", "53px 2953px", 1, 0, "https://ssl.gstatic.com/gb/images/sprites/p_1x_3ce0e950d407.png", "", "المزيد من Google", null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, [0]
                    ]
                ],
                sideChannel: {}
            });
        </script>
        <script id="wiz_jd" nonce="s24H2YaCsCRB820x9S0AJg">
            if (window['_wjdc']) {
                const wjd = {};
                window['_wjdc'](wjd);
                delete window['_wjdc'];
            }
        </script>
        <script aria-hidden="true" id="WIZ-footer" nonce="s24H2YaCsCRB820x9S0AJg">
            window.wiz_progress && window.wiz_progress();
            window.stopScanForCss && window.stopScanForCss();
            ccTick('bl');
        </script>
</body>

</html>