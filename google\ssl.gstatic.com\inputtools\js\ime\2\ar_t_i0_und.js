(function() {
    var d = "\u061f",
        e = "\u061b",
        f = "\u060c",
        g = "-";
    google.elements.ime.loadConfig("ar-t-i0-und", function() {
        var a = {
            ",": f,
            ";": e,
            "?": d
        };
        return {
            0: 0,
            1: 0,
            2: !0,
            3: !0,
            4: !1,
            5: !1,
            6: !1,
            7: !1,
            8: !1,
            9: !0,
            10: !1,
            28: !0,
            11: !0,
            12: !0,
            13: 50,
            14: 6,
            15: 1,
            16: null,
            19: function(h, b) {
                var c = {
                    back: 0
                };
                return b in a ? (c.text = a[b], c) : null
            },
            21: function(a, b) {
                return (a + b).match(/^([aei]l) /i) ? {
                    back: 0,
                    text: g
                } : null
            },
            22: /[a-z0-9`_\-\' ]/i,
            27: /[^a-z0-9`_\-'\u0600-\u06FF]/i
        }
    }());
})()