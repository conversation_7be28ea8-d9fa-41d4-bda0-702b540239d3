(function() {
    'use strict';
    google.elements.keyboard.loadme({
        id: "ar",
        title: "\u0644\u0648\u062d\u0629 \u0645\u0641\u0627\u062a\u064a\u062d \u0627\u0644\u0644\u063a\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629",
        direction: "rtl",
        mappings: {
            "scl,sc,sl,s": {
                "\u00c01234567890m=": "\u0651!@#$%^&*)(_+",
                QWER: "\u064e\u064b\u064f\u064c",
                T: "\u0644\u0625",
                "YUIOP\u00db\u00dd\u00dc": "\u0625\u2018\u00f7\u00d7\u061b<>|",
                ASDF: "\u0650\u064d][",
                G: "\u0644\u0623",
                "HJKL;\u00de": '\u0623\u0640\u060c/:"',
                ZXCV: "~\u0652}{",
                B: "\u0644\u0622",
                "NM\u00bc\u00be\u00bf": "\u0622\u2019,.\u061f"
            },
            "cl,l,c,": {
                "\u00c01234567890m=": "\u0630\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669\u0660-=",
                "QWERTYUIOP\u00db\u00dd\u00dc": "\u0636\u0635\u062b\u0642\u0641\u063a\u0639\u0647\u062e\u062d\u062c\u062f\\",
                "ASDFGHJKL;\u00de": "\u0634\u0633\u064a\u0628\u0644\u0627\u062a\u0646\u0645\u0643\u0637",
                ZXCV: "\u0626\u0621\u0624\u0631",
                B: "\u0644\u0627",
                "NM\u00bc\u00be\u00bf": "\u0649\u0629\u0648\u0632\u0638"
            }
        }
    });
}).call(this);