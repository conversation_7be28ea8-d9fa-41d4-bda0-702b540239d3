.HrROHe,
.PpaGLb {
    font-size: 13px;
    font-weight: 500;
    text-transform: uppercase
}

.PpaGLb {
    color: #0b57d0
}

.HrROHe,
.PpaGLb,
.XbJon,
.ZqLNQd {
    cursor: pointer
}

.HrROHe:disabled,
.PpaGLb:disabled,
.XbJon:disabled,
.ZqLNQd:disabled {
    cursor: default
}

.ZqLNQd {
    text-align: right
}

.ZqLNQd:hover {
    text-decoration: underline
}

.ZqLNQd:hover:disabled {
    text-decoration: none
}

.kyuRq,
.kyuRq.fontBodyLarge {
    color: #0b57d0;
    cursor: pointer;
    text-align: right
}

.kyuRq:disabled,
.kyuRq.fontBodyLarge:disabled {
    cursor: default
}

.kyuRq:hover,
.kyuRq.fontBodyLarge:hover {
    text-decoration: underline
}

.kyuRq:hover:disabled,
.kyuRq.fontBodyLarge:hover:disabled {
    text-decoration: none
}

.W3ArWe {
    background-color: #1b6ef3;
    border-radius: 2px;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    margin-top: 12px;
    padding: 8px 16px;
    text-transform: uppercase
}

@media screen and (forced-colors:active) {
    .W3ArWe {
        border: 1px solid transparent
    }
}

.W3ArWe:hover {
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 3px 1px -2px rgba(0, 0, 0, .12), 0 1px 5px 0 rgba(0, 0, 0, .2);
    -webkit-transition: box-shadow .1s ease-in-out;
    transition: box-shadow .1s ease-in-out
}

.XbJon {
    color: #0b57d0;
    border-radius: 6px;
    border: 1px solid transparent;
    overflow: hidden;
    padding: 7px 9px;
    position: relative;
    margin: 6px 0
}

.XbJon.HS8nfd {
    padding: 3px 9px
}

.XbJon:hover .OyjIsf {
    background-color: rgba(26, 115, 232, 0.08)
}

body:not(.LoJzbe) .XbJon:focus .OyjIsf {
    background-color: rgba(26, 115, 232, 0.12)
}

body:not(.LoJzbe) .XbJon:hover:focus .OyjIsf {
    background-color: rgba(26, 115, 232, 0.16)
}

.XbJon:active .OyjIsf {
    background-color: rgba(26, 115, 232, 0.12)
}

@media screen and (forced-colors:none) {
    .XbJon.XbJon:focus {
        outline: none
    }
}

.BunUDe {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    border-radius: 40px;
    box-sizing: border-box;
    color: #0b57d0;
    height: 40px;
    margin: 4px 0;
    overflow: hidden;
    padding-right: 12px;
    padding-left: 12px;
    position: relative
}

.G6JA1c {
    color: #1f1f1f
}

.G6JA1c:hover,
.fvQUnc .G6JA1c:focus {
    color: #0b57d0
}

.fOEg2b {
    color: #fff
}

html[dir=rtl] .G47vBd {
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1)
}

.NhBTye {
    font-variation-settings: "FILL" 1
}

.wBkYDb {
    color: #b26c00
}

.FZt9Uc {
    color: #ffbb29
}

.gnOR4e {
    color: #e3e3e3
}

.c2oMlb {
    color: #0b57d0
}

.ydCpf {
    color: #e35a1b
}

.ZDGTec {
    color: #c54105
}

.jE2ERb {
    color: #4285f3
}

.JUDSqb {
    color: #dc362e
}

.PsOCtf {
    color: #421f00
}

.bxESbc {
    color: #1f1f1f
}

.VDEjyc {
    color: #fff
}

.juHP0c {
    color: #fff
}

.c1G10c {
    color: #8f8f8f
}

.uc9Kqc {
    color: #1f1f1f
}

.nnBhCe {
    color: #1f1f1f
}

.AWETxc {
    color: #fff
}

.N23lEd {
    color: #0b57d0
}

.SwaGS {
    color: #1f1f1f
}

.OazX1c {
    color: #5e5e5e
}

.q3CStc {
    border-color: #c7c7c7;
    color: #c7c7c7
}

.Q6EWEd {
    color: #198639
}

.PHazN {
    color: #0b57d0
}

.iORfaf {
    background-color: #1b6ef3
}

.zw8yee {
    color: #00639b
}

.KP9RVd {
    color: #041e49
}

.hCdRMc {
    color: #0b57d0
}

.IheHDf {
    color: #b3261e
}

.bBoBtc {
    color: #410e0b
}

.af7cUc {
    color: #945700
}

.thAX0c {
    color: #421f00
}

.JTqyM {
    color: #146c2e
}

.Cdyak {
    color: #072711
}

.x87uTb {
    color: #0b57d0
}

.YtqBU {
    background-color: #ecf3fe
}

.elGi1d {
    color: #ffbb29
}

.B1yWAf {
    color: #fff
}

.fontDisplayLarge {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 2.8125rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 3.25rem
}

.fontDisplayMedium {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1.75rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 2.25rem
}

.lfPIob {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1.375rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.75rem
}

.fontHeadlineLarge {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 1.75rem
}

.fontHeadlineMedium {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1.125rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 1.5rem
}

.fontHeadlineSmall {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 1.25rem
}

.fontTitleLarge {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1.25rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 1.5rem
}

.fontTitleMedium {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem
}

.fontTitleSmall {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem
}

.fontBodyLarge {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.00625rem;
    line-height: 1.5rem;
    color: #1f1f1f
}

.fontBodyMedium {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem
}

.fontBodySmall {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 0.0125rem;
    line-height: 1rem;
    color: #5e5e5e
}

.J7sVM {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.6875rem;
    font-weight: 500;
    letter-spacing: 0.0125rem;
    line-height: 0.75rem
}

.fontLabelMedium {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.75rem;
    font-weight: 500;
    letter-spacing: 0.0125rem;
    line-height: 1rem
}

.igUw4d {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: .75rem;
    font-weight: 500;
    letter-spacing: .0125rem;
    line-height: 1rem;
    color: #5e5e5e
}

.NlVald {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem;
    text-transform: none
}

.dKzzJf {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: .875rem;
    font-weight: 500;
    letter-spacing: .00625rem;
    line-height: 1.25rem;
    text-transform: none
}

.gm2-headline-3 {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    line-height: 2.25rem;
    font-size: 1.75rem;
    letter-spacing: 0;
    font-weight: 400
}

.Hi2drd {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0rem;
    line-height: 1.25rem;
    font-weight: 400
}

.Y8qwKc {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem;
    font-family: "Google Sans", Roboto, Arial, sans-serif
}

.j39qH {
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 0.0125rem;
    line-height: 1rem;
    color: #1f1f1f;
    font-family: "Google Sans", Roboto, Arial, sans-serif
}

.Tc0rEd {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15)
}

.yu5kgd {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15)
}

.hdeJwf {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 3px rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15)
}

.LCTIRd {
    border: 1px solid #c7c7c7
}

.eKbjU {
    border-top: 1px solid #c7c7c7
}

.SPRMud {
    border-left: 1px solid #c7c7c7
}

.XDi3Bc {
    border-bottom: 1px solid #c7c7c7
}

.Llk9yb {
    border-right: 1px solid #c7c7c7
}

.google-symbols {
    font-family: "Google Symbols";
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: rtl;
    font-optical-sizing: none
}

.uj8Yqe {
    -webkit-animation: container-rotate 1568ms linear infinite;
    animation: container-rotate 1568ms linear infinite;
    height: 100%;
    width: 100%
}

@-webkit-keyframes container-rotate {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes container-rotate {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.VjANhb {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0
}

.VjANhb.VXJmff {
    border-color: #1a73e8;
    -webkit-animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, blue-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, blue-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both
}

.VjANhb.juFtAf {
    border-color: #d33b30;
    -webkit-animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, red-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, red-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both
}

.VjANhb.a8TBub {
    border-color: #f9ab00;
    -webkit-animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, yellow-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, yellow-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both
}

.VjANhb.E626Jd {
    border-color: #28994f;
    -webkit-animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, green-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both, green-fade-in-out 5332ms cubic-bezier(.4, 0, .2, 1) infinite both
}

.VjANhb.G1njrf {
    border-color: #fff;
    opacity: 1;
    -webkit-animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: fill-unfill-rotate 5332ms cubic-bezier(.4, 0, .2, 1) infinite both
}

@-webkit-keyframes fill-unfill-rotate {
    12.5% {
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg)
    }
    25% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg)
    }
    37.5% {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
    }
    50% {
        -webkit-transform: rotate(540deg);
        transform: rotate(540deg)
    }
    62.5% {
        -webkit-transform: rotate(675deg);
        transform: rotate(675deg)
    }
    75% {
        -webkit-transform: rotate(810deg);
        transform: rotate(810deg)
    }
    87.5% {
        -webkit-transform: rotate(945deg);
        transform: rotate(945deg)
    }
    to {
        -webkit-transform: rotate(3turn);
        transform: rotate(3turn)
    }
}

@keyframes fill-unfill-rotate {
    12.5% {
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg)
    }
    25% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg)
    }
    37.5% {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
    }
    50% {
        -webkit-transform: rotate(540deg);
        transform: rotate(540deg)
    }
    62.5% {
        -webkit-transform: rotate(675deg);
        transform: rotate(675deg)
    }
    75% {
        -webkit-transform: rotate(810deg);
        transform: rotate(810deg)
    }
    87.5% {
        -webkit-transform: rotate(945deg);
        transform: rotate(945deg)
    }
    to {
        -webkit-transform: rotate(3turn);
        transform: rotate(3turn)
    }
}

@-webkit-keyframes blue-fade-in-out {
    from {
        opacity: 1
    }
    25% {
        opacity: 1
    }
    26% {
        opacity: 0
    }
    89% {
        opacity: 0
    }
    90% {
        opacity: 1
    }
    100% {
        opacity: 1
    }
}

@keyframes blue-fade-in-out {
    from {
        opacity: 1
    }
    25% {
        opacity: 1
    }
    26% {
        opacity: 0
    }
    89% {
        opacity: 0
    }
    90% {
        opacity: 1
    }
    100% {
        opacity: 1
    }
}

@-webkit-keyframes red-fade-in-out {
    from {
        opacity: 0
    }
    15% {
        opacity: 0
    }
    25% {
        opacity: 1
    }
    50% {
        opacity: 1
    }
    51% {
        opacity: 0
    }
}

@keyframes red-fade-in-out {
    from {
        opacity: 0
    }
    15% {
        opacity: 0
    }
    25% {
        opacity: 1
    }
    50% {
        opacity: 1
    }
    51% {
        opacity: 0
    }
}

@-webkit-keyframes yellow-fade-in-out {
    from {
        opacity: 0
    }
    40% {
        opacity: 0
    }
    50% {
        opacity: 1
    }
    75% {
        opacity: 1
    }
    76% {
        opacity: 0
    }
}

@keyframes yellow-fade-in-out {
    from {
        opacity: 0
    }
    40% {
        opacity: 0
    }
    50% {
        opacity: 1
    }
    75% {
        opacity: 1
    }
    76% {
        opacity: 0
    }
}

@-webkit-keyframes green-fade-in-out {
    from {
        opacity: 0
    }
    65% {
        opacity: 0
    }
    75% {
        opacity: 1
    }
    90% {
        opacity: 1
    }
    100% {
        opacity: 0
    }
}

@keyframes green-fade-in-out {
    from {
        opacity: 0
    }
    65% {
        opacity: 0
    }
    75% {
        opacity: 1
    }
    90% {
        opacity: 1
    }
    100% {
        opacity: 0
    }
}

.yD92Ze {
    position: absolute;
    box-sizing: border-box;
    top: 0;
    right: 45%;
    width: 10%;
    height: 100%;
    overflow: hidden;
    border-color: inherit
}

.yD92Ze .q0z1yb {
    width: 1000%;
    right: -450%
}

.wl6qec {
    display: inline-block;
    position: relative;
    width: 50%;
    height: 100%;
    overflow: hidden;
    border-color: inherit
}

.wl6qec .q0z1yb {
    width: 200%
}

.q0z1yb {
    box-sizing: border-box;
    height: 100%;
    border-width: 3px;
    border-style: solid;
    border-color: inherit;
    border-bottom-color: transparent !important;
    border-radius: 50%;
    -webkit-animation: none;
    animation: none
}

.wl6qec.xvJMLe .q0z1yb {
    border-left-color: transparent !important;
    -webkit-transform: rotate(-129deg);
    transform: rotate(-129deg)
}

.wl6qec.k7Bifb .q0z1yb {
    right: -100%;
    border-right-color: transparent !important;
    -webkit-transform: rotate(129deg);
    transform: rotate(129deg)
}

.wl6qec.xvJMLe .q0z1yb {
    -webkit-animation: left-spin 1333ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: left-spin 1333ms cubic-bezier(.4, 0, .2, 1) infinite both
}

.wl6qec.k7Bifb .q0z1yb {
    -webkit-animation: right-spin 1333ms cubic-bezier(.4, 0, .2, 1) infinite both;
    animation: right-spin 1333ms cubic-bezier(.4, 0, .2, 1) infinite both
}

@-webkit-keyframes left-spin {
    from {
        -webkit-transform: rotate(130deg);
        transform: rotate(130deg)
    }
    50% {
        -webkit-transform: rotate(-5deg);
        transform: rotate(-5deg)
    }
    to {
        -webkit-transform: rotate(130deg);
        transform: rotate(130deg)
    }
}

@keyframes left-spin {
    from {
        -webkit-transform: rotate(130deg);
        transform: rotate(130deg)
    }
    50% {
        -webkit-transform: rotate(-5deg);
        transform: rotate(-5deg)
    }
    to {
        -webkit-transform: rotate(130deg);
        transform: rotate(130deg)
    }
}

@-webkit-keyframes right-spin {
    from {
        -webkit-transform: rotate(-130deg);
        transform: rotate(-130deg)
    }
    50% {
        -webkit-transform: rotate(5deg);
        transform: rotate(5deg)
    }
    to {
        -webkit-transform: rotate(-130deg);
        transform: rotate(-130deg)
    }
}

@keyframes right-spin {
    from {
        -webkit-transform: rotate(-130deg);
        transform: rotate(-130deg)
    }
    50% {
        -webkit-transform: rotate(5deg);
        transform: rotate(5deg)
    }
    to {
        -webkit-transform: rotate(-130deg);
        transform: rotate(-130deg)
    }
}

@-webkit-keyframes fade-out {
    from {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@keyframes fade-out {
    from {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

.CiOaN {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0
}

html,
body {
    height: 100%;
    margin: 0;
    padding: 0
}

hr {
    background: #e3e3e3;
    border: none;
    height: 1px;
    margin: 8px 0
}

body {
    touch-action: none;
    overflow: hidden
}

a,
button,
h1,
h2,
h3,
h4,
h5,
h6,
input,
ol,
p,
textarea,
th,
ul {
    background: transparent;
    border-radius: 0;
    border: 0;
    font: inherit;
    list-style: none;
    margin: 0;
    outline: 0;
    overflow: visible;
    padding: 0;
    vertical-align: baseline
}

textarea {
    overflow: auto
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

button::-moz-focus-inner,
input::-moz-focus-inner,
textarea::-moz-focus-inner {
    margin: 0;
    padding: 0;
    border: 0
}

button,
input,
textarea {
    color: inherit
}

input::-ms-clear {
    display: none
}

a {
    color: #0b57d0;
    cursor: pointer;
    text-decoration: none;
    outline: none
}

a:hover {
    text-decoration: underline
}

:focus {
    outline: none
}

.clearfix::after {
    content: "";
    display: table;
    clear: both
}

.fvQUnc *:focus {
    outline: 2px solid #0b57d0
}

.id-app-container {
    background: #1b6ef3;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden
}

.Pe4b {
    position: absolute;
    left: 16px;
    top: 64px;
    z-index: 2
}

.OkO9ve-haAclf {
    top: 4px;
    left: 0;
    position: absolute
}

.OkO9ve {
    margin: 8px 0 0 8px;
    position: fixed;
    left: 0;
    z-index: 16
}

.OkO9ve #gb {
    min-width: 0;
    width: auto;
    left: 0;
    right: auto
}

.hUbt4d-watermark {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0
}

.xcUKcd.eZfyae .hUbt4d-watermark {
    right: 72px
}

.xcUKcd.y2iKwd .hUbt4d-watermark {
    right: 408px
}

.xcUKcd.eZfyae.y2iKwd .hUbt4d-watermark {
    right: 480px
}

.xcUKcd.y2iKwd.S38FBb .hUbt4d-watermark {
    right: 836px
}

.xcUKcd.y2iKwd.VElZUe .hUbt4d-watermark {
    right: 788px
}

.xcUKcd.eZfyae.y2iKwd.S38FBb .hUbt4d-watermark {
    right: 908px
}

.xcUKcd.eZfyae.y2iKwd.VElZUe .hUbt4d-watermark {
    right: 860px
}

.pane-open-mode .hUbt4d-watermark {
    right: 408px
}

.app-vertical-widget-holder {
    pointer-events: none;
    position: absolute;
    bottom: 33px;
    z-index: 4;
    left: 20px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-flow: column wrap;
    flex-flow: column wrap;
    height: calc(100vh - 100px);
    -webkit-box-align: end;
    -webkit-align-items: end;
    align-items: end;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -webkit-column-gap: 5px;
    column-gap: 5px;
    min-width: 29px;
    max-width: 63px
}

.app-vertical-item {
    pointer-events: auto;
    margin-top: 3px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.app-horizontal-widget-holder {
    pointer-events: auto;
    position: absolute;
    left: 20px;
    bottom: 0;
    white-space: nowrap;
    z-index: 1;
    max-height: 30px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.app-horizontal-item {
    position: relative;
    float: right;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.app-viewcard-strip {
    position: absolute;
    z-index: 1;
    right: 0;
    left: 0;
    bottom: 0;
    -webkit-transition: right .2s cubic-bezier(0, 0, .2, 1);
    transition: right .2s cubic-bezier(0, 0, .2, 1)
}

.xcUKcd.eZfyae .app-viewcard-strip {
    right: 72px
}

.xcUKcd.y2iKwd .app-viewcard-strip {
    right: 408px
}

.xcUKcd.eZfyae.y2iKwd .app-viewcard-strip {
    right: 480px
}

.xcUKcd.y2iKwd.S38FBb .app-viewcard-strip {
    right: 836px
}

.xcUKcd.y2iKwd.VElZUe .app-viewcard-strip {
    right: 788px
}

.xcUKcd.eZfyae.y2iKwd.S38FBb .app-viewcard-strip {
    right: 908px
}

.xcUKcd.eZfyae.y2iKwd.VElZUe .app-viewcard-strip {
    right: 860px
}

.pane-open-mode .app-viewcard-strip {
    right: 408px
}

.HdXONd {
    opacity: 1;
    -webkit-transition: opacity .2s linear;
    transition: opacity .2s linear
}

.minimap-state-changing .HdXONd {
    opacity: 0
}

.app-bottom-content-anchor {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 0;
    bottom: 100%;
    margin-bottom: 20px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.full-width-minimap .app-bottom-content-anchor {
    margin-bottom: 8px
}

.app-bottom-content-anchor.widgets-above-runway {
    margin-bottom: 20px
}

.app-center-widget-holder {
    pointer-events: auto;
    position: absolute;
    right: 50%;
    bottom: 0;
    -webkit-transition: padding-bottom .1s linear, bottom .1s linear, margin-bottom .1s linear;
    transition: padding-bottom .1s linear, bottom .1s linear, margin-bottom .1s linear;
    -webkit-transform: translateX(50%);
    transform: translateX(50%)
}

@media only screen and (max-width:632px) {
    .app-center-widget-holder {
        padding-bottom: 33px
    }
}

@media only screen and (max-width:500px) {
    .app-center-widget-holder {
        padding-bottom: 82px
    }
}

@media only screen and (max-width:402px) {
    .app-center-widget-holder {
        padding-bottom: 125px
    }
}

.app-center-widget-holder.widgets-above-runway {
    bottom: 10px
}

.app-center-widget-holder.widgets-above-featurebox {
    margin-bottom: 113px
}

.id-content-container {
    background: #fff;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    width: 100%;
    z-index: 0;
    white-space: normal
}

.scene-footer-container {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 3
}

@media (min-width:643px) {
    .pane-open-mode .scene-footer-container {
        right: 408px
    }
    .xcUKcd.eZfyae .scene-footer-container {
        right: 72px
    }
    .xcUKcd.y2iKwd .scene-footer-container {
        right: 408px
    }
    .xcUKcd.eZfyae.y2iKwd .scene-footer-container {
        right: 480px
    }
}

.scene-footer {
    float: left;
    background-color: #f2f2f2;
    padding-right: 3px;
    display: table-row;
    font-size: 10px;
    color: #1f1f1f
}

.app-imagery-mode .scene-footer {
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff
}

.full-width-minimap .scene-footer {
    background-color: #f2f2f2;
    color: #1f1f1f
}

.hidden-Woal0c-jcJzye {
    display: none
}

.scene-footer a,
.scene-footer a:link,
.scene-footer a:visited {
    color: #1f1f1f
}

.app-imagery-mode .scene-footer a,
.app-imagery-mode .scene-footer a:link,
.app-imagery-mode .scene-footer a:visited {
    color: #ababab
}

.full-width-minimap .scene-footer a,
.full-width-minimap .scene-footer a:link,
.full-width-minimap .scene-footer a:visited {
    color: #1f1f1f
}

.scene-footer a:hover,
.scene-footer a:focus {
    color: #0b57d0
}

.app-imagery-mode .scene-footer a:hover,
.app-imagery-mode .scene-footer a:focus {
    color: #fff
}

.JLm1tf-bEDTcc-GWbSKc {
    pointer-events: auto
}

.app-planetary-mode .JLm1tf-bEDTcc-GWbSKc {
    display: none
}

@media only screen and (max-height:320px),
only screen and (max-width:160px) {
    .ZiieLd {
        display: none
    }
}

@media only screen and (max-width:568px) {
    .pane-open-mode .ZiieLd {
        display: none
    }
}

.full-width-minimap .scene-footer a:hover,
.full-width-minimap .scene-footer a:focus {
    color: #0b57d0
}

.id-scale,
.id-fineprint {
    display: table-cell
}

.hUbt4d-print {
    min-height: 66px
}

.id-omnibox {
    position: relative;
    right: 0;
    margin: 16px;
    top: 0;
    z-index: 15;
    -webkit-transition: right .5s;
    transition: right .5s;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition-property: visibility, opacity, right, -webkit-transform;
    transition-property: visibility, opacity, right, -webkit-transform;
    transition-property: transform, visibility, opacity, right;
    transition-property: transform, visibility, opacity, right, -webkit-transform;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
    -webkit-transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.id-omnibox-container .vasquette-margin-enabled.id-omnibox {
    margin-bottom: 12px
}

.id-omnibox-container {
    -webkit-transition: right .5s;
    transition: right .5s;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition-property: visibility, opacity, right, -webkit-transform;
    transition-property: visibility, opacity, right, -webkit-transform;
    transition-property: transform, visibility, opacity, right;
    transition-property: transform, visibility, opacity, right, -webkit-transform;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
    -webkit-transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.xcUKcd.eZfyae .id-omnibox-container,
.CMimtc .id-omnibox-container {
    right: 72px
}

@media only screen and (max-width:408px) {
    .id-omnibox {
        width: 100%
    }
}

.qK6Xvf:not(.cSgCkb) .id-omnibox-container {
    right: 0
}

.dC3P6 .id-omnibox-container {
    right: 0
}

.qK6Xvf.cSgCkb .id-omnibox-container {
    -webkit-transform: translateX(480px);
    transform: translateX(480px)
}

.id-rap-card {
    position: absolute;
    margin: 16px;
    z-index: 10
}

.vasquette-margin-enabled.id-rap-card,
.vasquette-margin-enabled.id-omnibox {
    margin: 12px 16px 12px 0
}

@media (max-width:536px) {
    .vasquette-margin-enabled.id-rap-card {
        margin-top: 60px
    }
    .vasquette-margin-enabled.hUbt4d-YvFRid-Pj3AId {
        margin-right: -16px
    }
}

.id-play {
    bottom: 100%;
    display: inline-block;
    right: calc(50% - 50px);
    margin-bottom: 48px;
    position: absolute;
    width: 100px
}

.id-pushdown {
    position: absolute;
    width: 100%;
    z-index: 13
}

.id-lightbox {
    display: inline-block;
    outline: none;
    position: absolute;
    z-index: 12
}

.id-omnibox-container {
    top: 0;
    position: absolute;
    z-index: 4
}

@media only screen and (max-width:408px) {
    .id-omnibox-container {
        width: 100%
    }
}

.id-scene {
    position: absolute;
    right: 0;
    width: 100%;
    bottom: 0;
    background-color: #f2f2f2;
    z-index: 0
}

.id-survey {
    display: none;
    z-index: 100;
    position: absolute;
    height: 238px;
    width: 440px;
    left: 9px;
    top: 59px
}

.id-consent-bump {
    background: #fff
}

[dir=ltr],
[dir=rtl] {
    unicode-bidi: -webkit-isolate;
    unicode-bidi: isolate
}

bdo[dir=ltr],
bdo[dir=rtl] {
    unicode-bidi: bidi-override;
    unicode-bidi: isolate-override
}

.top-center-stack {
    position: absolute;
    right: 0;
    left: 0
}

.pane-open-mode .top-center-stack {
    right: 408px
}

.xcUKcd.eZfyae .top-center-stack {
    right: 72px
}

.xcUKcd.y2iKwd .top-center-stack {
    right: 408px
}

.xcUKcd.eZfyae.y2iKwd .top-center-stack {
    right: 480px
}

.xcUKcd.y2iKwd.S38FBb .top-center-stack {
    right: 836px
}

.xcUKcd.y2iKwd.VElZUe .top-center-stack {
    right: 788px
}

.xcUKcd.eZfyae.y2iKwd.S38FBb .top-center-stack {
    right: 908px
}

.xcUKcd.eZfyae.y2iKwd.VElZUe .top-center-stack {
    right: 860px
}

.ma6Yeb-oXtfBe-m5SR9c-ZYyEqf {
    position: absolute;
    right: 50%;
    width: 100%
}

@media only screen and (max-width:800px) {
    .pane-open-mode .top-center-stack {
        display: none
    }
}

.moveable-pane {
    position: absolute;
    height: auto;
    z-index: 1;
    display: none
}

.moveable-pane .k7jAl,
.moveable-pane .e07Vkf,
.moveable-pane .hOJ2kd,
.moveable-pane .aIFcqe {
    position: relative
}

.pane-qAWA2-WAutxc .moveable-pane .k7jAl {
    -webkit-transform: none;
    transform: none
}

.cEevGf {
    position: absolute;
    z-index: 15;
    bottom: 0;
    right: 0;
    left: 0;
    height: 0;
    overflow: visible;
    -webkit-transition: right .2s 0s cubic-bezier(0, 0, .2, 1);
    transition: right .2s 0s cubic-bezier(0, 0, .2, 1);
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    align-items: flex-end
}

.ymw5uf {
    visibility: hidden;
    margin-bottom: -70px;
    -webkit-transition: visibility .15s, margin-bottom .15s;
    transition: visibility .15s, margin-bottom .15s;
    box-sizing: border-box;
    padding: 14px 24px;
    line-height: 20px;
    min-width: 288px;
    max-width: 568px;
    min-height: 48px;
    background-color: #000;
    color: #fff;
    border-radius: 3px 3px 0 0;
    font-size: 14px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    z-index: 1
}

.TEYSPe {
    visibility: visible;
    margin-bottom: 0
}

.EoqU6d {
    -webkit-box-flex: 1;
    -webkit-flex: auto;
    flex: auto
}

.BN49Ob {
    -webkit-flex-shrink: 1;
    flex-shrink: 1;
    margin-right: 48px;
    white-space: nowrap;
    color: #a8c7fa;
    cursor: pointer
}

.BN49Ob+.BN49Ob {
    margin-right: 18px
}

.BN49Ob:hover,
.BN49Ob:focus {
    text-decoration: none;
    outline-color: #a8c7fa
}

.gjMiCd {
    height: 18px;
    width: 18px
}

.PUtLdf .cEevGf {
    position: static;
    -webkit-transform: none;
    transform: none;
    height: auto
}

.PUtLdf .ymw5uf {
    position: static;
    display: block;
    width: auto;
    -webkit-transform: none;
    transform: none;
    background: transparent;
    color: black;
    padding: 10px 20px
}

.g9poTc picture {
    display: block
}

.g9poTc picture img {
    width: 100%;
    height: 100%
}

.Dn7b2c {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    overflow: hidden;
    z-index: 0
}

.mF4wq {
    background: url(//maps.gstatic.com/tactile/basepage/grid-image.png);
    z-index: 1
}

.kYinRe {
    -webkit-transition: opacity .2s;
    transition: opacity .2s
}

.id-inputtools {
    visibility: hidden;
    position: absolute;
    z-index: 1;
    right: 408px;
    top: 8px;
    -webkit-transition: all .2s linear;
    transition: all .2s linear
}

.id-inputtools.inputtools-active {
    visibility: visible
}

.id-inputtools.non-search-mode {
    margin-right: 8px;
    -webkit-transition-delay: 0ms;
    transition-delay: 0ms
}

.xcUKcd.y2iKwd .id-inputtools {
    margin-right: 16px;
    -webkit-transition-duration: .2s;
    transition-duration: .2s
}

.qK6Xvf.cSgCkb .id-inputtools {
    opacity: 0;
    right: 0;
    visibility: hidden
}

.id-inputtools .ita-kd-inputtools-div {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    min-width: 52px
}

.id-inputtools .ita-kd-icon-button {
    border: none;
    outline: none;
    box-shadow: none;
    -webkit-transition: none;
    transition: none;
    cursor: pointer;
    height: 48px;
    width: 39px
}

.id-inputtools .ita-kd-icon-button:focus {
    border: none !important;
    margin: 0
}

.id-inputtools .ita-kd-left.ita-kd-selected {
    background: linear-gradient(#d1d1d1, #f1f3f4, #fbfbfb)
}

.id-inputtools .ita-kd-icon-button .ita-kd-icon-span {
    opacity: .65;
    margin-top: 16px
}

.id-inputtools .ita-kd-icon-button.hover .ita-kd-icon-span,
.id-inputtools .ita-kd-icon-button-hover .ita-kd-icon-span,
.id-inputtools .ita-kd-right.ita-kd-selected .ita-kd-icon-span {
    opacity: 1
}

.id-inputtools .ita-kd-right,
.id-inputtools .ita-kd-right:focus {
    margin-left: 0;
    border-left: 1px solid #e3e3e3 !important;
    width: 24px
}

.ita-kd-dropdown-menu {
    margin-right: -1px !important
}

.ita-kd-menuitem {
    padding: 6px 8px 6px 38px !important
}

.PUtLdf .vk-box,
.PUtLdf .ita-hwt-ime {
    display: none !important
}

.BPikCc {
    display: none !important
}

.PUtLdf .BPikCc {
    display: block !important
}

@media print {
    body {
        print-color-adjust: exact
    }
}

.PUtLdf .Hk4XGb {
    display: none
}

body.PUtLdf {
    overflow: visible;
    width: auto;
    height: auto
}

.PUtLdf .id-app-container * {
    box-shadow: none
}

.PUtLdf a,
.PUtLdf a:link,
.PUtLdf a:visited,
.PUtLdf a:active {
    color: #1f1f1f
}

.PUtLdf .scene-footer {
    background-color: #fff;
    color: #1f1f1f;
    margin: 4px 0
}

.PUtLdf .scene-footer a,
.PUtLdf .scene-footer a:hover,
.PUtLdf .scene-footer a:link,
.PUtLdf .scene-footer a:visited {
    color: #1f1f1f
}

.PUtLdf .id-app-container {
    position: relative
}

.PUtLdf .id-content-container {
    position: static;
    width: auto
}

.PUtLdf .id-scene {
    position: relative
}

.PUtLdf .app-viewcard-strip {
    position: static
}

.PUtLdf .app-bottom-content-anchor {
    position: static;
    height: auto;
    margin-bottom: 0;
    line-height: 0
}

.PUtLdf .app-center-widget-holder {
    position: relative;
    bottom: auto;
    display: inline-block;
    line-height: normal
}

.PUtLdf .goog-menu,
.PUtLdf .goog-tooltip,
.PUtLdf .goog-popupdatepicker,
.PUtLdf .inproduct-guide-butterbar,
.PUtLdf .inproduct-guide-modal,
.PUtLdf .iph-dialog,
.PUtLdf #google-feedback-wizard {
    display: none
}

.MeBA1c {
    display: none
}

.MeBA1c .yONsp {
    position: absolute;
    width: 100vw;
    inset: 0;
    background-color: #f2f2f2;
    pointer-events: none
}

.cSBlpc {
    background: url(https://www.google.com/images/branding/googlelogo/1x/googlelogo_light_color_324x112dp.png) no-repeat center;
    background-size: 324px 112px;
    opacity: 1;
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    z-index: 4
}

.IIZecd .cSBlpc {
    background-image: url(https://www.google.com/images/branding/googlelogo/2x/googlelogo_light_color_324x112dp.png)
}

.o016Pd {
    position: absolute;
    height: 15px;
    right: 50%;
    top: 70%;
    width: 65px;
    -webkit-transform: translateX(50%);
    transform: translateX(50%)
}

.E1Wpof {
    background: url(//maps.gstatic.com/tactile/basepage/grid-image.png);
    opacity: 1;
    -webkit-transition: opacity .3s;
    transition: opacity .3s
}

.gWkKud .uj8Yqe {
    display: inline-block;
    height: 24px;
    width: 24px;
    margin-left: 14px;
    vertical-align: middle
}

.gWkKud .qUOwbe {
    vertical-align: middle
}

.kA9KIf,
.vRIAEd {
    outline-offset: -2px;
    overflow: hidden
}

@supports not (selector(::-webkit-scrollbar)) {
    .kA9KIf,
    .vRIAEd {
        scrollbar-width: thin
    }
}

.kA9KIf {
    overflow-y: auto
}

.vRIAEd {
    overflow-x: auto
}

@media (hover:hover) {
    .vRIAEd,
    .kA9KIf {
        overflow: hidden
    }
    .kA9KIf.dS8AEf,
    .kA9KIf:focus,
    .kA9KIf:hover,
    .C19oRb .kA9KIf {
        overflow-y: auto
    }
    .vRIAEd.dS8AEf,
    .vRIAEd:focus,
    .vRIAEd:hover,
    .C19oRb .vRIAEd {
        overflow-x: auto
    }
    .vRIAEd::-webkit-scrollbar,
    .kA9KIf::-webkit-scrollbar {
        height: 6px;
        width: 6px
    }
    .ZaEtDb.vRIAEd::-webkit-scrollbar,
    .ZaEtDb.kA9KIf::-webkit-scrollbar {
        height: 0;
        width: 0
    }
    .vRIAEd::-webkit-scrollbar-track,
    .kA9KIf::-webkit-scrollbar-track {
        background-color: #f2f2f2
    }
    .vRIAEd::-webkit-scrollbar-thumb,
    .kA9KIf::-webkit-scrollbar-thumb {
        background-color: #5e5e5e
    }
}

.OyjIsf {
    position: absolute;
    pointer-events: none;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0
}

.zemfqc {
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    top: -1px;
    right: -1px
}

.YkyvRc {
    display: none
}

.l72E8b {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    display: block;
    z-index: 100;
    padding: 2px 8px 3px;
    font-size: 13px;
    font-weight: 200;
    line-height: 18px;
    border: 1px solid #e3e3e3
}

.EI48Lc,
.La5UZd {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    border-radius: 4px;
    display: block;
    z-index: 100;
    padding: 2px 8px;
    white-space: nowrap;
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transition: opacity .15s cubic-bezier(0, 0, .2, 1), -webkit-transform .15s cubic-bezier(0, 0, .2, 1);
    transition: opacity .15s cubic-bezier(0, 0, .2, 1), -webkit-transform .15s cubic-bezier(0, 0, .2, 1);
    transition: opacity .15s cubic-bezier(0, 0, .2, 1), transform .15s cubic-bezier(0, 0, .2, 1);
    transition: opacity .15s cubic-bezier(0, 0, .2, 1), transform .15s cubic-bezier(0, 0, .2, 1), -webkit-transform .15s cubic-bezier(0, 0, .2, 1);
    font-family: "Google Sans", Roboto, Arial, sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.00625rem;
    line-height: 1.25rem
}

.EI48Lc {
    background-color: #000;
    color: #fff
}

.La5UZd {
    background-color: #303030;
    color: #fff
}

.La5UZd.FOZY8e,
.EI48Lc.FOZY8e {
    opacity: 0;
    -webkit-transform: scale(.8);
    transform: scale(.8);
    -webkit-transition: opacity 75ms cubic-bezier(.4, 0, 1, 1), visibility 0ms 75ms, -webkit-transform 0ms 75ms;
    transition: opacity 75ms cubic-bezier(.4, 0, 1, 1), visibility 0ms 75ms, -webkit-transform 0ms 75ms;
    transition: opacity 75ms cubic-bezier(.4, 0, 1, 1), transform 0ms 75ms, visibility 0ms 75ms;
    transition: opacity 75ms cubic-bezier(.4, 0, 1, 1), transform 0ms 75ms, visibility 0ms 75ms, -webkit-transform 0ms 75ms
}

.jHLihd {
    font-family: Roboto, Arial, sans-serif;
    white-space: nowrap;
    color: #1f1f1f;
    cursor: default;
    display: inline-block;
    font-size: 14px;
    line-height: 14px;
    vertical-align: baseline;
    font-weight: bold
}

.jHLihd::after {
    content: "·";
    margin: 0 5px
}

.G4Wk9d .jHLihd {
    color: #fff
}

.xoLGzf {
    position: relative;
    background: #fff;
    border-radius: 24px;
    box-sizing: border-box;
    width: 376px;
    height: 48px;
    border-bottom: 1px solid transparent;
    -webkit-transition-property: background, box-shadow;
    transition-property: background, box-shadow;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    border: 1px solid #e3e3e3;
    padding: 11px 64px 11px 106px;
    box-shadow: none
}

.xoLGzf.FkJ4Sc {
    padding-right: 24px
}

@media only screen and (max-width:536px) {
    .xoLGzf {
        width: calc(100vw - 128px)
    }
}

.NaMBUd {
    display: none
}

.NaMBUd.omnibox-active {
    display: block
}

.NaMBUd .nhb85d {
    z-index: 4
}

.JmVY9b.nhb85d {
    padding-left: 144px
}

.OAaR7b {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 -1px 0px rgba(0, 0, 0, 0.02);
    border: none
}

.xiQnY:-ms-input-placeholder {
    color: #8f8f8f
}

.NhWQq {
    position: relative
}

.zGB9df .NhWQq {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.zGB9df .xiQnY {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.uDEfZe {
    width: 0;
    float: right
}

.OHJuEb {
    margin-left: 4px;
    color: #8f8f8f
}

.NaMBUd .SZckEe.xiQnY,
.uDEfZe .z1Srse {
    color: #5e5e5e;
    display: inline-block;
    padding: 3px 0;
    white-space: nowrap;
    position: absolute;
    width: 100%;
    overflow: hidden;
    z-index: -1
}

.fvQUnc .xiQnY {
    outline: none
}

.xiQnY.nZtQUb:not(:focus) {
    color: #5e5e5e
}

.SZckEe.xiQnY {
    pointer-events: none;
    position: absolute;
    z-index: 1;
    right: 0
}

.A6Eb0 {
    border-radius: 16px 16px 0 0;
    border-bottom: 1px solid #e3e3e3;
    box-shadow: 0 0 2px rgb(0 0 0/20%), 0 -1px 0 rgb(0 0 0/2%);
    border: none
}

.hYBOP {
    display: block;
    cursor: pointer;
    padding: 12px 16px;
    color: #5e5e5e
}

.hYBOP:hover,
.hYBOP:focus {
    color: #0b57d0
}

.hYBOP.BJ5Jx {
    cursor: default;
    color: #8f8f8f
}

.hYBOP.BJ5Jx:hover,
.hYBOP.BJ5Jx:focus {
    color: #5e5e5e
}

.lSDxNd {
    display: block;
    position: absolute;
    left: 0;
    top: 0
}

.lSDxNd .yAuNSb {
    cursor: pointer;
    padding: 12px 15px 10px
}

.yAuNSb {
    color: #5e5e5e
}

.yAuNSb:hover,
.yAuNSb:active,
.yAuNSb:focus {
    color: #0b57d0
}

.hArJGc {
    color: #0b57d0;
    cursor: pointer;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    height: 48px;
    text-align: center;
    text-transform: uppercase;
    width: 54px
}

.pzfvzf {
    position: absolute;
    left: 54px;
    top: 0
}

.JmVY9b .pzfvzf {
    left: 94px
}

.pmPLle {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    position: absolute;
    left: 8px;
    top: 7px;
    width: 71px;
    height: 32px;
    border-radius: 24px;
    background: #1b6ef3
}

.pmPLle .SxXeRb {
    top: 35px
}

.pzycZ {
    color: #fff;
    width: 40%;
    float: right;
    text-align: center;
    cursor: default
}

.jYIGzd {
    width: 0;
    height: 17px;
    border-right: 1px solid #fff;
    opacity: .3;
    display: inline-block;
    float: right
}

.z6ADEf {
    width: 40%;
    height: 24px;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    cursor: pointer
}

.PFywrc {
    color: #fff
}

.rIWqBe {
    display: none;
    height: 100%;
    width: 100%
}

.hkxWWb {
    height: 20px;
    width: 20px
}

.tBgtgb {
    z-index: 5
}

.tBgtgb .k7jAl {
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
    box-sizing: border-box;
    top: unset
}

.tBgtgb.tBgtgb .e07Vkf {
    background: none;
    overflow: visible;
    top: unset
}

.tBgtgb .miFGmb {
    background: none;
    box-shadow: none
}

.DAdBuc {
    background-color: #fff;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
    font-size: 15px;
    width: 376px;
    padding: 8px 0;
    max-height: calc(100vh - 200px)
}

.DAdBuc.kA9KIf {
    overflow-y: auto
}

@media only screen and (max-width:536px) {
    .DAdBuc {
        width: calc(100vw - 128px)
    }
}

.PUtLdf .DAdBuc {
    display: none
}

.ZHeE1b {
    border: none;
    position: relative;
    color: #5e5e5e;
    font-size: 12px;
    min-height: 24px;
    padding-top: 6px;
    padding-bottom: 7px;
    cursor: pointer;
    direction: rtl;
    text-align: right;
    line-height: 32px
}

.n1lwoe {
    padding: 12px 20px 12px 12px
}

.WX899b {
    color: #5e5e5e;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    pointer-events: auto;
    float: left;
    z-index: 1
}

.Q3vuZd {
    color: #1f1f1f;
    visibility: hidden;
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    pointer-events: auto;
    z-index: 1
}

.ZHeE1b:hover .Q3vuZd,
.ZHeE1b:focus .Q3vuZd,
.xiGv9:hover .Q3vuZd,
.xiGv9:focus .Q3vuZd {
    visibility: visible
}

.xiGv9 {
    margin: 0 4px 0 -2px;
    min-width: 32px;
    height: 32px;
    position: relative;
    cursor: pointer
}

.xiGv9 .OyjIsf {
    border-radius: 50%
}

.ZHeE1b:hover .pG37Ce {
    color: #1f1f1f
}

.ZHeE1b:hover .D85HAd,
.D85HAd:focus {
    width: 32px
}

.D85HAd:hover .Q3vuZd,
.D85HAd:focus .Q3vuZd {
    visibility: visible
}

.D85HAd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    width: 0;
    height: 32px;
    position: relative;
    cursor: pointer
}

.D85HAd .OyjIsf {
    border-radius: 50%
}

.xiGv9:hover .OyjIsf {
    background-color: rgba(31, 31, 31, 0.08)
}

body:not(.LoJzbe) .xiGv9:focus .OyjIsf {
    background-color: rgba(31, 31, 31, 0.12)
}

body:not(.LoJzbe) .xiGv9:hover:focus .OyjIsf {
    background-color: rgba(31, 31, 31, 0.16)
}

.xiGv9:active .OyjIsf {
    background-color: rgba(31, 31, 31, 0.12)
}

.D85HAd:hover .OyjIsf {
    background-color: rgba(31, 31, 31, 0.08)
}

body:not(.LoJzbe) .D85HAd:focus .OyjIsf {
    background-color: rgba(31, 31, 31, 0.12)
}

body:not(.LoJzbe) .D85HAd:hover:focus .OyjIsf {
    background-color: rgba(31, 31, 31, 0.16)
}

.D85HAd:active .OyjIsf {
    background-color: rgba(31, 31, 31, 0.12)
}

.ZHeE1b:hover,
.sbox-highlight .ZHeE1b {
    background-color: rgba(31, 31, 31, 0.08);
    outline: none
}

@media screen and (forced-colors:active) {
    .ZHeE1b:hover,
    .sbox-highlight .ZHeE1b {
        border: 3px solid transparent
    }
}

.sbsb_c:last-child .ZHeE1b {
    border-radius: 0 0 2px 2px
}

.WYdB7b {
    padding-top: 7px
}

.l0wghb {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    padding-left: 13px
}

.wh2Rkb {
    padding-left: 0;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.WYdB7b .l0wghb {
    padding-left: 0
}

.DgCNMb {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.WYdB7b .DgCNMb {
    text-align: center
}

.DgCNMb span {
    unicode-bidi: normal
}

.ZHeE1b button.DgCNMb {
    cursor: pointer;
    text-align: right
}

.sbsb_c[dir=ltr] .DgCNMb {
    direction: ltr;
    text-align: left
}

.sbsb_c[dir=rtl] .DgCNMb {
    direction: rtl;
    text-align: right
}

.Y3gFfb {
    -webkit-flex-shrink: 1;
    flex-shrink: 1
}

.JvXd9b {
    outline: none
}

.u6z30d {
    max-height: 24px;
    width: 24px
}

.H2gSkb {
    height: 23px;
    width: 23px;
    max-height: revert
}

.sNfqIe {
    visibility: hidden
}

.jlzIOd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    float: right;
    height: 32px;
    width: 56px;
    padding-left: 8px
}

.fFXfOb .jlzIOd {
    height: 32px
}

.XPIgTd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    float: right;
    height: 40px;
    width: 40px;
    border-radius: 8px;
    overflow: clip;
    margin-left: 8px;
    position: relative
}

.QD0vz {
    position: absolute;
    inset: 0;
    background-image: linear-gradient(180deg, rgba(32, 33, 36, 0) 0%, rgba(32, 33, 36, 0) 20%, rgba(32, 33, 36, 0.1) 40%, rgba(32, 33, 36, 0.4) 60%, rgba(32, 33, 36, 0.6) 80%, rgba(32, 33, 36, 0.6) 100%)
}

.buvaue {
    height: 100%;
    width: 100%;
    object-fit: cover
}

.fLGRCd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    min-height: 40px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.Gb56oe,
.kzrPmf {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 288px;
    line-height: 20px
}

.Gb56oe {
    height: 20px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    color: #1f1f1f
}

.ZHeE1b:hover .Gb56oe,
.ZHeE1b:hover .kzrPmf {
    width: 248px
}

.pG37Ce {
    height: 20px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    color: #5e5e5e
}

.N3kk4c {
    height: 20px;
    display: inline-block;
    line-height: 20px
}

.NLqBMb {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    height: 18px;
    bottom: 2px;
    left: 2px;
    position: absolute
}

.Bnstjc {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    float: right;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: clip;
    position: relative
}

.JlacXc {
    border-radius: 8px
}

.upnfPb {
    background-color: #fff0ef
}

.EFEZfe {
    background-color: #f2f2f2
}

.WF58Ob {
    background-color: #d3e3fd
}

.cRo4hb {
    background-color: #f9dedc
}

.Knrkxf {
    background-color: #c4eed0
}

.wxXj6d {
    background-color: #ecf3fe
}

.H6zHn {
    background-color: #ffdf99
}

.RYaupb.fontBodyMedium {
    color: #1f1f1f;
    line-height: 32px
}

.cGyruf .XYuRPe {
    color: #1f1f1f;
    font-weight: 500
}

.fFXfOb .cGyruf {
    line-height: 32px
}

.EmLCAe {
    color: #5e5e5e
}

.T5EJvf.cGyruf {
    color: #0b57d0
}

.WYdB7b .cGyruf {
    border: none
}

.WYdB7b .cGyruf:hover,
.WYdB7b .cGyruf:focus {
    background-color: unset
}

.Rx86ib.fontTitleSmall {
    margin: 0 -5px;
    line-height: 24px
}

.QPrLUd.fontTitleSmall {
    margin: 0 -1px;
    line-height: 24px
}

.tJ5Rbb {
    top: 42px;
    left: 0
}

.mnE5xd {
    top: 5px;
    left: 45px
}

.SxXeRb {
    display: none;
    pointer-events: none;
    position: absolute;
    z-index: 1003;
    white-space: nowrap
}

.RR87W+.SxXeRb {
    display: block
}

.nhb85d .searchboxinput,
.nhb85d .tactile-searchbox-input {
    color: #1f1f1f;
    font-size: 15px;
    padding: 0 !important;
    height: 24px !important;
    line-height: 24px;
    vertical-align: top;
    -webkit-transition-property: color;
    transition-property: color;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    width: 100%
}

.fKm1Mb {
    position: absolute;
    z-index: 1003;
    right: 0;
    top: 0
}

.mL3xi {
    display: block;
    padding: 12px;
    color: #5e5e5e
}

.r6EGDb .mL3xi,
.xoLGzf:not(.searchbox-empty) .mL3xi {
    cursor: pointer
}

.r6EGDb .mL3xi,
.xoLGzf:not(.searchbox-empty) .mL3xi:hover,
.xoLGzf:not(.searchbox-empty) .mL3xi:focus {
    color: #0b57d0
}

.nhb85d .sbib_b {
    position: relative
}

.nhb85d .sbib_b::after {
    content: "";
    pointer-events: none;
    position: absolute;
    z-index: 6;
    top: 0;
    width: 20px;
    height: 24px;
    background: linear-gradient(90deg, transparent, #fff 20px);
    opacity: 1;
    visibility: visible;
    -webkit-transition-property: opacity, visibility;
    transition-property: opacity, visibility;
    -webkit-transition-duration: .3s;
    transition-duration: .3s
}

.nhb85d .sbib_b[dir=ltr]::after {
    right: 0
}

.nhb85d .sbib_b[dir=rtl]::after {
    left: 0;
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1)
}

.nhb85d.sbox-focus .sbib_b::after {
    opacity: 0;
    visibility: hidden
}

.XltNde {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
    -webkit-transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    z-index: 3
}

.PUtLdf .XltNde,
.PUtLdf .w6VYqd,
.XltNde .k7jAl {
    position: static
}

.PLbyfe .e07Vkf {
    margin-top: 72px;
    height: calc(100% - 72px)
}

@media (max-width:536px) {
    .PLbyfe .e07Vkf {
        margin-top: 50px;
        height: calc(100% - 50px)
    }
}

.qK6Xvf.XltNde {
    -webkit-transform: translateX(100%);
    transform: translateX(100%)
}

.QR0DPe .w6VYqd {
    display: none
}

.gYkzb {
    position: absolute;
    z-index: 0;
    top: calc(50% - 24px);
    display: none
}

.w6VYqd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1
}

.tTVLSc.XltNde {
    opacity: 1;
    height: 100%
}

.tTVLSc .gYkzb {
    display: block
}

.yra0jd {
    background: #fff;
    border: 0;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    width: 23px;
    height: 48px;
    cursor: pointer;
    border-left: 1px solid #e3e3e3;
    border-radius: 8px 0 0 8px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.qK6Xvf .yra0jd {
    background-color: #fff;
    border-radius: 0 8px 8px 0
}

html[dir=rtl] .yra0jd {
    -webkit-transform: none;
    transform: none
}

html[dir=rtl] .qK6Xvf .yra0jd,
.qK6Xvf .yra0jd {
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1)
}

.PySCB {
    position: absolute;
    right: 100%;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    margin-right: 8px;
    line-height: 22px;
    white-space: nowrap
}

.PySCB::before {
    content: "";
    position: absolute;
    top: 50%;
    right: -5px;
    margin-top: -6px;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid #1f1f1f
}

.XltNde.dC3P6,
.bJzME {
    display: none
}

.tTVLSc.bJzME {
    display: block;
    position: relative
}

.Hu9e2e {
    position: relative;
    top: 60px;
    height: calc(100% - 80px);
    margin-right: 20px
}

.Hu9e2e .k7jAl,
.Hu9e2e .e07Vkf {
    border-radius: 16px
}

.JgEiLc {
    position: relative;
    width: 72px;
    height: 100vh;
    z-index: 10
}

.srXS8c {
    width: 408px;
    position: absolute;
    top: 0;
    opacity: 1;
    display: block;
    height: 100%;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
    -webkit-transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

.MbVBgb {
    background-color: #1b6ef3;
    height: 85px
}

.lxQSp {
    background-color: #f2f2f2;
    height: 85px
}

.LTjAJb {
    background-color: transparent;
    height: 85px
}

.aNFcie {
    background: url(//maps.gstatic.com/tactile/basepage/loader_beige_1x.gif) center/40px 8px no-repeat;
    height: 22px;
    margin: 24px 0
}

.MnW8Vb {
    opacity: 0
}

.Z0Pmqc {
    opacity: 1;
    -webkit-transition: opacity .5s ease-in;
    transition: opacity .5s ease-in
}

sentinel {}