#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🗺️ خادم خرائط اليمن المحلي الكامل
Complete Local Yemen Maps Server
"""

from flask import Flask, jsonify, send_from_directory, request, send_file, abort
from flask_cors import CORS
import psycopg2
import psycopg2.extras
import os
import logging
import json
import time
from datetime import datetime

# إعداد التطبيق
app = Flask(__name__,
           static_folder='public',
           template_folder='templates')
CORS(app)

# إعداد قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'yemen_gps',
    'user': 'yemen',
    'password': 'admin'
}

# إعدادات البلاطات المحلية
TILES_DIR = "tiles/yemen_real"
CACHE_DIR = "tiles/yemen_cache"
TEMPLATES_DIR = "templates"

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إحصائيات الخادم
server_stats = {
    'start_time': datetime.now(),
    'requests_count': 0,
    'database_queries': 0,
    'tiles_served': 0
}

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def log_request():
    """تسجيل الطلبات"""
    server_stats['requests_count'] += 1

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    log_request()
    return send_from_directory(TEMPLATES_DIR, 'index.html')

@app.route('/admin')
def admin():
    """لوحة التحكم"""
    log_request()
    return send_from_directory(TEMPLATES_DIR, 'admin.html')

@app.route('/test')
def test_map():
    """صفحة اختبار الخريطة"""
    log_request()
    return send_from_directory(TEMPLATES_DIR, 'test_map.html')

@app.route('/local-map.html')
def local_map():
    """الخريطة المحلية الكاملة"""
    log_request()
    return send_from_directory(TEMPLATES_DIR, 'local-map.html')

# خدمة البلاطات المحلية
@app.route('/tiles/yemen_real/<int:z>/<int:x>/<int:y>.png')
def serve_local_tiles(z, x, y):
    """تقديم البلاطات المحلية"""
    log_request()
    server_stats['tiles_served'] += 1

    # البحث عن البلاطة في المجلدات المختلفة
    tile_paths = [
        f"{TILES_DIR}/{z}/{x}/{y}.png",
        f"{CACHE_DIR}/{z}/{x}/{y}.png",
        f"tiles/yemen/{z}/{x}/{y}.png",
        f"templates/yemen/{z}/{x}/{y}.png"
    ]

    for tile_path in tile_paths:
        if os.path.exists(tile_path):
            return send_file(tile_path, mimetype='image/png')

    # إذا لم توجد البلاطة، إرجاع بلاطة فارغة
    return '', 404

# خدمة الملفات الثابتة
@app.route('/templates/<path:filename>')
def serve_templates(filename):
    """تقديم ملفات القوالب"""
    log_request()
    return send_from_directory('templates', filename)

@app.route('/js/<path:filename>')
def serve_js(filename):
    """تقديم ملفات JavaScript"""
    log_request()
    return send_from_directory('js', filename)

@app.route('/public/<path:filename>')
def serve_public(filename):
    """تقديم الملفات العامة"""
    log_request()
    return send_from_directory('public', filename)

@app.route('/static/<path:filename>')
def serve_static(filename):
    """تقديم الملفات الثابتة"""
    log_request()
    return send_from_directory('static', filename)

# API المواقع والأماكن
@app.route('/api/places')
def get_places():
    """جلب جميع الأماكن"""
    log_request()
    server_stats['database_queries'] += 1

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'فشل الاتصال بقاعدة البيانات'}), 500

        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # جلب الأماكن مع معلومات إضافية
        query = """
        SELECT
            id, name, name_ar, name_en,
            latitude, longitude,
            category, subcategory,
            address, phone, website,
            rating, review_count,
            created_at, updated_at
        FROM places
        WHERE latitude IS NOT NULL AND longitude IS NOT NULL
        ORDER BY name_ar
        LIMIT 1000
        """

        cur.execute(query)
        places = cur.fetchall()

        conn.close()

        # تحويل النتائج إلى قائمة
        places_list = []
        for place in places:
            places_list.append({
                'id': place['id'],
                'name': place['name_ar'] or place['name'] or place['name_en'],
                'name_ar': place['name_ar'],
                'name_en': place['name_en'],
                'lat': float(place['latitude']) if place['latitude'] else None,
                'lng': float(place['longitude']) if place['longitude'] else None,
                'category': place['category'],
                'subcategory': place['subcategory'],
                'address': place['address'],
                'phone': place['phone'],
                'website': place['website'],
                'rating': float(place['rating']) if place['rating'] else None,
                'review_count': place['review_count'] or 0
            })

        return jsonify({
            'success': True,
            'count': len(places_list),
            'places': places_list
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الأماكن: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/places/search')
def search_places():
    """البحث في الأماكن"""
    log_request()
    server_stats['database_queries'] += 1

    query = request.args.get('q', '').strip()
    if not query:
        return jsonify({'error': 'يرجى إدخال كلمة البحث'}), 400

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'فشل الاتصال بقاعدة البيانات'}), 500

        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # البحث في الأماكن
        search_query = """
        SELECT
            id, name, name_ar, name_en,
            latitude, longitude,
            category, subcategory,
            address, phone,
            rating, review_count
        FROM places
        WHERE (
            name_ar ILIKE %s OR
            name ILIKE %s OR
            name_en ILIKE %s OR
            address ILIKE %s OR
            category ILIKE %s
        )
        AND latitude IS NOT NULL AND longitude IS NOT NULL
        ORDER BY
            CASE
                WHEN name_ar ILIKE %s THEN 1
                WHEN name ILIKE %s THEN 2
                ELSE 3
            END,
            name_ar
        LIMIT 50
        """

        search_term = f'%{query}%'
        exact_term = f'{query}%'

        cur.execute(search_query, [
            search_term, search_term, search_term, search_term, search_term,
            exact_term, exact_term
        ])

        results = cur.fetchall()
        conn.close()

        # تحويل النتائج
        places_list = []
        for place in results:
            places_list.append({
                'id': place['id'],
                'name': place['name_ar'] or place['name'] or place['name_en'],
                'name_ar': place['name_ar'],
                'name_en': place['name_en'],
                'lat': float(place['latitude']) if place['latitude'] else None,
                'lng': float(place['longitude']) if place['longitude'] else None,
                'category': place['category'],
                'subcategory': place['subcategory'],
                'address': place['address'],
                'phone': place['phone'],
                'rating': float(place['rating']) if place['rating'] else None,
                'review_count': place['review_count'] or 0
            })

        return jsonify({
            'success': True,
            'query': query,
            'count': len(places_list),
            'results': places_list
        })

    except Exception as e:
        logger.error(f"خطأ في البحث: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/places/categories')
def get_categories():
    """جلب الفئات المتاحة"""
    log_request()
    server_stats['database_queries'] += 1

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'فشل الاتصال بقاعدة البيانات'}), 500

        cur = conn.cursor()

        # جلب الفئات مع عدد الأماكن
        query = """
        SELECT
            category,
            COUNT(*) as count
        FROM places
        WHERE category IS NOT NULL
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        GROUP BY category
        ORDER BY count DESC, category
        """

        cur.execute(query)
        categories = cur.fetchall()
        conn.close()

        categories_list = []
        for cat in categories:
            categories_list.append({
                'name': cat[0],
                'count': cat[1]
            })

        return jsonify({
            'success': True,
            'categories': categories_list
        })

    except Exception as e:
        logger.error(f"خطأ في جلب الفئات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/places/by-category/<category>')
def get_places_by_category(category):
    """جلب الأماكن حسب الفئة"""
    log_request()
    server_stats['database_queries'] += 1

    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'error': 'فشل الاتصال بقاعدة البيانات'}), 500

        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        query = """
        SELECT
            id, name, name_ar, name_en,
            latitude, longitude,
            category, subcategory,
            address, phone,
            rating, review_count
        FROM places
        WHERE category = %s
        AND latitude IS NOT NULL AND longitude IS NOT NULL
        ORDER BY name_ar
        LIMIT 200
        """

        cur.execute(query, [category])
        places = cur.fetchall()
        conn.close()

        places_list = []
        for place in places:
            places_list.append({
                'id': place['id'],
                'name': place['name_ar'] or place['name'] or place['name_en'],
                'lat': float(place['latitude']),
                'lng': float(place['longitude']),
                'category': place['category'],
                'subcategory': place['subcategory'],
                'address': place['address'],
                'phone': place['phone'],
                'rating': float(place['rating']) if place['rating'] else None,
                'review_count': place['review_count'] or 0
            })

        return jsonify({
            'success': True,
            'category': category,
            'count': len(places_list),
            'places': places_list
        })

    except Exception as e:
        logger.error(f"خطأ في جلب أماكن الفئة: {e}")
        return jsonify({'error': str(e)}), 500

# API حالة الخادم
@app.route('/api/status')
def server_status():
    """حالة الخادم"""
    log_request()

    try:
        # فحص قاعدة البيانات
        conn = get_db_connection()
        db_status = "متصل" if conn else "غير متصل"

        if conn:
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM places")
            places_count = cur.fetchone()[0]
            conn.close()
        else:
            places_count = 0

        uptime = datetime.now() - server_stats['start_time']

        return jsonify({
            'server': 'Yemen Maps Local Server',
            'status': 'running',
            'database': db_status,
            'places_count': places_count,
            'uptime_seconds': int(uptime.total_seconds()),
            'stats': server_stats,
            'local_mode': True,
            'offline_capable': True
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🗺️ بدء خادم خرائط اليمن المحلي الكامل")
    print("=" * 50)
    print("🏠 الخادم المحلي: http://localhost:5000")
    print("📊 لوحة التحكم: http://localhost:5000/admin")
    print("🔍 API الأماكن: http://localhost:5000/api/places")
    print("📱 البحث: http://localhost:5000/api/places/search?q=صنعاء")
    print("📂 الفئات: http://localhost:5000/api/places/categories")
    print("=" * 50)

    app.run(host='0.0.0.0', port=5000, debug=True)
