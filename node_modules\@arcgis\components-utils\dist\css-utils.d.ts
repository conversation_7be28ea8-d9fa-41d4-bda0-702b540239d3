import { Nil } from './types';
/**
 * This code contains imperative syntax (like for loops and mutation) as it is
 * in the hot path - performance optimizations are critical here.
 * See https://devtopia.esri.com/WebGIS/arcgis-js-api/commit/2565cedd87b
 *
 * Stencil has native support for passing-in class prop as an object or string,
 * but it does not support arrays or booleans, thus this utility is used
 * instead.
 *
 * @remarks
 * This function is not necessary in a Lit package as Lit's `classMap` directive
 * accepts an object
 */
export declare const classes: (...classes: (Nil | Record<string, boolean> | string[] | string | false)[]) => string;
