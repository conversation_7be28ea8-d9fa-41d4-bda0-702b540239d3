/**
 * A deferred promise.
 * Useful for when you want to return a promise but don't have the value yet.
 * Example:
 * ```
 * const deferred = new Deferred<string>();
 * setTimeout(() => deferred.resolve("Hello World"), 1000);
 * return deferred.promise;
 * ```
 * @template T The type of the promise.
 */
export declare class Deferred<T> {
    /**
     * The promise that can be awaited.
     */
    promise: Promise<T>;
    /**
     * Creates a new deferred promise.
     */
    constructor();
}
export interface Deferred<T> {
    /**
     * Resolves the promise.
     * @param value The value to resolve the promise with.
     *
     * @privateRemarks
     * Defined as a method to disable covariance checks. Overridden in constructor.
     */
    resolve(_value: PromiseLike<T> | T): void;
    /**
     * Rejects the promise.
     */
    reject(_error: unknown): void;
}
