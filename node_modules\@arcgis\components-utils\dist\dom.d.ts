/**
 * Observe the element and its ancestors for attribute mutations.
 * If the attributes have been changed in the ancestor tree then the callback will be invoked.
 * Example: `observeAncestorsMutation(element, ["dir", "lang"], () => console.log("dir or lang changed"));`
 * @param element The element on which to observe the attribute mutations.
 * @param attributeFilter The list of attributes to observe.
 * @param callback The callback to invoke when the attributes have been changed.
 * @returns The mutation observer
 */
export declare const observeAncestorsMutation: (element: Node, attributeFilter: string[], callback: () => void) => (() => void);
/**
 * Find the closest element that matches the selector.
 * It will traverse the element's ancestors to find the target element.
 * Shadow DOM boundaries are also taken into account.
 * @param base The element to start the search from.
 * @param selector The selector to match.
 * @returns The closest element that matches the selector or null if not found.
 */
export declare const closestElement: (base: Element, selector: string) => Element | null;
/**
 * Use the Calcite mode to determine the theme of the element.
 * It will traverse the element's ancestors to find the theme.
 * Shadow DOM boundaries are also taken into account.
 * @param base The element to start the search from.
 * @returns The theme of the element, either "light" or "dark", "light" is the default.
 */
export declare const getElementTheme: (base: Element) => "dark" | "light";
/**
 * Get direction property of closest element.
 * @param el The element to start the search from.
 * @returns The direction of the element, either "ltr" | "rtl", "ltr" is the default.
 */
export declare const getElementDir: (el: HTMLElement) => "ltr" | "rtl";
/**
 * Get the attribute value from the element.
 * It will traverse the element's ancestors to find the attribute.
 * Shadow DOM boundaries are also taken into account.
 * If the attribute is not found then the fallback value is returned.
 * Example: `getElementAttribute(element, "dir", "ltr");`
 * @param base The element to start the search from.
 * @param prop The attribute name.
 * @param fallbackValue The fallback value if the attribute is not found.
 * @returns The attribute value or the fallback value if the attribute is not found.
 */
export declare const getElementAttribute: (el: Element, prop: string, fallbackValue: string) => string;
export interface FocusableElement extends HTMLElement {
    setFocus?: () => Promise<void>;
}
export declare const focusElement: (el: FocusableElement | undefined) => Promise<void>;
/**
 * Set the focus on the element that matches the selector.
 * It will traverse the element's ancestors to find the target element.
 * Shadow DOM boundaries are also taken into account.
 * If the element is not found then the focus is not set.
 * Example: `setFocusOnElement(element, "[role='menuitem']");`
 * @param ref The element to start the search from.
 * @param selector The selector to match.
 * @returns Returns true if the focus is set on the element.
 *
 * REFACTOR: this is doing too much. break it into separate find element and focus
 * element utilities
 */
export declare const setFocusOnElement: (ref: (Element & {
    componentOnReady?: () => Promise<void>;
}) | null | undefined, selector: string) => void;
