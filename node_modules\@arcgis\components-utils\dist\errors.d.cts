/**
 * Check whether the code is executing in an Esri internal environment (for
 * example, Lumina dev server). When true, your code can enable extra validation
 * to detect incorrect usages or do runtime bug detection.
 *
 * The call to isEsriInternalEnv() MUST always appear behind one of the
 * following guards to ensure it is correctly eliminated in production bundles:
 *
 * - `process.env.NODE_ENV !== "production"`
 * - `process.env.NODE_ENV === "development"`
 * - `process.env.NODE_ENV === "test"`
 *
 * @remarks
 * This function is primary for usage in support packages. In Lumina component
 * packages, simpler alternatives are provided:
 * https://qawebgis.esri.com/components/lumina/publishing#bundling-code-conditionally
 */
export declare const isEsriInternalEnv: () => boolean;
/**
 * Calls a sync method and catch any errors. Returns undefined if error occurred.
 *
 * Can also provide a thisContext and rest arguments
 */
export declare const safeCall: <Callback extends (...args: never[]) => unknown>(callback?: Callback, thisContext?: ThisParameterType<Callback>, ...rest: Parameters<Callback>) => ReturnType<Callback> | void;
/**
 * Calls an async method and catch any errors. Returns undefined if error occurred.
 *
 * Can also provide a thisContext and rest arguments
 */
export declare const safeAsyncCall: <Callback extends (...args: never[]) => unknown>(callback?: Callback, thisContext?: ThisParameterType<Callback>, ...rest: Parameters<Callback>) => Promise<Awaited<ReturnType<Callback>> | void>;
