"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const mappedFind = (array, callback) => {
  for (let i = 0; i < array.length; i++) {
    const value = callback(array[i], i);
    if (value != null) {
      return value ?? void 0;
    }
  }
  return;
};
const classes = (...classes2) => {
  const effectiveClasses = [];
  for (let i = 0; i < classes2.length; i++) {
    const arg = classes2[i];
    if (typeof arg === "string") {
      effectiveClasses.push(arg);
    } else if (Array.isArray(arg)) {
      effectiveClasses.push.apply(effectiveClasses, arg);
    } else if (typeof arg === "object") {
      for (const prop in arg) {
        if (arg[prop]) {
          effectiveClasses.push(prop);
        }
      }
    }
  }
  const className = effectiveClasses.join(" ");
  effectiveClasses.length = 0;
  return className;
};
class Deferred {
  /**
   * Creates a new deferred promise.
   */
  constructor() {
    this.promise = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }
}
const inTargetElement = (element, targetElement) => {
  let currentElement = element;
  while (currentElement) {
    if (currentElement === targetElement) {
      return true;
    }
    if (!currentElement.parentNode) {
      return false;
    }
    if (currentElement.parentNode instanceof ShadowRoot) {
      currentElement = currentElement.parentNode.host;
    } else {
      currentElement = currentElement.parentNode;
    }
  }
  return false;
};
const observeAncestorsMutation = (element, attributeFilter, callback) => {
  const subscribe = observe(attributeFilter).subscribe;
  return subscribe((mutations) => {
    const matched = mutations.some((mutation) => inTargetElement(element, mutation.target));
    if (matched) {
      callback();
    }
  });
};
const observers = {};
const observe = (attributeFilter) => {
  const attributes = attributeFilter.join(",");
  const previousObserver = observers[attributes];
  if (previousObserver !== void 0) {
    return previousObserver;
  }
  const subscribers = /* @__PURE__ */ new Set();
  const mutationObserver = new MutationObserver((mutations) => subscribers.forEach((callback) => callback(mutations)));
  if (globalThis.document) {
    mutationObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter,
      subtree: true
    });
  }
  const observer = {
    subscribe: (callback) => {
      subscribers.add(callback);
      return () => {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          mutationObserver.disconnect();
          observers[attributes] = void 0;
        }
      };
    }
  };
  observers[attributes] = observer;
  return observer;
};
const closestElement = (base, selector) => {
  let currentElement = base;
  while (currentElement) {
    const element = currentElement.closest?.(selector);
    if (element) {
      return element;
    }
    const rootElement = currentElement.getRootNode?.();
    if (rootElement === globalThis.document) {
      return null;
    }
    currentElement = rootElement.host;
  }
  return null;
};
const getElementTheme = (base) => {
  const themeElement = closestElement(base, ":is(.calcite-mode-light, .calcite-mode-dark)");
  return themeElement?.classList.contains("calcite-mode-dark") ? "dark" : "light";
};
const getElementDir = (el) => getElementAttribute(el, "dir", "ltr");
const getElementAttribute = (el, prop, fallbackValue) => {
  const closest = closestElement(el, `[${prop}]`);
  return closest?.getAttribute(prop) ?? fallbackValue;
};
const isElement = (ref) => ref.nodeType === Node.ELEMENT_NODE;
const hasSetFocus = (ref) => typeof ref.setFocus === "function";
const setFocus = (ref, selector = "") => {
  if (!isElement(ref)) {
    return false;
  }
  if (ref.matches(selector)) {
    if (hasSetFocus(ref)) {
      setTimeout(() => void ref.setFocus(), 0);
    }
    return true;
  }
  for (const child of ref.children) {
    if (setFocus(child, selector)) {
      return true;
    }
  }
  const shadowRoot = ref.shadowRoot;
  if (shadowRoot) {
    for (const child of shadowRoot.children) {
      if (setFocus(child, selector)) {
        return true;
      }
    }
  }
  return false;
};
const focusElement = async (el) => {
  if (el == null) {
    return;
  }
  if (hasSetFocus(el)) {
    await el.setFocus();
  } else {
    el.focus();
  }
};
const setFocusOnElement = (ref, selector) => {
  if (!ref?.shadowRoot) {
    return;
  }
  if (ref.hasAttribute("hydrated") || ref.hasAttribute("calcite-hydrated")) {
    setFocus(ref, selector);
    return;
  }
  void Promise.resolve(ref.componentOnReady?.()).then(() => setFocus(ref, selector));
};
const isEsriInternalEnv = () => (
  /*
   * `globalThis.` is important here. Some bundlers remove the `typeof process`
   * checks, but don't remove the usages of undefined variables - this can cause
   * runtime error. By adding `globalThis.`, we avoid having `typeof process`
   * check removed by the bundler.
   * This does meant tree-shaking won't happen for the isEsriInternalEnv()
   * check, but this is ok since this check is meant to always be behind the
   * development/test guard.
   * See https://devtopia.esri.com/WebGIS/arcgis-web-components/pull/2087#issuecomment-5152454
   */
  typeof globalThis.process === "object" && !!process.env.ESRI_INTERNAL
);
const safeCall = (callback, thisContext, ...rest) => {
  try {
    return callback?.call(thisContext, ...rest);
  } catch (error) {
    console.error(error, callback);
  }
  return void 0;
};
const safeAsyncCall = async (callback, thisContext, ...rest) => {
  try {
    const result = callback?.call(thisContext, ...rest);
    return await result;
  } catch (error) {
    console.error(error, callback);
  }
  return void 0;
};
const gen = (count) => {
  let out = "";
  for (let i = 0; i < count; i++) {
    out += ((1 + Math.random()) * 65536 | 0).toString(16).substring(1);
  }
  return out;
};
const generateGuid = () => [gen(2), gen(1), gen(1), gen(1), gen(3)].join("-");
const supportedLocalesArray = "ar,bg,bs,ca,cs,da,de,el,en,es,et,fi,fr,he,hr,hu,id,it,ja,ko,lt,lv,nl,nb,no,pl,pt-BR,pt-PT,ro,ru,sk,sl,sr,sv,th,tr,uk,vi,zh-CN,zh-HK,zh-TW".split(
  ","
);
const supportedLocales = /* @__PURE__ */ new Set(supportedLocalesArray);
const defaultLocale = "en";
const localeEquivalencies = {
  // We use `pt-PT` as it will have the same translations as `pt`, which has no corresponding bundle
  pt: "pt-PT",
  // We support both 'nb' and 'no' (BCP 47) for Norwegian but only `no` has corresponding bundle
  nb: "no",
  // We support both 'nn' and 'no' (BCP 47) for Norwegian but only `no` has corresponding bundle
  // See https://devtopia.esri.com/WebGIS/arcgis-web-components/issues/4667
  nn: "no",
  // We use `zh-CN` as base translation for chinese locales which has no corresponding bundle.
  zh: "zh-CN"
};
const fetchT9nStringsBundle = async (locale, assetsPath, prefix = "") => {
  const path = `${assetsPath}/${prefix}`;
  const filePath = `${path}${locale}.json`;
  t9nStringsCache[filePath] ?? (t9nStringsCache[filePath] = fetchBundle(locale, path));
  return await t9nStringsCache[filePath];
};
const t9nStringsCache = {};
const fetchBundle = async (locale, path) => {
  const filePath = `${path}${locale}.json`;
  try {
    const response = await fetch(filePath);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    if (process.env.NODE_ENV !== "production") {
      const is404ViteFallback = String(error).includes(`Unexpected token '<', "<!doctype "... is not valid JSON`);
      if (is404ViteFallback) {
        console.error(`[404] Localization strings not found at ${filePath}`);
      } else {
        console.error(`Error fetching localization strings at ${filePath}`, error);
      }
    } else {
      console.error(error);
    }
    return {};
  }
  if (locale === defaultLocale) {
    return {};
  }
  return await fetchBundle(defaultLocale, path);
};
const getElementLocales = (element) => {
  const lang = getElementAttribute(element, "lang", globalThis.navigator?.language || defaultLocale);
  return { lang, t9nLocale: normalizeLocale(lang) };
};
const normalizeLocale = (locale) => {
  const [rawLanguageCode, regionCode] = locale.split("-");
  const languageCode = rawLanguageCode.toLowerCase();
  let normalizedLocale = languageCode;
  if (regionCode) {
    normalizedLocale = `${languageCode}-${regionCode.toUpperCase()}`;
  }
  normalizedLocale = localeEquivalencies[normalizedLocale] ?? normalizedLocale;
  if (supportedLocales.has(normalizedLocale)) {
    return normalizedLocale;
  }
  if (regionCode) {
    return normalizeLocale(languageCode);
  }
  return defaultLocale;
};
const startLocaleObserver = (element, getAssetsPath, onUpdated, assetName) => {
  let result = void 0;
  const callback = () => updateComponentLocaleState(element, getAssetsPath(), assetName).then((newResult) => {
    if (result?.lang !== newResult.lang || result.t9nLocale !== newResult.t9nLocale || result.t9nStrings !== newResult.t9nStrings) {
      onUpdated(newResult);
    }
    result = newResult;
  }).catch(console.error);
  queueMicrotask(callback);
  return observeAncestorsMutation(element, ["lang"], callback);
};
const updateComponentLocaleState = async (element, assetsPath, assetName = element.localName.split("-").slice(1).join("-")) => {
  const { lang, t9nLocale } = getElementLocales(element);
  const t9nAssetsPath = `${assetsPath}/${assetName}/t9n`;
  const prefix = `messages.`;
  const t9nStrings = (
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    assetName === null ? {} : await fetchT9nStringsBundle(t9nLocale, t9nAssetsPath, prefix)
  );
  return { lang, t9nLocale, t9nStrings };
};
const blurb = "All material copyright Esri, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/{minorVersion}/esri/copyright.txt for details.\nv{version}";
const extractMinorVersion = (version) => {
  const [major, minor] = version.split(".");
  return `${major}.${minor}`;
};
const getPreamble = (version) => blurb.replace("{minorVersion}", extractMinorVersion(version)).replace("{version}", version);
const doubleQuote = '"';
const singleQuote = "'";
const repeatString = (value, n) => new Array(n + 1).join(value);
const quoteString = (value) => {
  let quote = doubleQuote;
  let alternateQuote = singleQuote;
  const avoidEscape = value.includes(quote) && !value.includes(alternateQuote);
  if (avoidEscape) {
    alternateQuote = doubleQuote;
    quote = singleQuote;
  }
  const alternateEscape = new RegExp(`(^|[^\\\\])((?:\\\\{2})*)((?:\\\\${alternateQuote})+)`, "gu");
  value = value.replace(
    alternateEscape,
    (_, boundaryChar, leadingEscapedSlashes, escapedQuoteChars) => (
      // We divide the escapedQuoteChars by 2 since there are 2 characters in each escaped part ('\\"'.length === 2)
      boundaryChar + leadingEscapedSlashes + repeatString(alternateQuote, escapedQuoteChars.length / 2)
    )
  );
  const quoteEscape = new RegExp(`(^|[^\\\\])((?:\\\\{2})*)(${quote}+)`, "gu");
  value = value.replace(
    quoteEscape,
    (_, boundaryChar, leadingEscapedSlashes, quoteChars) => boundaryChar + leadingEscapedSlashes + repeatString(`\\${quote}`, quoteChars.length)
  );
  return quote + value + quote;
};
const createFilterExpression = (filterWord) => {
  const sanitizedWord = filterWord ? filterWord.replaceAll(/[-[\]/{}()*+?.\\^$|]/gu, "\\$&") : "^.*$";
  return new RegExp(sanitizedWord, "i");
};
const setValuesInString = (message, values = {}) => (message ?? "").replace(/\{(?<valueName>.*?)\}/gu, (match, valueName) => values[valueName] ?? match);
const addLTRMark = (value) => (
  // Make sure the string value is LTR. This prevent issues with RTL language used in LTR containers.
  `‎${value ?? ""}‎`
);
const kebabToPascal = (string) => string.split("-").map(capitalize).join("");
const camelToKebab = (string) => string.replace(upperBeforeLower, (upper, remainder) => `${remainder === 0 ? "" : "-"}${upper.toLowerCase()}`);
const upperBeforeLower = /[A-Z]+(?![a-z])|[A-Z]/gu;
const capitalize = (string) => string.charAt(0).toUpperCase() + string.slice(1);
const uncapitalize = (string) => string.charAt(0).toLowerCase() + string.slice(1);
const camelToHuman = (string) => capitalize(string.replace(upperBeforeLower, (upper, remainder) => `${remainder === 0 ? "" : " "}${upper}`));
const devToolsAwareTimeout = (callback, timeout) => {
  const interval = timeout > longTimeoutThreshold ? longTimeoutInterval : timeout / shortTimeoutIntervals;
  let elapsed = 0;
  const reference = setInterval(() => {
    elapsed += interval;
    if (elapsed >= timeout) {
      clearInterval(reference);
      callback();
    }
  }, interval);
  return reference;
};
const longTimeoutThreshold = 4e3;
const longTimeoutInterval = 2e3;
const shortTimeoutIntervals = 4;
const isNotNull = (item) => item !== null;
const isNotUndefined = (item) => item !== void 0;
const identity = (value) => value;
const debounce = (func, waitFor = 100) => {
  let timeout;
  return (...args) => {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, waitFor);
  };
};
const hasSameOrigin = (url1, url2, ignoreProtocol = false) => {
  if (!url1 || !url2) {
    return false;
  }
  const url1Obj = new URL(url1);
  const url2Obj = new URL(url2);
  if (!ignoreProtocol && url1Obj.protocol !== url2Obj.protocol) {
    return false;
  }
  if (url1Obj.host == null || url2Obj.host == null) {
    return false;
  }
  return url1Obj.host.toLowerCase() === url2Obj.host.toLowerCase() && url1Obj.port === url2Obj.port;
};
const isURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};
exports.Deferred = Deferred;
exports.addLTRMark = addLTRMark;
exports.camelToHuman = camelToHuman;
exports.camelToKebab = camelToKebab;
exports.capitalize = capitalize;
exports.classes = classes;
exports.closestElement = closestElement;
exports.createFilterExpression = createFilterExpression;
exports.debounce = debounce;
exports.defaultLocale = defaultLocale;
exports.devToolsAwareTimeout = devToolsAwareTimeout;
exports.extractMinorVersion = extractMinorVersion;
exports.fetchT9nStringsBundle = fetchT9nStringsBundle;
exports.focusElement = focusElement;
exports.generateGuid = generateGuid;
exports.getElementAttribute = getElementAttribute;
exports.getElementDir = getElementDir;
exports.getElementLocales = getElementLocales;
exports.getElementTheme = getElementTheme;
exports.getPreamble = getPreamble;
exports.hasSameOrigin = hasSameOrigin;
exports.identity = identity;
exports.isEsriInternalEnv = isEsriInternalEnv;
exports.isNotNull = isNotNull;
exports.isNotUndefined = isNotUndefined;
exports.isURL = isURL;
exports.kebabToPascal = kebabToPascal;
exports.mappedFind = mappedFind;
exports.normalizeLocale = normalizeLocale;
exports.observeAncestorsMutation = observeAncestorsMutation;
exports.quoteString = quoteString;
exports.safeAsyncCall = safeAsyncCall;
exports.safeCall = safeCall;
exports.setFocusOnElement = setFocusOnElement;
exports.setValuesInString = setValuesInString;
exports.startLocaleObserver = startLocaleObserver;
exports.supportedLocales = supportedLocales;
exports.uncapitalize = uncapitalize;
