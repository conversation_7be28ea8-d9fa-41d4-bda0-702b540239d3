/**
 * The interface for translated strings.
 */
export interface GenericT9nStrings {
    [key: string]: GenericT9nStrings | string;
}
declare const supportedLocalesArray: ["ar", "bg", "bs", "ca", "cs", "da", "de", "el", "en", "es", "et", "fi", "fr", "he", "hr", "hu", "id", "it", "ja", "ko", "lt", "lv", "nl", "nb", "no", "pl", "pt-BR", "pt-PT", "ro", "ru", "sk", "sl", "sr", "sv", "th", "tr", "uk", "vi", "zh-CN", "zh-HK", "zh-TW"];
/**
 * The list of supported locales for ArcGIS Maps SDK for JavaScript components.
 */
export declare const supportedLocales: Set<"hr" | "th" | "tr" | "ar" | "bg" | "bs" | "ca" | "cs" | "da" | "de" | "el" | "en" | "es" | "et" | "fi" | "fr" | "he" | "hu" | "id" | "it" | "ja" | "ko" | "lt" | "lv" | "nl" | "nb" | "no" | "pl" | "pt-BR" | "pt-PT" | "ro" | "ru" | "sk" | "sl" | "sr" | "sv" | "uk" | "vi" | "zh-CN" | "zh-HK" | "zh-TW">;
export type SupportedLocale = (typeof supportedLocalesArray)[number];
export declare const defaultLocale = "en";
/**
 * Fetch the T9N strings bundle for the given locale, assets path and prefix.
 * The locale must be one of the supported locales.
 * If the locale is not supported, it will default to 'en'.
 * If the T9N strings bundle cannot be found, it will default to 'en'.
 *
 * @remarks
 * Rather than using this function directly, prefer the `useT9n()` controller.
 *
 * @example
 * ```ts
 * const t9nStrings = await fetchT9nStringsBundle("en", getAssetPath("./assets/coding-editor/t9n"), "messages.");
 * ```
 */
export declare const fetchT9nStringsBundle: <Strings extends GenericT9nStrings>(
/** The locale for which to fetch the T9N strings  */
locale: string, 
/** The path to the assets folder where the T9N strings are located */
assetsPath: string, 
/** The prefix to use for the T9N strings file name. */
prefix?: string) => Promise<Strings>;
/**
 * Get the locale of the given element.
 * It will look for the lang attribute on the element and its ancestors.
 * If not lang is found, it will default to 'en'.
 */
export declare const getElementLocales: (element: HTMLElement) => {
    readonly lang: string;
    readonly t9nLocale: SupportedLocale;
};
export declare const normalizeLocale: (locale: string) => SupportedLocale;
export type LocaleObserver<Strings extends GenericT9nStrings = GenericT9nStrings> = {
    /** The T9N strings of the component */
    t9nStrings: Strings;
    /**
     * The locale of the component set by the `lang` attribute on the component host element or one of its ancestors.
     */
    lang: string;
    /**
     * The locale used by the component to load the T9N strings.
     * It may be different than the locale of the component host element that was set by the `lang` attribute.
     */
    t9nLocale: SupportedLocale;
};
/**
 * Start the locale observer for the given component.
 * The callback will be called when the locale changes for the component.
 * It will observe the lang attribute on the component and its ancestors.
 * The callback is called once at the beginning.
 *
 * @remarks
 * Rather than using this function directly, prefer the `useT9n()` controller.
 */
export declare const startLocaleObserver: <Strings extends GenericT9nStrings = GenericT9nStrings>(
/** The Web component HTML element that is doing the fetching */
element: HTMLElement, 
/**
 * The callback to get path to the assets folder where the T9N strings are
 * located.
 *
 * @example
 * ```ts
 * () => getAssetPath("./assets")
 * ```
 */
getAssetsPath: () => string, 
/** The callback to call when the locale changes  */
onUpdated: (payload: LocaleObserver<Strings>) => void, 
/**
 * Optionally override the asset file name.
 * Default file name is the component tag name without the part before the
 * first dash (e.g. `arcgis-map` becomes `map`).
 *
 * Set to null if the component has no localization strings, but you still
 * wish to use `startLocaleObserver` to get the locale information.
 */
assetName?: string | null) => (() => void);
export {};
