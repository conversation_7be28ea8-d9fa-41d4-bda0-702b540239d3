export declare const extractMinorVersion: (version: string) => string;
/**
 * Creates preamble text from a version number. The preamble text is used in stencil.config.ts and inserts a comment at the top of the generated bundles.
 * The preamble text contains the version number and a link to the license.
 * The version number is typically extracted from the package.json file.
 * @example
 * ```ts
 * const preamble = getPreamble("4.18.2-beta.1");
 * console.log(preamble);
 * // All material copyright Esri, All Rights Reserved, unless otherwise specified.
 * // See https://js.arcgis.com/4.18/esri/copyright.txt for details.
 * // v4.18.2-beta.1
 * ```
 * @param version the version number, typically coming from package.json.
 * @returns a string containing the preamble text.
 */
export declare const getPreamble: (version: string) => string;
