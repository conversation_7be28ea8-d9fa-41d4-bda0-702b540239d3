/**
 * Add quotes to a string for display purposes.
 * If the string contains a double quote, then single quotes will be used.
 * If the string contains a single quote, then double quotes will be used.
 * If the string contains both, then double quotes will be used and the single quotes will be escaped.
 * @param value The string to quote
 * @returns The quoted string
 */
export declare const quoteString: (value: string) => string;
/**
 * Create a filter expression from a filter word.
 * @param filterWord The filter word to create the expression from.
 * @returns The filter expression.
 */
export declare const createFilterExpression: (filterWord: string) => RegExp;
/**
 * Replace values in a string using the format {valueName} with the value from the values object.
 * If the value is not found in the values object, then the value is not replaced.
 * @param message The string to replace values in.
 * @param values The values to replace in the string.
 * @returns The string with the values replaced.
 */
export declare const setValuesInString: (message: string | null | undefined, values?: Record<string, string>) => string;
/**
 * Add LTR marks to a string to ensure it is displayed as LTR.
 * @param value The string to add LTR marks to.
 * @returns The string with LTR marks.
 */
export declare const addLTRMark: (value: string | undefined) => string;
