/** Convert kebab-case string to PascalCase */
export declare const kebabToPascal: (string: string) => string;
/** Convert camelCase string to kebab-case */
export declare const camelToKebab: (string: string) => string;
export declare const capitalize: <T extends string>(string: T) => Capitalize<T>;
export declare const uncapitalize: <T extends string>(string: T) => Uncapitalize<T>;
export declare const camelToHuman: (string: string) => string;
