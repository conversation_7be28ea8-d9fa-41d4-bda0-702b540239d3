/**
 * Safeguard to ensure that an item is not null.
 * @param item The item to check.
 * @returns Returns true if the item is not null.
 */
export declare const isNotNull: <T>(item: T | null) => item is T;
/**
 * Safe guard to ensure that an item is not undefined.
 * @param item The item to check.
 * @returns Returns true if the item is not undefined.
 */
export declare const isNotUndefined: <T>(item: T | undefined) => item is T;
