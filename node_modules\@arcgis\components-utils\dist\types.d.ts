export type Nil = null | undefined;
export type IHandle = {
    remove: () => void;
};
/**
 * Identity function - returns back the first parameter
 *
 * Useful when providing a "mapping function" is required, but you have no need
 * to change the value
 */
export declare const identity: <T>(value: T) => T;
/**
 * Get back a type whose keys are all marked as mutable (no longer "readonly")
 *
 * It's a best practice to mark all keys as readonly by default in all the
 * interfaces and types you create. Benefits:
 * - Explicitly documents that a given key won't be modified/should not be
 *   modified
 * - Favors more declarative and functional style programming
 * - Helps prevent bugs related to mutation unexpectedly affecting other places
 *   that held a reference to the same object
 *
 * Your function could return a type like "ResolvedConfig", that has all keys
 * marked as readonly. But inside the function you may wish to modify some of
 * the keys while creating the object - casting the object to
 * `Writable<ResolvedConfig>` helps with that
 */
export type Writable<T> = {
    -readonly [k in keyof T]: T[k];
};
