/**
 * Allows to debounce a function.
 * @template F Function type that should extend a function with any parameters and a return type.
 * @param func Function to be debounced
 * @param waitFor Debounce time in milliseconds
 * @returns Returns a function that can be called to debounce the given function.
 */
export declare const debounce: <F extends (...args: Parameters<F>) => ReturnType<F>>(func: F, waitFor?: number) => ((...args: Parameters<F>) => void);
