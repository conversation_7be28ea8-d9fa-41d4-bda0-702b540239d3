/**
 * Compares two url strings for their origin and returns true if they have the same origin.
 * @param url1 First url string
 * @param url2 Second url string
 * @param ignoreProtocol Indicates if protocol comparison should be ignored
 * @returns True if the two url strings have the same origin
 */
export declare const hasSameOrigin: (url1: string | null | undefined, url2: string | null | undefined, ignoreProtocol?: boolean) => boolean;
/**
 * Tests if a url string is a URL or not.
 * @param url The url string to test
 * @returns True if the string is a URL.
 */
export declare const isURL: (url: string) => boolean;
