/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"../core/Accessor.js";import{isSome as r}from"../core/arrayUtils.js";import s from"../core/Clonable.js";import o from"../core/Identifiable.js";import i from"../core/JSONSupport.js";import{property as a}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import{subclass as n}from"../core/accessorSupport/decorators/subclass.js";let l=0,p=class extends(i.JSONSupportMixin(s.ClonableMixin(o.IdentifiableMixin(t)))){constructor(e){super(e),this.id=`${Date.now().toString(16)}-analysis-${l++}`,this.title=null}get parent(){return this._get("parent")}set parent(e){const t=this.parent;if(null!=t)switch(t.type){case"line-of-sight":case"dimension":case"viewshed":t.releaseAnalysis(this);break;case"2d":case"3d":t.analyses.includes(this)&&t.analyses.remove(this)}this._set("parent",e)}get isEditable(){return this.requiredPropertiesForEditing.every(r)}};e([a({type:String,constructOnly:!0,clonable:!1})],p.prototype,"id",void 0),e([a({type:String})],p.prototype,"title",void 0),e([a({clonable:!1,value:null})],p.prototype,"parent",null),e([a({readOnly:!0})],p.prototype,"isEditable",null),p=e([n("esri.analysis.Analysis")],p);const c=p;export{c as default};
