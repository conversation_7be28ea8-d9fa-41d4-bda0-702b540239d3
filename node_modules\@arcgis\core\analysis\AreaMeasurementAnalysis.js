/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import r from"./Analysis.js";import{equals as t}from"../core/arrayUtils.js";import o from"../core/Logger.js";import{measurementAreaUnits as s}from"../core/unitUtils.js";import{property as i}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import{subclass as l}from"../core/accessorSupport/decorators/subclass.js";import n from"../geometry/Polygon.js";let p=class extends r{constructor(e){super(e),this.type="area-measurement",this.unit=null}set geometry(e){null!=e?(e.rings.length>1&&o.getLogger(this).warn("Measuring polygons with multiple rings is not supported."),this._set("geometry",e.clone())):this._set("geometry",null)}get requiredPropertiesForEditing(){if(null!=this.geometry&&1===this.geometry.rings.length){const e=this.geometry.rings[0];if(e.length<=2||!t(e[0],e[e.length-1]))return[null]}return[this.geometry]}clear(){this.geometry=null}};e([i({type:["area-measurement"]})],p.prototype,"type",void 0),e([i({value:null,type:n})],p.prototype,"geometry",null),e([i({type:s,value:null})],p.prototype,"unit",void 0),e([i({readOnly:!0})],p.prototype,"requiredPropertiesForEditing",null),p=e([l("esri.analysis.AreaMeasurementAnalysis")],p);const m=p;export{m as default};
