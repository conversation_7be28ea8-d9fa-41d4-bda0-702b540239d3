/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"./Analysis.js";import n from"./DimensionSimpleStyle.js";import o from"./LengthDimension.js";import s from"../core/Collection.js";import{referenceSetter as i,castForReferenceSetter as r}from"../core/collectionUtils.js";import{watch as l,syncAndInitial as p}from"../core/reactiveUtils.js";import{property as u}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as m}from"../core/accessorSupport/decorators/subclass.js";import a from"../geometry/Extent.js";import{projectOrLoadMany as c}from"../geometry/projection.js";const d=s.ofType(o);let f=class extends t{constructor(e){super(e),this.type="dimension",this.style=new n,this.extent=null}initialize(){this.addHandles(l((()=>this._computeExtent()),(e=>{null==e?.pending&&this._set("extent",null!=e?e.extent:null)}),p))}get dimensions(){return this._get("dimensions")||new d}set dimensions(e){this._set("dimensions",i(e,this.dimensions,d))}get spatialReference(){for(const e of this.dimensions){if(null!=e.startPoint)return e.startPoint.spatialReference;if(null!=e.endPoint)return e.endPoint.spatialReference}return null}get requiredPropertiesForEditing(){return this.dimensions.reduce(((e,t)=>(e.push(t.startPoint,t.endPoint),e)),[])}async waitComputeExtent(){const e=this._computeExtent();return null!=e?e.pending:Promise.resolve()}_computeExtent(){const e=this.spatialReference;if(null==e)return{pending:null,extent:null};const t=[];for(const s of this.dimensions)null!=s.startPoint&&t.push(s.startPoint),null!=s.endPoint&&t.push(s.endPoint);const n=c(t,e);if(null!=n.pending)return{pending:n.pending,extent:null};let o=null;return null!=n.geometries&&(o=n.geometries.reduce(((e,t)=>null==e?null!=t?a.fromPoint(t):null:null!=t?e.union(a.fromPoint(t)):e),null)),{pending:null,extent:o}}clear(){this.dimensions.removeAll()}};e([u({type:["dimension"]})],f.prototype,"type",void 0),e([u({cast:r,type:d,nonNullable:!0})],f.prototype,"dimensions",null),e([u({readOnly:!0})],f.prototype,"spatialReference",null),e([u({types:{key:"type",base:null,typeMap:{simple:n}},nonNullable:!0})],f.prototype,"style",void 0),e([u({value:null,readOnly:!0})],f.prototype,"extent",void 0),e([u({readOnly:!0})],f.prototype,"requiredPropertiesForEditing",null),f=e([m("esri.analysis.DimensionAnalysis")],f);const y=f;export{y as default};
