/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import e from"../Color.js";import r from"../core/Clonable.js";import t from"../core/JSONSupport.js";import{toPt as s,px2pt as i}from"../core/screenUtils.js";import{property as p}from"../core/accessorSupport/decorators/property.js";import{Integer as l}from"../core/accessorSupport/ensureType.js";import"../core/has.js";import"../core/RandomLCG.js";import{subclass as n}from"../core/accessorSupport/decorators/subclass.js";let c=class extends(t.JSONSupportMixin(r)){constructor(o){super(o),this.type="simple",this.color=new e("black"),this.lineSize=2,this.fontSize=10,this.textColor=new e("black"),this.textBackgroundColor=new e([255,255,255,.6])}};o([p({type:["simple"],readOnly:!0,json:{write:{isRequired:!0}}})],c.prototype,"type",void 0),o([p({type:e,nonNullable:!0,json:{type:[l],write:{isRequired:!0}}})],c.prototype,"color",void 0),o([p({type:Number,cast:s,nonNullable:!0,range:{min:i(1)},json:{write:{isRequired:!0}}})],c.prototype,"lineSize",void 0),o([p({type:Number,cast:s,nonNullable:!0,json:{write:{isRequired:!0}}})],c.prototype,"fontSize",void 0),o([p({type:e,nonNullable:!0,json:{type:[l],write:{isRequired:!0}}})],c.prototype,"textColor",void 0),o([p({type:e,nonNullable:!0,json:{type:[l],write:{isRequired:!0}}})],c.prototype,"textBackgroundColor",void 0),c=o([n("esri.analysis.DimensionSimpleStyle")],c);const a=c;export{a as default};
