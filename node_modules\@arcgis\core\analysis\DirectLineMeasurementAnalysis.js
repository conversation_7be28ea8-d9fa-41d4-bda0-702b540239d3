/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import r from"./Analysis.js";import{measurementLengthUnits as e}from"../core/unitUtils.js";import{property as o}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as s}from"../core/accessorSupport/decorators/subclass.js";import i from"../geometry/Point.js";let n=class extends r{constructor(t){super(t),this.type="direct-line-measurement",this.startPoint=null,this.endPoint=null,this.unit=null}get requiredPropertiesForEditing(){return[this.startPoint,this.endPoint]}clear(){this.startPoint=null,this.endPoint=null}};t([o({type:["direct-line-measurement"]})],n.prototype,"type",void 0),t([o({type:i})],n.prototype,"startPoint",void 0),t([o({type:i})],n.prototype,"endPoint",void 0),t([o({type:e,value:null})],n.prototype,"unit",void 0),t([o({readOnly:!0})],n.prototype,"requiredPropertiesForEditing",null),n=t([s("esri.analysis.DirectLineMeasurementAnalysis")],n);const p=n;export{p as default};
