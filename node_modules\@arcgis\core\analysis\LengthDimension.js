/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import{LengthDimensionMeasureType as e,lengthDimensionMeasureType as r}from"./dimensionUtils.js";import t from"../core/Clonable.js";import{cyclicalDegrees as s}from"../core/Cyclical.js";import i from"../core/JSONSupport.js";import{property as p}from"../core/accessorSupport/decorators/property.js";import{cast as n}from"../core/accessorSupport/decorators/cast.js";import"../core/has.js";import"../core/RandomLCG.js";import{subclass as m}from"../core/accessorSupport/decorators/subclass.js";import{ensureNumber as c}from"../core/accessorSupport/ensureType.js";import a from"../geometry/Point.js";let l=class extends(i.JSONSupportMixin(t)){constructor(o){super(o),this.type="length",this.startPoint=null,this.endPoint=null,this.measureType=e.Direct,this.offset=0,this.orientation=0}};o([p({type:["length"],json:{write:{isRequired:!0}}})],l.prototype,"type",void 0),o([p({type:a,json:{write:{isRequired:!0}}})],l.prototype,"startPoint",void 0),o([p({type:a,json:{write:{isRequired:!0}}})],l.prototype,"endPoint",void 0),o([p({type:r,nonNullable:!0,json:{write:{isRequired:!0}}})],l.prototype,"measureType",void 0),o([p({type:Number,nonNullable:!0,json:{write:{isRequired:!0}}})],l.prototype,"offset",void 0),o([p({type:Number,nonNullable:!0,json:{write:{isRequired:!0}}}),n((o=>s.normalize(c(o),0,!0)))],l.prototype,"orientation",void 0),l=o([m("esri.analysis.LengthDimension")],l);const u=l;export{u as default};
