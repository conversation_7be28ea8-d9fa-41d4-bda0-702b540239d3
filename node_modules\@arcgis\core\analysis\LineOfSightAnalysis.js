/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as t}from"../chunks/tslib.es6.js";import e from"./Analysis.js";import o from"./LineOfSightAnalysisObserver.js";import r from"./LineOfSightAnalysisTarget.js";import s from"../core/Collection.js";import{referenceSetter as i,castForReferenceSetter as n}from"../core/collectionUtils.js";import{watch as l,syncAndInitial as p}from"../core/reactiveUtils.js";import{property as a}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as u}from"../core/accessorSupport/decorators/subclass.js";import{projectOrLoad as c}from"../geometry/projection.js";import{fromValues as m,expandWithVec3 as g,toExtent as f}from"../geometry/support/aaBoundingBox.js";import{getGeometryEffectiveElevationMode as h}from"../support/elevationInfoUtils.js";const y=s.ofType(r);let d=class extends e{constructor(t){super(t),this.type="line-of-sight",this.observer=null,this.extent=null}initialize(){this.addHandles(l((()=>this._computeExtent()),(t=>{null==t?.pending&&this._set("extent",null!=t?t.extent:null)}),p))}get targets(){return this._get("targets")||new y}set targets(t){this._set("targets",i(t,this.targets,y))}get spatialReference(){return null!=this.observer?.position?this.observer.position.spatialReference:null}get requiredPropertiesForEditing(){return[this.observer?.position]}async waitComputeExtent(){const t=this._computeExtent();return null!=t?t.pending:Promise.resolve()}_computeExtent(){const t=this.spatialReference;if(null==this.observer?.position||null==t)return null;const e=t=>"absolute-height"===h(t.position,t.elevationInfo),o=this.observer.position,r=m(o.x,o.y,o.z,o.x,o.y,o.z);for(const i of this.targets)if(null!=i.position){const e=c(i.position,t);if(null!=e.pending)return{pending:e.pending,extent:null};if(null!=e.geometry){const{x:t,y:o,z:s}=e.geometry;g(r,[t,o,s])}}const s=f(r,t);return e(this.observer)&&this.targets.every(e)||(s.zmin=void 0,s.zmax=void 0),{pending:null,extent:s}}clear(){this.observer=null,this.targets.removeAll()}};t([a({type:["line-of-sight"]})],d.prototype,"type",void 0),t([a({type:o,json:{read:!0,write:!0}})],d.prototype,"observer",void 0),t([a({cast:n,type:y,nonNullable:!0,json:{read:!0,write:!0}})],d.prototype,"targets",null),t([a({value:null,readOnly:!0})],d.prototype,"extent",void 0),t([a({readOnly:!0})],d.prototype,"spatialReference",null),t([a({readOnly:!0})],d.prototype,"requiredPropertiesForEditing",null),d=t([u("esri.analysis.LineOfSightAnalysis")],d);const v=d;export{v as default};
