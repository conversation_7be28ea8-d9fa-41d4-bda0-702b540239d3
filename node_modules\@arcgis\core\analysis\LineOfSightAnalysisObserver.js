/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import{featureReferenceEquals as r,featureReferenceProperty as e}from"./featureReferenceUtils.js";import t from"../core/Accessor.js";import s from"../core/Clonable.js";import i from"../core/JSONSupport.js";import{equalsMaybe as p}from"../core/maybe.js";import{property as n}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as a}from"../core/accessorSupport/decorators/subclass.js";import{persistable as m}from"../core/accessorSupport/decorators/persistable.js";import c from"../geometry/Point.js";import l from"../symbols/support/ElevationInfo.js";let f=class extends(i.JSONSupportMixin(s.ClonableMixin(t))){constructor(o){super(o),this.position=null,this.elevationInfo=null,this.feature=null}equals(o){return p(this.position,o.position)&&p(this.elevationInfo,o.elevationInfo)&&r(this.feature,o.feature)}};o([n({type:c,json:{write:{isRequired:!0}}})],f.prototype,"position",void 0),o([n({type:l}),m()],f.prototype,"elevationInfo",void 0),o([n(e)],f.prototype,"feature",void 0),f=o([a("esri.analysis.LineOfSightAnalysisObserver")],f);const u=f;export{u as default};
