/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import{featureReferenceEquals as r,featureReferenceProperty as e}from"./featureReferenceUtils.js";import t from"../core/Clonable.js";import s from"../core/JSONSupport.js";import{equalsMaybe as i}from"../core/maybe.js";import{property as p}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as n}from"../core/accessorSupport/decorators/subclass.js";import{persistable as a}from"../core/accessorSupport/decorators/persistable.js";import m from"../geometry/Point.js";import c from"../symbols/support/ElevationInfo.js";let l=class extends(s.JSONSupportMixin(t)){constructor(o){super(o),this.position=null,this.elevationInfo=null,this.feature=null}equals(o){return i(this.position,o.position)&&i(this.elevationInfo,o.elevationInfo)&&r(this.feature,o.feature)}};o([p({type:m,json:{write:!0,origins:{"web-scene":{write:{isRequired:!0}}}}}),a()],l.prototype,"position",void 0),o([p({type:c}),a()],l.prototype,"elevationInfo",void 0),o([p(e)],l.prototype,"feature",void 0),l=o([n("esri.analysis.LineOfSightAnalysisTarget")],l);const f=l;export{f as default};
