/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"./Analysis.js";import r from"./SlicePlane.js";import o from"../core/Collection.js";import{referenceSetter as s,castForReferenceSetter as l}from"../core/collectionUtils.js";import{property as i}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as p}from"../core/accessorSupport/decorators/subclass.js";let a=class extends t{constructor(e){super(e),this.type="slice",this.tiltEnabled=!1,this.shape=null,this.excludeGroundSurface=!1}get excludedLayers(){return this._get("excludedLayers")||new o}set excludedLayers(e){this._set("excludedLayers",s(e,this._get("excludedLayers")))}get requiredPropertiesForEditing(){return[this.shape?.position]}clear(){this.shape=null}};e([i({type:["slice"]})],a.prototype,"type",void 0),e([i()],a.prototype,"tiltEnabled",void 0),e([i({types:{key:"type",base:null,typeMap:{plane:r},defaultKeyValue:"plane"}})],a.prototype,"shape",void 0),e([i({cast:l,clonable:e=>e.slice()})],a.prototype,"excludedLayers",null),e([i({type:Boolean,nonNullable:!0})],a.prototype,"excludeGroundSurface",void 0),e([i({readOnly:!0})],a.prototype,"requiredPropertiesForEditing",null),a=e([p("esri.analysis.SliceAnalysis")],a);const c=a;export{c as default};
