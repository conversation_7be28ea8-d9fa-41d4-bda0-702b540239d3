/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as o}from"../chunks/tslib.es6.js";import t from"../core/Clonable.js";import{cyclicalDegrees as r}from"../core/Cyclical.js";import e from"../core/JSONSupport.js";import{equalsMaybe as i}from"../core/maybe.js";import{property as s}from"../core/accessorSupport/decorators/property.js";import{cast as p}from"../core/accessorSupport/decorators/cast.js";import"../core/has.js";import"../core/RandomLCG.js";import{subclass as a}from"../core/accessorSupport/decorators/subclass.js";import{ensureNumber as n}from"../core/accessorSupport/ensureType.js";import{persistable as l}from"../core/accessorSupport/decorators/persistable.js";import m from"../geometry/Point.js";let c=class extends(t.ClonableMixin(e)){constructor(o){super(o),this.type="plane",this.position=null,this.heading=0,this.tilt=0,this.width=10,this.height=10}equals(o){return this.heading===o.heading&&this.tilt===o.tilt&&i(this.position,o.position)&&this.width===o.width&&this.height===o.height}};o([s({readOnly:!0,json:{read:!1,write:!0}})],c.prototype,"type",void 0),o([s({type:m}),l()],c.prototype,"position",void 0),o([s({type:Number,nonNullable:!0,range:{min:0,max:360}}),l(),p((o=>r.normalize(n(o),0,!0)))],c.prototype,"heading",void 0),o([s({type:Number,nonNullable:!0,range:{min:0,max:360}}),l(),p((o=>r.normalize(n(o),0,!0)))],c.prototype,"tilt",void 0),o([s({type:Number,nonNullable:!0}),l()],c.prototype,"width",void 0),o([s({type:Number,nonNullable:!0}),l()],c.prototype,"height",void 0),c=o([a("esri.analysis.SlicePlane")],c);const h=c;export{h as default};
