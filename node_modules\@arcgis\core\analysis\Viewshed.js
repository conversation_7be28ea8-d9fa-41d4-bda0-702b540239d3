/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import{featureReferenceEquals as r,featureReferenceProperty as i}from"./featureReferenceUtils.js";import o from"../core/Clonable.js";import{cyclicalDegrees as t}from"../core/Cyclical.js";import s from"../core/JSONSupport.js";import{equalsMaybe as a}from"../core/maybe.js";import{property as l}from"../core/accessorSupport/decorators/property.js";import{cast as n}from"../core/accessorSupport/decorators/cast.js";import"../core/has.js";import"../core/RandomLCG.js";import{subclass as p}from"../core/accessorSupport/decorators/subclass.js";import{ensureNumber as c}from"../core/accessorSupport/ensureType.js";import m from"../geometry/Point.js";let u=class extends(s.JSONSupportMixin(o)){constructor(e){super(e),this.observer=null,this.farDistance=1e3,this.heading=0,this.tilt=90,this.horizontalFieldOfView=45,this.verticalFieldOfView=45,this.feature=null}isValid(){return null!=this.observer&&this.farDistance>0}equals(e){return a(this.observer,e.observer)&&this.farDistance===e.farDistance&&this.heading===e.heading&&this.tilt===e.tilt&&this.horizontalFieldOfView===e.horizontalFieldOfView&&this.verticalFieldOfView===e.verticalFieldOfView&&r(this.feature,e.feature)}};e([l({type:m,json:{write:{isRequired:!0}}})],u.prototype,"observer",void 0),e([l({type:Number,nonNullable:!0,range:{min:0},json:{write:{isRequired:!0}}})],u.prototype,"farDistance",void 0),e([l({type:Number,nonNullable:!0,json:{write:{isRequired:!0}}}),n((e=>t.normalize(c(e),void 0,!0)))],u.prototype,"heading",void 0),e([l({type:Number,nonNullable:!0,range:{min:0,max:180},json:{write:{isRequired:!0}}})],u.prototype,"tilt",void 0),e([l({type:Number,nonNullable:!0,range:{min:0,max:360},json:{write:{isRequired:!0}}})],u.prototype,"horizontalFieldOfView",void 0),e([l({type:Number,nonNullable:!0,range:{min:0,max:180},json:{write:{isRequired:!0}}})],u.prototype,"verticalFieldOfView",void 0),e([l(i)],u.prototype,"feature",void 0),e([l({json:{read:!1}})],u.prototype,"isValid",null),u=e([p("esri.analysis.Viewshed")],u);const d=u;export{d as default};
