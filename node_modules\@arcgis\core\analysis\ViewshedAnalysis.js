/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../chunks/tslib.es6.js";import t from"./Analysis.js";import r from"./Viewshed.js";import n from"../core/Collection.js";import{referenceSetter as s,castForReferenceSetter as i}from"../core/collectionUtils.js";import{cyclicalDegrees as o}from"../core/Cyclical.js";import{deg2rad as l,clamp as a}from"../core/mathUtils.js";import{watch as p,syncAndInitial as c}from"../core/reactiveUtils.js";import{property as u}from"../core/accessorSupport/decorators/property.js";import"../core/has.js";import"../core/Logger.js";import"../core/RandomLCG.js";import{subclass as m}from"../core/accessorSupport/decorators/subclass.js";import d from"../geometry/Extent.js";import{projectOrLoadMany as h}from"../geometry/projection.js";const f=n.ofType(r);let x=class extends t{constructor(e){super(e),this.type="viewshed",this._extent=null}initialize(){this.addHandles(p((()=>this._computeExtent()),(e=>{null==e.pending&&(this._extent=e.extent)}),c))}get viewsheds(){return this._get("viewsheds")||new f}set viewsheds(e){this._set("viewsheds",s(e,this.viewsheds,f))}get spatialReference(){for(const e of this.viewsheds)if(null!=e.observer)return e.observer.spatialReference;return null}get extent(){return this._extent}get requiredPropertiesForEditing(){return this.viewsheds.items.map((({observer:e})=>e))}async waitComputeExtent(){const e=this._computeExtent();null!=e.pending&&await e.pending}_computeExtent(){const{spatialReference:e}=this;if(null==e)return{pending:null,extent:null};const t=this.viewsheds.filter((e=>null!=e.observer)),r=t.map((e=>e.observer)).toArray(),n=h(r,e);if(null!=n.pending)return{pending:n.pending,extent:null};return{pending:null,extent:n.geometries.map(((e,r)=>{const n=t.at(r);return null!=e&&null!=n?this._computeViewshedExtent(this.viewsheds.at(r),e):null})).filter((e=>null!=e)).reduce(((e,t)=>y(e,t)),null)}}_computeViewshedExtent(e,t){const{farDistance:r,heading:n,tilt:s,horizontalFieldOfView:i,verticalFieldOfView:p}=e,{spatialReference:c}=t,u=i/2,m=p/2,h=r/c.metersPerUnit,f=[o.normalize(n-u),n,o.normalize(n+u)],x=d.fromPoint(t),y=e=>{const t=f.map((t=>o.normalize(t-e)));if(t[0]>t[2]||360===i)return h;const r=t.map((e=>Math.abs(e>180?360-e:e))).reduce(((e,t)=>e>t?t:e));return r>90?0:h*Math.cos(l(r))};x.xmax+=y(90),x.xmin-=y(-90),x.ymax+=y(0),x.ymin-=y(180);const w=t.z;if(null!=w){let e=w,t=w;const n=s-90,i=a(n+m,-90,90),o=a(n-m,-90,90),l=c?.isGeographic?r:h;e+=l*g(i),t+=l*g(o);const p=v(m)*l,d=g(n)*p*(1-v(u));s<90&&(e-=d),s>90&&(t-=d),x.zmax=Math.max(e,w),x.zmin=Math.min(t,w)}return x}clear(){this.viewsheds.removeAll()}};function y(e,t){return null==e?t:null==t?e:e.union(t)}function v(e){return Math.cos(l(e))}function g(e){return Math.sin(l(e))}e([u({type:["viewshed"]})],x.prototype,"type",void 0),e([u({cast:i,type:f,nonNullable:!0})],x.prototype,"viewsheds",null),e([u({readOnly:!0})],x.prototype,"spatialReference",null),e([u()],x.prototype,"_extent",void 0),e([u({readOnly:!0})],x.prototype,"extent",null),e([u({readOnly:!0})],x.prototype,"requiredPropertiesForEditing",null),x=e([m("esri.analysis.ViewshedAnalysis")],x);const w=x;export{w as default};
