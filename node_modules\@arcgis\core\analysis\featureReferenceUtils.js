/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{a as e,l as t,b as r}from"../chunks/vec32.js";import{create as n}from"../core/libs/gl-matrix-2/factories/vec3f64.js";import{create as l,fromPoints as o}from"../geometry/support/ray.js";import{StoreResults as i}from"../views/3d/webgl-engine/lib/IntersectorInterfaces.js";import{toGraphic as u,hasLod as s,getIntersectedFeatureBSRadius as a}from"../views/3d/webgl-engine/lib/intersectorUtilsConversions.js";function c(e,t){return d(e)===d(t)}function d(e){if(null==e)return null;const t=null!=e.layer?e.layer.id:"";let r=null;return r=null!=e.objectId?e.objectId:null!=e.layer&&"objectIdField"in e.layer&&null!=e.layer.objectIdField&&null!=e.attributes?e.attributes[e.layer.objectIdField]:e.uid,null==r?null:`o-${t}-${r}`}const b={json:{write:{writer:f,target:{"feature.layerId":{type:[Number,String],isRequired:!0},"feature.objectId":{type:[Number,String],isRequired:!0}}},origins:{"web-scene":{read:I}}}};function f(e,t){null!=e?.layer?.objectIdField&&null!=e.attributes&&(t.feature={layerId:e.layer.id,objectId:e.attributes[e.layer.objectIdField]})}function I(e){if(null!=e.layerId&&null!=e.objectId)return{uid:null,layer:{id:e.layerId,objectIdField:"ObjectId"},attributes:{ObjectId:e.objectId}}}function m(l,s,a,c){const{sceneIntersectionHelper:b}=l,{observer:f,observerFeatureId:I,targetFeatureId:m,target:g}=a;if(null==I&&null==m)return;c||(c=e=>e),e(p,f,g);const v=t(p),S=1;r(p,f,p,S/v);const F=o(p,g,y);s.options.store=i.ALL,b.intersectToolIntersectorRay(F,s);let N=null,w=null,R=null,A=null;for(const e of s.results.all){const t=u(e,l);if(null==t||null==e.distanceInRenderSpace)continue;const r=d(t);null!=r&&(null!=I&&r===I&&(N??=c(j(e,l,v)),e.distanceInRenderSpace-S<N&&(R=e)),null!=m&&r===m&&(w??=c(j(e,l,v)),null==A&&e.distanceInRenderSpace-S<v&&v-e.distanceInRenderSpace+S<w&&(A=e)))}const{observerAdjusted:T,targetAdjusted:h}=a;R?.getIntersectionPoint(T)?a.observerSurfaceNormal=R.getTransformedNormal(n()):a.observerSurfaceNormal=null,A?.getIntersectionPoint(h)?a.targetSurfaceNormal=A.getTransformedNormal(n()):a.targetSurfaceNormal=null}function j(e,t,r){if(s(e)){const n=a(e,t);if(null!=n)return Math.min(n*g,r)}return 1e-5*r}const g=.05,y=l(),p=n();export{c as featureReferenceEquals,b as featureReferenceProperty,d as getFeatureId,m as updatePointsFromFeatureReference};
