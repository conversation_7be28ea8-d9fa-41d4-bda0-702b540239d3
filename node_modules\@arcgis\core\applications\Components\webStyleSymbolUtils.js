/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{fetchSymbolFromStyle as t}from"../../symbols/support/webStyleSymbolUtils.js";function e(e,o,m,r,c,i){return t(e,o,m,(t=>{const e=c(t,r);return e?{format:"cimRef"===r?"cim":"web",url:e}:void 0}),{...i,acceptedFormats:"cimRef"===r?["cim"]:["web"]})}export{e as fetchSymbolFromStyle};
