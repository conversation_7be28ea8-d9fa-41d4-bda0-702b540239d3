import "../../interfaces";

export const extentToInBoundsRings: __esri.generalSharedKgUtils["extentToInBoundsRings"];
export const getBindParametersFromCypherQuery: __esri.generalSharedKgUtils["getBindParametersFromCypherQuery"];
export const getDefaultKnowledgeSublayerLabelingInfos: __esri.generalSharedKgUtils["getDefaultKnowledgeSublayerLabelingInfos"];
export const getDefaultLinkChartSublayerLabelingInfos: __esri.generalSharedKgUtils["getDefaultLinkChartSublayerLabelingInfos"];
export const newLinkChartLayerWithOptimizedGeometry: __esri.generalSharedKgUtils["newLinkChartLayerWithOptimizedGeometry"];
