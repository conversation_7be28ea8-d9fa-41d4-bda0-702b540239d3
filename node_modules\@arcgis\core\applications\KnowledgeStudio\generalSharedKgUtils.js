/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import r from"../../layers/LinkChartLayer.js";import{getBindParametersFromCypherQuery as e}from"../../layers/knowledgeGraph/cypherUtils.js";import{getLinkChartDefaultLabelingInfo as n,getMapDefaultLabelingInfo as t}from"../../layers/knowledgeGraph/layerUtils.js";import{utilsExtentToInBoundsRings as o}from"../../layers/knowledgeGraph/supportUtils.js";function s(r){return o(r)}async function a(r){return e(r)}function i(e){return new r(e)}function p(r,e,t){return n(r,e,t)}function u(r,e,n){return t(r,e,n)}export{s as extentToInBoundsRings,a as getBindParametersFromCypherQuery,u as getDefaultKnowledgeSublayerLabelingInfos,p as getDefaultLinkChartSublayerLabelingInfos,i as newLinkChartLayerWithOptimizedGeometry};
