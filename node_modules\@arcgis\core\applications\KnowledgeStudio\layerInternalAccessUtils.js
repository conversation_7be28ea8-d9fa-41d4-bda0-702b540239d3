/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
function n(n){return n.dataManager.inclusionModeDefinition}function i(n){n.dataManager.inclusionModeDefinition=null}function o(n,i){n.dataManager.inclusionModeDefinition=i}export{o as addInclusionDefinitionToLayer,n as getInclusionDefinitionFromLayer,i as removeInclusionDefinitionFromLayer};
