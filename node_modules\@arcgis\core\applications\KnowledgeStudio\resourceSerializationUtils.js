/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../request.js";import t from"../../core/Error.js";import{getOrCreateMapValue as r}from"../../core/MapUtils.js";import{convertFromGeometry as i}from"../../layers/graphics/featureConversionUtils.js";import n from"../../layers/graphics/OptimizedGeometry.js";import{ProtoOutboundRelationshipFeatureCollectionAttributionIndexes as s,ProtoInboundFeatureCollectionAttributionNames as a,ProtoOutboundEntityFeatureCollectionAttributionIndexes as o}from"../../rest/knowledgeGraph/ProtoFeatureCollection.js";import{getWasmInterface as l}from"../../rest/knowledgeGraph/wasmInterface/knowledgeWasmAccess.js";import{EsriFieldTypes as c}from"../../rest/knowledgeGraph/wasmInterface/WasmDataModelWrapperInterfaces.js";import{WasmQuantizeOriginPositionTypeCode as d,SqlTypeCode as u,IdArrayType as y}from"../../rest/knowledgeGraph/wasmInterface/WasmSerializedLayerData.js";import{wasmToProtoFeatureCollection as f}from"../../rest/knowledgeGraph/wasmInterface/wasmToFeatureFactories.js";async function p(e){const t=[],r={generateAllSublayers:!1,namedTypeDefinitions:new Map};return e.entitiesUrl&&t.push(E(e.entitiesUrl).then((e=>{I(e,r)}))),e.relationshipsUrl&&t.push(E(e.relationshipsUrl).then((e=>{I(e,r)}))),await Promise.all(t),r}async function m(e,t){t??=!1;const r={generateAllSublayers:t,namedTypeDefinitions:new Map};return await k(e).then((e=>{M(e,r)})),r}async function _(e,t,r){const i=await l(),n=new i.FeatureCollection,s=new i.FeatureCollection;try{T(n,i,e,"entities",t,r),T(s,i,e,"relationships",t,r);const a=new i.FeatureCollectionEncoder,o=new i.FeatureCollectionEncoder,l=C(n,a),c=structuredClone(l),d=C(s,o),u={entitiesFC:c,relationshipsFC:structuredClone(d)};return a.delete(),o.delete(),u}finally{n.delete(),s.delete()}}async function w(e,t,r){const i=await l(),n=new i.FeatureCollection;try{T(n,i,e,"entities",t,r);const s=new i.FeatureCollectionEncoder,a=C(n,s),o=structuredClone(a);return s.delete(),o}finally{n.delete()}}async function g(e,t,r){const i=await l(),n=new i.FeatureCollection;try{T(n,i,e,"relationships",t,r);const s=new i.FeatureCollectionEncoder,a=C(n,s),o=structuredClone(a);return s.delete(),o}finally{n.delete()}}async function h(e){const r=await l(),i=new r.MapOfObjectIdentifierSets;b(i,r,e);const n=new r.MapOfObjectIdentifierSetsEncoder;try{n.set_map_of_identifier_sets(i),n.encode();const e=n.get_encoding_result();if(0!==e.error.error_code)throw new t("knowledge-graph:layer-support-utils",e.error.error_message);const r=structuredClone(e.get_byte_buffer());return n.delete(),r}finally{i.delete()}}function T(e,t,r,n,a,l){e.version="";const y=new t.QueryResult,f=new t.FeatureResult,p="entities"===n?o:s;f.unique_id_field={name:p[p.ELEMENTUID],isSystemMaintained:!1},f.globalid_field_name=p[p.ELEMENTUID],f.geohash_field_name="",f.geometry_properties={shapeAreaFieldName:"",shapeLengthFieldName:"",units:""},f.spatial_reference={wkid:4326,latestWkid:4326,vcsWkid:0,latestVcsWkid:0,wkt:""},f.exceeded_transfer_limit=!1,f.has_z=!1,f.has_m=!1,f.transform={quantizeOriginPosition:{value:d.upperLeft},scale:{xScale:1e-9,yScale:1e-9,mScale:1e-4,zScale:1e-4},translate:{xTranslate:-400,yTranslate:-400,mTranslate:-1e5,zTranslate:-1e5}};for(const i of Object.keys(p).filter((e=>isNaN(Number(e))))){const e=new t.Field;if(e.name=i,e.alias=i,e.sql_type={value:u.sqlTypeBigInt},"entities"===n)switch(i){case p[p.ELEMENTUID]:case p[p.TYPENAME]:e.field_type={value:c.esriFieldTypeString}}else switch(i){case p[p.ELEMENTUID]:case p[p.TYPENAME]:case s[s.FROMUID]:case s[s.TOUID]:e.field_type={value:c.esriFieldTypeString}}e.domain="",f.add_field(e),e.delete()}const m=new Map;for(const i of a.dataModel.entityTypes)m.set(i.name,"entities");for(const i of a.dataModel.relationshipTypes)m.set(i.name,"relationships");for(const[o,c]of r.namedTypeDefinitions)if(n===m.get(o))for(const e of c.members.values()){const r=new t.Feature;for(const i of Object.keys(p).filter((e=>isNaN(Number(e)))))if("entities"===n)switch(i){case p[p.ELEMENTUID]:r.add_attribute(e.id,t.esriFieldType.esriFieldTypeString);break;case p[p.TYPENAME]:r.add_attribute(o,t.esriFieldType.esriFieldTypeString)}else switch(i){case p[p.ELEMENTUID]:r.add_attribute(e.id,t.esriFieldType.esriFieldTypeString);break;case s[s.FROMUID]:r.add_attribute(l.has(e.id)?l.get(e.id)[0]:"",t.esriFieldType.esriFieldTypeString);break;case s[s.TOUID]:r.add_attribute(l.has(e.id)?l.get(e.id)[1]:"",t.esriFieldType.esriFieldTypeString);break;case p[p.TYPENAME]:r.add_attribute(o,t.esriFieldType.esriFieldTypeString)}let a;if(e.linkChartLocation&&"x"in e.linkChartLocation?a=i(e.linkChartLocation):e.linkChartLocation&&(a=e.linkChartLocation),e.linkChartLocation&&a){const e=new t.FeatureCollectionGeometry;let i=!1;switch(n){case"entities":e.geometry_type=t.esriGeometryType.esriGeometryPoint,i=!0;break;case"relationships":e.geometry_type=t.esriGeometryType.esriGeometryPolyline}e.coords=new Float64Array(a.coords),e.lengths=new Uint32Array(i?[1]:a.lengths),r.set_compressed_geometry(e),e.delete()}f.add_feature(r),r.delete()}switch(n){case"entities":f.geometry_type=t.esriGeometryType.esriGeometryPoint;break;case"relationships":f.geometry_type=t.esriGeometryType.esriGeometryPolyline}return y.set_feature_result(f),e.set_query_result(y),f.delete(),y.delete(),e}function b(e,t,r){for(const[i,n]of r.namedTypeDefinitions){if(!n.members||n.useAllData)continue;const r=n.members.keys();let s=!1,a=!0;const o=new t.ObjectIdArray,l=new t.StringArray,c=new t.GlobalIdArray,d=new t.IdentifierArray,u=new t.ObjectIdentifierSet;for(const e of r)a&&(s=!isNaN(Number(e)),a=!1),s?o.add_objectid(Number(e)):(l.add_string(e),c.add_globalid(e)),d.add_identifier(e);u.set_oid_array(o),u.set_string_array(l),u.set_globalid_array(c),u.set_identifier_array(d),o.delete(),l.delete(),c.delete(),d.delete(),e.put_identifier_set(i,u),u.delete()}return e}async function E(t){const r=await e(t,{responseType:"array-buffer"});return F(await r.data)}async function F(e){const r=new((await l()).FeatureCollectionDecoder),i=r.decode(new Uint8Array(e));if(0!==i.error_code)throw new t("knowledge-graph:layer-support-utils",i.error_message);const n=r.get_feature_collection(),s=f(n);return r.delete(),s}async function k(t){const r=await e(t,{responseType:"array-buffer"}),i=await r.data;return A(new Uint8Array(i))}async function A(e){const r=new((await l()).MapOfObjectIdentifierSetsDecoder),i=r.decode(new Uint8Array(e)),n=new Map;if(0!==i.error_code)throw new t("knowledge-graph:layer-support-utils",i.error_message);const s=r.get_map_of_identifier_sets(),a=s.keys,o=a.size();for(let l=0;l<o;l++){const e=a.get(l),r=s.query_identifier_set(e),i=[];if(r.id_array_type.value===y.GLOBALID_ARRAY){const e=r.get_globalid_array(),t=e.count();for(let r=0;r<t;r++)i.push(e.get_globalid_at(r))}else if(r.id_array_type.value===y.IDENTIFIER_ARRAY){const e=r.get_identifier_array(),t=e.count();for(let r=0;r<t;r++)i.push(e.get_identifier_at(r).toString())}else if(r.id_array_type.value===y.STRING_ARRAY){const e=r.get_string_array(),t=e.count();for(let r=0;r<t;r++)i.push(e.get_string_at(r))}else{if(r.id_array_type.value!==y.OID_ARRAY)throw new t("knowledge-graph:layer-support-utils","Tried to encode an unexpected ID Array type.");{const e=r.get_oid_array(),t=e.count();for(let r=0;r<t;r++)i.push(e.get_objectid_at(r).toString())}}n.set(e,i)}return r.delete(),n}function I(e,t){if(!e?.queryResult?.featureResult)return t;const i=e.queryResult.featureResult.fieldNameToAttributeIndexMap;for(const s of e.queryResult.featureResult.features){const e=s.attributes[i[a.TYPENAME]],o=r(t.namedTypeDefinitions,e,(()=>({useAllData:!1,members:new Map}))),l=s.attributes[i[a.ELEMENTUID]];if(s.compressedGeometry?.coords?.length>0){let e=s.compressedGeometry.lengths;"esriGeometryPoint"===s.compressedGeometry.geometryType&&(e=[]),o.members.set(l,{id:l,linkChartLocation:new n(e,s.compressedGeometry.coords)})}else o.members.set(l,{id:l})}return t}function M(e,t){for(const[i,n]of e){const e=r(t.namedTypeDefinitions,i,(()=>({useAllData:!1,members:new Map})));for(const t of n)e.members.has(t)||e.members.set(t,{id:t})}return t}const C=(e,r)=>{r.set_feature_collection(e),r.encode();const i=r.get_encoding_result();if(0!==i.error.error_code)throw new t("knowledge-graph:layer-support-utils",i.error.error_message);return i.get_byte_buffer()},D={fetchAndConvertSerializedLinkChart:e=>p(e)};export{I as addFeatureCollectionToInclusionDefinition,M as addIdMapToInclusionDefinition,F as deserializeFeatureCollection,A as deserializeIdCollectionMap,m as fetchAndConvertSerializedKnowledgeIdMap,p as fetchAndConvertSerializedLinkChart,E as fetchAndDeserializeFeatureCollection,k as fetchAndDeserializeIdCollectionMap,D as serializationUtilsModuleWrapper,_ as serializeInclusionDefinitionToAllPbf,w as serializeInclusionDefinitionToEntitiesPbf,h as serializeInclusionDefinitionToIdCollectionsMapPbf,g as serializeInclusionDefinitionToRelationshipsPbf,T as setFeatureCollectionProperties};
