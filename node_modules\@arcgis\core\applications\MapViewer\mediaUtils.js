/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{createDefaultControlPointsGeoreference as e,createLocalModeControlPointsGeoreference as r}from"../../layers/support/mediaUtils.js";async function o(r,o){return e(r,o)}function t(e){return r(e)}export{o as createDefaultControlPointsGeoreference,t as createLocalModeControlPointsGeoreference};
