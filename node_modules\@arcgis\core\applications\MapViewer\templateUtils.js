/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{createFieldInfos as e}from"../../support/popupUtils.js";import t from"../../tables/AttributeTableTemplate.js";import i from"../../tables/elements/AttributeTableAttachmentElement.js";import l from"../../tables/elements/AttributeTableFieldElement.js";import s from"../../tables/elements/AttributeTableRelationshipElement.js";import r from"../../widgets/FeatureTable/support/AttachmentsColumnTemplate.js";import o from"../../widgets/FeatureTable/support/FieldColumnTemplate.js";import a from"../../widgets/FeatureTable/support/RelationshipColumnTemplate.js";import n from"../../widgets/FeatureTable/support/TableTemplate.js";import{isIFeatureTableSupportedLayerWithAttachments as d,isIFeatureTableSupportedLayerWithRelationships as p,hasTemplateForField as m}from"../../widgets/FeatureTable/support/tableUtils.js";import{createAttributeTableElements as u,createColumnTemplates as f}from"../../widgets/FeatureTable/support/templateUtils.js";function c(e,t,i=!0){const{allColumns:l,columns:s}=e,r=[],o=u(s.toArray());e.activeSortOrders.forEach((({fieldName:e,direction:t})=>{e&&t&&r.push({field:e,order:t})}));return l.filter((e=>null!=e.direction&&(!e.hidden||i))).forEach((({fieldName:e,direction:t})=>{const i=r.find((t=>t.field===e));t&&!i&&r.push({field:e,order:t})})),t.elements=o,t.orderByFields=r,t}async function b(r){const{excludeAttachments:o,excludeRelationships:a,layer:n,excludedFieldTypesOverride:m}=r,u=[],f=[];n||Promise.resolve(new t),await n.load();const c=e(n,{...m&&{ignoreFieldTypes:m},sortDisabled:!0});for(const{visible:e,fieldName:t,label:i}of c)e&&u.push(new l({fieldName:t,label:i}));return d(n)&&!0!==o&&u.push(new i),p(n)&&!0!==a&&n.relationships?.forEach((e=>{u.push(new s({relationshipId:e.id,label:e.name}))})),new t({elements:u,orderByFields:f})}async function h(t){const{excludeAttachments:i,excludeRelationships:l,layer:s,excludedFieldTypesOverride:m}=t,u=[];await s.load();return e(s,{...m&&{ignoreFieldTypes:m},sortDisabled:!0}).forEach((({fieldName:e,format:t,label:i,visible:l})=>{null!=e&&u.push(new o({fieldName:e,format:t,label:i,visible:l}))})),d(s)&&!0!==i&&u.push(new r),p(s)&&!0!==l&&s.relationships?.forEach((({id:e})=>{u.push(new a({relationshipId:e}))})),new n({columnTemplates:u})}async function T(t){const{layer:i,template:l,includeHiddenFields:s,excludedFieldTypesOverride:u}=t,c=l.orderByFields??[],b=f(l.elements,c);if(s){await i.load();e(i,{...u&&{ignoreFieldTypes:u},sortDisabled:!0}).forEach((e=>{if(!e.fieldName)return;const{fieldName:t,label:i}=e;if(!m(t,b)){const e=c.findIndex((e=>e.field&&e.field===t)),l=e>-1?c.at(e)?.order:void 0;b.push(new o({direction:l,fieldName:t,initialSortPriority:e,label:i,visible:!1}))}})),d(i)&&!b.some((e=>"attachment"===e.type))&&b.push(new r({visible:!1})),p(i)&&i.relationships?.forEach((({id:e})=>{b.some((t=>"relationship"===t.type&&t.relationshipId===e))||b.push(new a({relationshipId:e,visible:!1}))}))}return new n({columnTemplates:b})}export{b as createDefaultAttributeTableTemplateFromLayer,h as createDefaultTableTemplateFromLayer,T as createTableTemplateFromAttributeTableTemplate,c as syncAttributeTableTemplate};
