/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../core/Collection.js";import r from"../../core/Logger.js";import{isHostedAgolService as t}from"../../layers/support/arcgisLayerUrl.js";import{registerLoader as o}from"../../layers/support/schemaValidatorLoader.js";import{schemaValidationErrorName as n}from"../../support/webSceneUtils.js";import{renderSVG as i}from"../../symbols/support/svgUtils.js";import{viewingModeFromString as s}from"../../views/ViewingMode.js";import{ZoomControllerGlobal as a}from"../../views/3d/state/controllers/ZoomControllerGlobal.js";import{ZoomControllerLocal as p}from"../../views/3d/state/controllers/ZoomControllerLocal.js";import{TilingScheme as u}from"../../views/3d/terrain/TilingScheme.js";import{isSpatialReferenceSupported as c}from"../../views/support/spatialReferenceSupport.js";import{registerLoader as l}from"../../webscene/support/schemaValidatorLoader.js";function f(r,{defaultSpatialReference:t,isSpatialReferenceSupported:o,priorityLayers:n,required:i}){const s={...r.defaultsFromMapSettings};t&&(s.defaultSpatialReference=t),o&&(s.getSpatialReferenceSupport=(e,t)=>o(e,t)?r.getSpatialReferenceSupport(e,t):null),n&&(s.priorityCollection={layers:new e(n)}),i&&(s.required=i),r.defaultsFromMapSettings=s}function m(e){return e.back()}function d(e){return e.hasPendingEdits}function S(e,t){return r.getLogger("sceneViewer.appState.saveState").warn(e,t)}function g(e){return t(e)}function j(e,r,t,o){return i(e,r,t,o)}function w(e){return!e.canNotSaveAs()}function y(e,r){return c(e,s(r))}function L(e,r){return e.getCompatibleForVTL(r)}function v(e){return new u(e)}function V(e){return null==u.checkUnsupported(e)}function b(){o((()=>import("../../layers/support/schemaValidator.js"))),l((()=>import("../../webscene/support/schemaValidator.js")))}function h(e){return e&&e.name===n}function R(e){const r=e.state.cameraController;return r instanceof a||r instanceof p?r.dragBeginPoint:null}function C(e){e.openedLayers.pop()}export{w as canSaveAs,C as closeCatalogLayer,v as createTilingScheme,d as editorHasPendingEdits,m as editorNavigateBack,L as getCompatibleTileInfoForVTL,b as initializeSchemaValidators,g as isHostedAgolServiceUrl,h as isSchemaValidationError,y as isSpatialReferenceSupported,V as isSupportedTileInfo,j as renderSVG,S as saveStateWarning,f as setDefaultsFromMapSettings,R as zoomDragBeginPoint};
