/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{throwIfAborted as e}from"../../core/promiseUtils.js";async function t(t,r,o){const{convertMeshVertexSpace:s}=await import("../../geometry/support/meshUtils/convertMeshVertexSpace.js");return e(o),s(t,r,{...o,useEllipsoid:!0})}export{t as convertVertexSpaceEllipsoid};
