/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{createTask as e}from"../../core/asyncUtils.js";import{on as o}from"../../core/events.js";import{makeHandle as t}from"../../core/handleUtils.js";import{ignoreAbortErrors as i,isAborted as n,createResolver as r,onAbort as s,waitTick as a}from"../../core/promiseUtils.js";import{whenOnce as c}from"../../core/reactiveUtils.js";import{waitAnimationFrame as l}from"../../core/scheduling.js";import{createScreenPoint as p}from"../../core/screenUtils.js";import m from"../../views/interactive/sketch/SketchTooltipVisibleElements.js";async function d(o,t,m={}){async function d(e){const i=w(o),{tooltipOptions:d}=o;if("coordinates"===t?(t="x",d.xyMode="coordinates"):"direction"!==t&&"distance"!==t||(d.xyMode="direction-distance"),await l(),n(m)||n(e))return;const f=i?.tooltip;if(!f||!i.activeTooltipInfo?.editableFields?.find((e=>e.name===t)))return;const{handle:u,promise:v}=h(f,m),y=r(),b=s(m.signal,y.resolve),j=s(e,y.resolve),g=d.offset,M=f.position;try{if(d.forceEnabled=!0,await a(),m.position&&(d.offset=0,f.position=p(m.position[0],m.position[1])),await f.enterInputMode(t),n(m)||n(e))return;m.onOpen?.(),await Promise.race([c((()=>i.destroyed||"feedback"===f.content?.mode),m),v,y.promise])}finally{b?.remove(),j?.remove(),u.remove();const e=f.outerContainer.contains(document.activeElement);d.enabled?await f.exitInputMode({focusOnView:e}):e&&o.view?.focus(),m.onClose?.(),f.position=M,d.offset=g,d.forceEnabled=!1}}const u=f.get(o);u&&(u.abort(),await u.promise);const v=e(d);return f.set(o,v),v.promise.catch(i).finally((()=>{f.get(o)===v&&f.delete(o)}))}const f=new Map,u={area:!1,distance:!1,direction:!1,elevation:!1,orientation:!1,radius:!1,rotation:!1,scale:!1,size:!1,totalLength:!1,coordinates:!1,helpMessage:!1,header:!1};function v(e){e.tooltipOptions.visibleElements=new m(u)}function h(e,{hideOnBlur:i=!0}){const n=r();if(!i)return{promise:n.promise,handle:t()};const{outerContainer:s}=e,a=o(s,"focusout",(({relatedTarget:e})=>{e instanceof HTMLElement&&s.contains(e)||n.resolve()}));return{promise:n.promise,handle:a}}function w(e){const{activeComponent:o}=e;return o&&"tooltip"in o&&"activeTooltipInfo"in o&&"destroyed"in o?o:null}export{v as hideAllVisibleElements,d as showTooltipAndFocusField};
