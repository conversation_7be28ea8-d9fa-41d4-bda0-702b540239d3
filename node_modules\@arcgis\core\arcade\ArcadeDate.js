/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{replace as e}from"../core/string.js";import{UnknownTimeZone as t}from"../core/sql/UnknownTimeZone.js";import{DateTime as n,Zone as r,FixedOffsetZone as s,IANAZone as i}from"luxon";var o;!function(e){e.TimeZoneNotRecognized="TimeZoneNotRecognized"}(o||(o={}));const a={[o.TimeZoneNotRecognized]:"Timezone identifier has not been recognized."};class d extends Error{constructor(t,n){super(e(a[t],n)),this.declaredRootClass="esri.arcade.arcadedate.dateerror",Error.captureStackTrace&&Error.captureStackTrace(this,d)}}function u(e,t,n){return e<t?e-t:e>n?e-n:0}function c(e,t,n){return e<t?t:e>n?n:e}class m{constructor(e){this._date=e,this.declaredRootClass="esri.arcade.arcadedate"}static fromParts(e=0,t=1,r=1,s=0,i=0,o=0,a=0,d){if(isNaN(e)||isNaN(t)||isNaN(r)||isNaN(s)||isNaN(i)||isNaN(o)||isNaN(a))return null;const l=n.local(e,t).daysInMonth;let f=n.fromObject({day:c(r,1,l),year:e,month:c(t,1,12),hour:c(s,0,23),minute:c(i,0,59),second:c(o,0,59),millisecond:c(a,0,999)},{zone:h(d)});return f=f.plus({months:u(t,1,12),days:u(r,1,l),hours:u(s,0,23),minutes:u(i,0,59),seconds:u(o,0,59),milliseconds:u(a,0,999)}),new m(f)}static get systemTimeZoneCanonicalName(){return Intl.DateTimeFormat().resolvedOptions().timeZone??"system"}static arcadeDateAndZoneToArcadeDate(e,n){const r=h(n);return e.isUnknownTimeZone||r===t.instance?m.fromParts(e.year,e.monthJS+1,e.day,e.hour,e.minute,e.second,e.millisecond,r):new m(e._date.setZone(r))}static dateJSToArcadeDate(e){return new m(n.fromJSDate(e,{zone:"system"}))}static dateJSAndZoneToArcadeDate(e,t="system"){const r=h(t);return new m(n.fromJSDate(e,{zone:r}))}static unknownEpochToArcadeDate(e){return new m(n.fromMillis(e,{zone:t.instance}))}static unknownDateJSToArcadeDate(e){return new m(n.fromMillis(e.getTime(),{zone:t.instance}))}static epochToArcadeDate(e,t="system"){const r=h(t);return new m(n.fromMillis(e,{zone:r}))}static dateTimeToArcadeDate(e){return new m(e)}clone(){return new m(this._date)}changeTimeZone(e){const t=h(e);return m.dateTimeToArcadeDate(this._date.setZone(t))}static dateTimeAndZoneToArcadeDate(e,n){const r=h(n);return e.zone===t.instance||r===t.instance?m.fromParts(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond,r):new m(e.setZone(r))}static nowToArcadeDate(e){const t=h(e);return new m(n.fromJSDate(new Date,{zone:t}))}static nowUTCToArcadeDate(){return new m(n.utc())}get isSystem(){return"system"===this.timeZone||this.timeZone===m.systemTimeZoneCanonicalName}equals(e){return this.isSystem&&e.isSystem?this.toNumber()===e.toNumber():this.isUnknownTimeZone===e.isUnknownTimeZone&&this._date.equals(e._date)}get isUnknownTimeZone(){return this._date.zone===t.instance}get isValid(){return this._date.isValid}get hour(){return this._date.hour}get second(){return this._date.second}get day(){return this._date.day}get dayOfWeekISO(){return this._date.weekday}get dayOfWeekJS(){let e=this._date.weekday;return e>6&&(e=0),e}get millisecond(){return this._date.millisecond}get monthISO(){return this._date.month}get weekISO(){return this._date.weekNumber}get yearISO(){return this._date.weekYear}get monthJS(){return this._date.month-1}get year(){return this._date.year}get minute(){return this._date.minute}get zone(){return this._date.zone}get timeZoneOffset(){return this.isUnknownTimeZone?0:this._date.offset}get timeZone(){if(this.isUnknownTimeZone)return"unknown";if("system"===this._date.zone.type)return"system";const e=this.zone;return"fixed"===e.type?0===e.fixed?"UTC":e.formatOffset(0,"short"):e.name}stringify(){return JSON.stringify(this.toJSDate())}plus(e){return new m(this._date.plus(e))}diff(e,t="milliseconds"){return this._date.diff(e._date,t)[t]}toISODate(){return this._date.toISODate()}toISOString(e){return e?this._date.toISO({suppressMilliseconds:!0,includeOffset:!this.isUnknownTimeZone}):this._date.toISO({includeOffset:!this.isUnknownTimeZone})}toISOTime(e,t){return this._date.toISOTime({suppressMilliseconds:e,includeOffset:t&&!this.isUnknownTimeZone})}toFormat(e,t){return this.isUnknownTimeZone&&(e=e.replaceAll("Z","")),this._date.toFormat(e,t)}toJSDate(){return this._date.toJSDate()}toSQLValue(){return this._date.toFormat("yyyy-LL-dd HH:mm:ss")}toSQLWithKeyword(){return`timestamp '${this.toSQLValue()}'`}toDateTime(){return this._date}toNumber(){return this._date.toMillis()}getTime(){return this._date.toMillis()}toUTC(){return new m(this._date.toUTC())}toLocal(){return new m(this._date.toLocal())}toString(){return this.toISOString(!0)}static fromReaderAsTimeStampOffset(e){if(!e)return null;const t=n.fromISO(e,{setZone:!0});return new m(t)}}function h(e,n=!0){if(e instanceof r)return e;if("system"===e.toLowerCase())return"system";if("utc"===e.toLowerCase())return"UTC";if("unknown"===e.toLowerCase())return t.instance;if(/^[+-]?[0-9]{1,2}([:][0-9]{2})?$/.test(e)){const t=s.parseSpecifier("UTC"+(e.startsWith("+")||e.startsWith("-")?"":"+")+e);if(t)return t}const a=i.create(e);if(!a.isValid){if(n)throw new d(o.TimeZoneNotRecognized);return null}return a}export{m as ArcadeDate,d as ArcadeDateError,o as ArcadeDateErrorCodes,a as arcadeDateErrorMessages,h as createDateTimeZone};
