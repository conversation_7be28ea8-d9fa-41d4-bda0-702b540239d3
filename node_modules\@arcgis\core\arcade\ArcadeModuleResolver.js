/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../request.js";import{ModuleError as t,ModuleErrorCodes as r}from"./executionError.js";import{parseScript as o}from"./parser.js";import s from"./featureset/support/RecentlyUsedCache.js";import i from"../portal/Portal.js";class l{constructor(e){this.portalUri=e}normalizeModuleUri(e){const o=/^[a-z0-9A-Z]+(@[0-9]+\.[0-9]+\.[0-9]+)?([?|/].*)?$/gi,s=/(?<portalurl>.+)\/home\/item\.html\?id=(?<itemid>.+)$/gi,c=/(?<portalurl>.+)\/sharing\/rest\/content\/users\/[a-zA-Z0-9]+\/items\/(?<itemid>.+)$/gi,u=/(?<portalurl>.+)\/sharing\/rest\/content\/items\/(?<itemid>.+)$/gi,n=/(?<itemid>.*)@(?<versionstring>[0-9]+\.[0-9]+\.[0-9]+)([?|/].*)?$/gi;if(e.startsWith("portal+")){let l=e.slice(7),a="",m=l,d=!1;for(const e of[s,u,c]){const t=e.exec(l);if(null!==t){const e=t.groups;m=e.itemid,a=e.portalurl,d=!0;break}}if(!1===d){if(!o.test(l))throw new t(r.UnsupportedUriProtocol,{uri:e});m=l,a=this.portalUri}m.includes("/")&&(m=m.split("/")[0]),m.includes("?")&&(m=m.split("?")[0]);let h="current";const p=n.exec(m);if(null!==p){const e=p.groups;m=e.itemid,h=e.versionstring}return l=new i({url:a}).restUrl+"/content/items/"+m+"/resources/"+h+".arc",{url:l,scheme:"portal",uri:"PO:"+l}}if(e.startsWith("mock")){if("mock"===e){return{url:"",scheme:"mock",data:'\n      export var hello = 1;\n      export function helloWorld() {\n          return "Hello World " + hello;\n      }\n  ',uri:"mock"}}const t=e.replace("mock:","");if(void 0!==l.mocks[t])return{url:"",scheme:"mock",data:l.mocks[t],uri:e}}throw new t(r.UnrecognizedUri,{uri:e})}async fetchModule(e){const t=l.cachedModules.getFromCache(e.uri);if(t)return t;const r=this.fetchSource(e);l.cachedModules.addToCache(e.uri,r);let o=null;try{o=await r}catch(s){throw l.cachedModules.removeFromCache(e.uri),s}return o}async fetchSource(s){if("portal"===s.scheme){const t=await e(s.url,{responseType:"text",query:{}});if(t.data)return o(t.data,[])}if("mock"===s.scheme)return o(s.data??"",[]);throw new t(r.UnsupportedUriProtocol)}static create(e){return new l(e)}static getDefault(){return this._default??(l._default=l._moduleResolverFactory())}static set moduleResolverClass(e){this._moduleResolverFactory=e,this._default=null}}l.mocks={},l.cachedModules=new s(30),l._default=null,l._moduleResolverFactory=()=>{const e=i.getDefault();return new l(e.url)};export{l as ArcadeModuleResolver};
