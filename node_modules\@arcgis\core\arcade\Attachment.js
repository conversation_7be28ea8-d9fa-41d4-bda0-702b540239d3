/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"./Dictionary.js";class t extends e{constructor(e,t,i,s,l,d,h){super(),this.attachmentUrl=l,this.declaredClass="esri.arcade.Attachment",this.immutable=!1,this.setField("id",e),this.setField("name",t),this.setField("contenttype",i),this.setField("size",s),this.setField("exifinfo",d),this.setField("keywords",h),this.immutable=!0}deepClone(){return new t(this.field("id"),this.field("name"),this.field("contenttype"),this.field("size"),this.attachmentUrl,this.field("exifinfo")?.deepClone()??null,this.field("keywords"))}}export{t as default};
