/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as t}from"./ArcadeDate.js";import{deepClone as s}from"./deepClone.js";import{ArcadeExecutionError as e,ExecutionErrorCodes as r}from"./executionError.js";import{i,c as n,a as o,b as a,d as u,t as l,e as c,f,g as h,h as m,j as b,k as d,l as y}from"../chunks/languageUtils.js";import w from"../geometry/Geometry.js";import{isNumber as p,isBoolean as A,isString as k,isArray as v}from"../support/guards.js";function j(t,s,e=!1,r=!1){if(null==t)return null;if(p(t))return l(t);if(A(t))return c(t);if(k(t))return f(t);if(h(t))return m(t,s);if(b(t))return t;if(d(t))return t;if(v(t)){const i=[];for(const n of t)i.push(j(n,s,e,r));return i}if(r&&y(t))return t;const i=new C;i.immutable=!1;for(const n of Object.keys(t)){const o=t[n];void 0!==o&&i.setField(n,j(o,s,e,r))}return i.immutable=e,i}class C{constructor(t){this.declaredClass="esri.arcade.Dictionary",this.plain=!1,this.immutable=!0,this.attributes=t instanceof C?t.attributes:t??{}}field(t){const s=t.toLowerCase(),i=this.attributes[t];if(void 0!==i)return i;for(const e in this.attributes)if(e.toLowerCase()===s)return this.attributes[e];throw new e(null,r.FieldNotFound,null,{key:t})}setField(s,n){if(this.immutable)throw new e(null,r.Immutable,null);if(i(n))throw new e(null,r.NoFunctionInDictionary,null);const o=s.toLowerCase();n instanceof Date&&(n=t.dateJSToArcadeDate(n));if(void 0===this.attributes[s]){for(const t in this.attributes)if(t.toLowerCase()===o)return void(this.attributes[t]=n);this.attributes[s]=n}else this.attributes[s]=n}hasField(t){const s=t.toLowerCase();if(void 0!==this.attributes[t])return!0;for(const e in this.attributes)if(e.toLowerCase()===s)return!0;return!1}keys(){let t=[];for(const s in this.attributes)t.push(s);return t=t.sort(),t}castToText(t=!1){return n(this.attributes,{useNumbersForDates:t})}static convertObjectToArcadeDictionary(t,s,e=!0,r=!1){const i=new C;i.immutable=!1;for(const n in t){const o=t[n];void 0!==o&&i.setField(n.toString(),j(o,s,e,r))}return i.immutable=e,i}static convertJsonToArcade(t,s,e=!1,r=!1){return j(t,s,e,r)}castAsJson(t=null){const s={};for(let e in this.attributes){const r=this.attributes[e];void 0!==r&&(t?.keyTranslate&&(e=t.keyTranslate(e)),s[e]=o(r,t))}return s}async castDictionaryValueAsJsonAsync(t,s,e,r=null,i){const n=await a(e,r,i);return t[s]=n,n}async castAsJsonAsync(s=null,e=null){const r={},i=[];for(let n in this.attributes){const a=this.attributes[n];e?.keyTranslate&&(n=e.keyTranslate(n)),void 0!==a&&(u(a)||a instanceof w||a instanceof t?r[n]=o(a,e):i.push(this.castDictionaryValueAsJsonAsync(r,n,a,s,e)))}return i.length>0&&await Promise.all(i),r}deepClone(){const t=new C;t.immutable=!1;for(const e of this.keys())t.setField(e,s(this.field(e)));return t}}export{C as default};
