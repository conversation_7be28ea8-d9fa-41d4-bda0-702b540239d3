/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as e}from"./ArcadeDate.js";import{configureDeepClone as t}from"./deepClone.js";import i from"./Dictionary.js";import{ArcadeExecutionError as s,ExecutionErrorCodes as r}from"./executionError.js";import{p as o,c as n,s as a,k as l,j as u,g as d,d as m,a as f}from"../chunks/languageUtils.js";import{constructGeometryFromDictionary as y}from"./geometry/constructors.js";import{DateOnly as h}from"../core/sql/DateOnly.js";import{TimeOnly as c}from"../core/sql/TimeOnly.js";import p from"../geometry/Geometry.js";import{fromJSON as _}from"../geometry/support/jsonUtils.js";import{convertToGeometry as b}from"../layers/graphics/featureConversionUtils.js";import T from"../layers/support/FieldsIndex.js";import{isString as g,isNumber as F,isBoolean as x}from"../support/guards.js";class D{constructor(){this.arcadeDeclaredClass="esri.arcade.Feature",this._optimizedGeomDefinition=null,this._geometry=null,this.attributes=null,this._layer=null,this._fieldTypesFixed=!0,this.fieldsIndex=null,this.contextTimeZone=null,this.immutable=!0,this._fieldsToFixDataTypes=null,this.immutable=!0}static createFromGraphic(e,t){const i=new D;return i.contextTimeZone=t??null,i._geometry=null!=e.geometry?e.geometry:null,void 0===e.attributes||null===e.attributes?i.attributes={}:i.attributes=e.attributes,e._sourceLayer?(i._layer=e._sourceLayer,i._fieldTypesFixed=!1):e._layer?(i._layer=e._layer,i._fieldTypesFixed=!1):e.layer&&"fields"in e.layer?(i._layer=e.layer,i._fieldTypesFixed=!1):e.sourceLayer&&"fields"in e.sourceLayer&&(i._layer=e.sourceLayer,i._fieldTypesFixed=!1),i._layer&&!i._fieldTypesFixed&&(i.fieldsIndex=this.hydrateFieldsIndex(i._layer)),i}static createFromArcadeFeature(e){if(e instanceof D){const t=new D;return t._fieldTypesFixed=e._fieldTypesFixed,t.attributes=e.attributes,t._geometry=e._geometry,t._optimizedGeomDefinition=e._optimizedGeomDefinition,e._layer&&(t._layer=e._layer),t.fieldsIndex=e.fieldsIndex,t.contextTimeZone=e.contextTimeZone,t}const t={};for(const i of e.keys())t[i]=e.field(i);return D.createFromGraphicLikeObject(e.geometry(),t,e.fullSchema(),e.contextTimeZone)}static createFromOptimisedFeature(e,t,i){const s=new D;return s._geometry=e.geometry?{geometry:e.geometry}:null,s._optimizedGeomDefinition=i,s.attributes=e.attributes||{},s._layer=t,s._fieldTypesFixed=!1,s}static createFromArcadeDictionary(e,t){const s=new D;return s.attributes=e.field("attributes"),null!==s.attributes&&s.attributes instanceof i?(s.attributes=s.attributes.attributes,null===s.attributes&&(s.attributes={})):s.attributes={},s._geometry=e.field("geometry"),null!==s._geometry&&(s._geometry instanceof i?s._geometry=y(s._geometry,t):s._geometry instanceof p||(s._geometry=null)),s}static createFromGraphicLikeObject(e,t,i=null,s){const r=new D;return r.contextTimeZone=s??null,null===t&&(t={}),r.attributes=t,r._geometry=null!=e?e:null,r._layer=i,r._layer&&(r._fieldTypesFixed=!1,r.fieldsIndex=this.hydrateFieldsIndex(r._layer)),r}static hydrateFieldsIndex(e){return null===e?null:o(e)?e.getFieldsIndex():e.fieldsIndex?e.fieldsIndex:T.fromLayerJSON({datesInUnknownTimezone:e.datesInUnknownTimezone,fields:e.fields,timeInfo:e.timeInfo,editFieldsInfo:e.editFieldsInfo,dateFieldsTimeReference:e.dateFieldsTimeReference??{timeZone:"UTC",respectsDaylightSaving:!1}})}repurposeFromGraphicLikeObject(e,t,i=null){null===t&&(t={}),this.attributes=t,this._geometry=e??null,this._layer=i,this._layer?this._fieldTypesFixed=!1:this._fieldTypesFixed=!0}castToText(e=!1){!1===this._fieldTypesFixed&&this._fixFieldTypes();const t=n(this.attributes,{useNumbersForDates:e});return'{"geometry":'+(null===this.geometry()?"null":a(this.geometry()))+',"attributes":'+t+"}"}_fixFieldTypes(){if(this._fieldsToFixDataTypes&&this._fieldsToFixDataTypes?.length>0)return this._fixAllFields(this._fieldsToFixDataTypes),void(this._fieldTypesFixed=!0);const e=[],t=this._layer.fields;for(let i=0;i<t.length;i++){const s=t[i],{name:r,type:o}=s;switch(o){case"date":case"esriFieldTypeDate":e.push({field:r,dataType:"date"});break;case"date-only":case"esriFieldTypeDateOnly":e.push({field:r,dataType:"date-only"});break;case"time-only":case"esriFieldTypeTimeOnly":e.push({field:r,dataType:"time-only"});break;case"timestamp-offset":case"esriFieldTypeTimestampOffset":e.push({field:r,dataType:"timestamp-offset"})}}this._fieldsToFixDataTypes=e,e.length>0&&this._fixAllFields(e),this._fieldTypesFixed=!0}isUnknownDateTimeField(e){return"unknown"===this.fieldsIndex?.getTimeZone(e)}_fixAllFields(t){this.attributes={...this.attributes};const i=this.contextTimeZone??"system";for(let s=0;s<t.length;s++){const r=t[s].field,o=t[s].dataType;let n=this.attributes[r];if(void 0===n){for(const t in this.attributes)if(t.toLowerCase()===r.toLowerCase()){if(n=this.attributes[t],null!==n){if("time-only"===o){l(n)||(this.attributes[t]=c.fromReader(n.toString()));break}if("date-only"===o){u(n)||(this.attributes[t]=h.fromReader(n.toString()));break}if("timestamp-offset"===o){d(n)||(this.attributes[t]=e.fromReaderAsTimeStampOffset(n.toString()));break}const s=this.isUnknownDateTimeField(t);n instanceof Date?this.attributes[t]=s?e.unknownDateJSToArcadeDate(n):e.dateJSAndZoneToArcadeDate(n,i):d(n)||(this.attributes[t]=s?e.unknownEpochToArcadeDate(n):e.epochToArcadeDate(n,i))}break}}else if(null!==n){if("time-only"===o){l(n)?this.attributes[r]=n:this.attributes[r]=c.fromReader(n.toString());continue}if("date-only"===o){u(n)?this.attributes[r]=n:this.attributes[r]=h.fromReader(n.toString());continue}if("timestamp-offset"===o){d(n)?this.attributes[r]=n:this.attributes[r]=e.fromReaderAsTimeStampOffset(n.toString());continue}const t=this.isUnknownDateTimeField(r);d(n)?this.attributes[r]=n:n instanceof Date?this.attributes[r]=t?e.unknownDateJSToArcadeDate(n):e.dateJSAndZoneToArcadeDate(n,i):this.attributes[r]=t?e.unknownEpochToArcadeDate(n):e.epochToArcadeDate(n,i)}}}geometry(){return null===this._geometry||this._geometry instanceof p||(this._optimizedGeomDefinition?(this._geometry=_(b(this._geometry,this._optimizedGeomDefinition.geometryType,this._optimizedGeomDefinition.hasZ,this._optimizedGeomDefinition.hasM)),this._geometry.spatialReference=this._optimizedGeomDefinition.spatialReference):this._geometry=_(this._geometry)),this._geometry}field(e){this._fieldTypesFixed||this._fixFieldTypes();const t=this.attributes[e];if(void 0!==t)return t;const i=e.toLowerCase();for(const s in this.attributes)if(s.toLowerCase()===i)return this.attributes[s];if(this._hasFieldDefinition(i))return null;throw new s(null,r.FieldNotFound,null,{key:e})}_hasFieldDefinition(e){if(null===this._layer)return!1;for(let t=0;t<this._layer.fields.length;t++){if(this._layer.fields[t].name.toLowerCase()===e)return!0}return!1}setField(t,i){if(this.immutable)throw new s(null,r.Immutable,null);if(i instanceof Date&&(i=this.isUnknownDateTimeField(t)?e.unknownDateJSToArcadeDate(i):e.dateJSToArcadeDate(i)),!1===m(i))throw new s(null,r.TypeNotAllowedInFeature,null);const o=t.toLowerCase();if(void 0===this.attributes[t]){for(const e in this.attributes)if(e.toLowerCase()===o)return void(this.attributes[e]=i);this.attributes[t]=i}else this.attributes[t]=i}hasField(e){const t=e.toLowerCase();if(void 0!==this.attributes[e])return!0;for(const i in this.attributes)if(i.toLowerCase()===t)return!0;return!!this._hasFieldDefinition(t)}keys(){let e=[];const t={};for(const i in this.attributes)e.push(i),t[i.toLowerCase()]=1;if(null!==this._layer)for(let i=0;i<this._layer.fields.length;i++){const s=this._layer.fields[i];1!==t[s.name.toLowerCase()]&&e.push(s.name)}return e=e.sort(),e}static parseAttributesFromDictionary(e){const t={};for(const i in e.attributes){const o=e.attributes[i];if(!m(o))throw new s(null,r.InvalidParameter,null);t[i]=o}return t}static fromJson(e,t){let i=null;null!==e.geometry&&void 0!==e.geometry&&(i=_(e.geometry));const o={};if(null!==e.attributes&&void 0!==e.attributes)for(const n in e.attributes){const t=e.attributes[n];if(null===t)o[n]=t;else{if(!(g(t)||F(t)||x(t)||d(t)||l(t)||u(t)))throw new s(null,r.InvalidParameter,null);o[n]=t}}return D.createFromGraphicLikeObject(i,o,null,t??null)}fullSchema(){return this._layer}gdbVersion(){if(null===this._layer)return"";const e=this._layer.gdbVersion;return void 0===e?"":""===e&&this._layer.capabilities?.isVersioned?"SDE.DEFAULT":e}castAsJson(e){const t={attributes:{},geometry:!0===e?.keepGeometryType?this.geometry():this.geometry()?.toJSON()??null};for(const i in this.attributes){const s=this.attributes[i];void 0!==s&&(t.attributes[i]=f(s,e))}return t}async castAsJsonAsync(e=null,t){return this.castAsJson(t)}}t(D);export{D as default};
