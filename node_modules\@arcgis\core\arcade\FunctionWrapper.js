/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{isPromiseLike as t}from"../core/promiseUtils.js";class r{constructor(){}}function n(t,n,e){if(t instanceof r&&!(t instanceof s)){const r=new s;return r.fn=t,r.parameterEvaluator=e,r.context=n,r}return t}class e extends r{constructor(t){super(),this.fn=t}createFunction(t){return(...r)=>this.fn(t,{preparsed:!0,arguments:r})}call(t,r){return this.fn(t,r)}marshalledCall(e,a,l,c){return c(e,a,((a,o,i)=>{i=i.map((t=>t instanceof r&&!(t instanceof s)?n(t,e,c):t));const u=this.call(l,{args:i});return t(u)?u.then((t=>n(t,l,c))):u}))}}class s extends r{constructor(){super(...arguments),this.fn=null,this.context=null}createFunction(t){return this.fn.createFunction(this.context)}call(t,r){return this.fn.marshalledCall(t,r,this.context,this.parameterEvaluator)}marshalledCall(t,r,n){return this.fn.marshalledCall(t,r,this.context,this.parameterEvaluator)}}export{r as ArcadeFunction,e as NativeFunction,s as ScopeMarshalledFunction,n as wrapModuleScopedResponse};
