/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import t from"./ImmutableArray.js";import s from"../geometry/Point.js";class i extends t{constructor(t,s,i,h,e,a){super(t),this._lazyPt=[],this._hasZ=!1,this._hasM=!1,this._spRef=s,this._hasZ=i,this._hasM=h,this._cacheId=e,this._partId=a}get(t){if(void 0===this._lazyPt[t]){const i=this._elements[t];if(void 0===i)return;const h=new s(i[0],i[1],this._spRef);this._hasZ&&(h.z=i[2]),this._hasM&&(h.m=this._hasZ?i[3]:i[2]),h.cache._arcadeCacheId=this._cacheId.toString()+"-"+this._partId.toString()+"-"+t.toString(),this._lazyPt[t]=h}return this._lazyPt[t]}equalityTest(t){return t===this||null!==t&&(t instanceof i!=!1&&t.getUniqueHash()===this.getUniqueHash())}getUniqueHash(){return this._cacheId.toString()+"-"+this._partId.toString()}}export{i as default};
