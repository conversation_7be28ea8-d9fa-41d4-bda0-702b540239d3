/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as t}from"./ArcadeDate.js";import{ArcadeExecutionError as e,ExecutionErrorCodes as i}from"./executionError.js";import r from"./ImmutableArray.js";import{c as s}from"../chunks/languageUtils.js";import{isDate as o}from"../support/guards.js";const n="Voxel.Position",a="Voxel.LocalTime";function l(t){const s=t.getAttribute(n);if("string"!=typeof s)throw new e(null,i.InvalidParameter,null);const o=JSON.parse(s);if(!Array.isArray(o)||"number"!=typeof o[0]||"number"!=typeof o[1]||"number"!=typeof o[2])throw new e(null,i.InvalidParameter,null);return new r(o)}function u(r,s){const n=r.getAttribute(a);if(null==n)return null;if(!o(n))throw new e(null,i.InvalidParameter,null);return t.dateJSAndZoneToArcadeDate(n,s)}class c{constructor(t,e){this._graphic=t,this._timeZone=e,this.arcadeDeclaredClass="esri.arcade.Voxel",this._layer=t.layer}getPosition(){return void 0!==this._position?this._position:this._position=l(this._graphic)}getLocalTime(){return void 0!==this._localTime?this._localTime:this._localTime=u(this._graphic,this._timeZone??"system")}keys(){return this._layer.fields.map((t=>t.name)).sort()}hasField(t){return this._layer.fieldsIndex.has(t)}field(t){const r=this._layer.fieldsIndex?.get(t)?.name;if(null==r)throw new e(null,i.FieldNotFound,null,{key:t});switch(r){case n:return this.getPosition();case a:return this.getLocalTime()}return this._graphic.attributes[r]??null}castToText(t=!1){const e={...this._graphic.attributes};e[n]=this.getPosition(),a in e&&(e[a]=this.getLocalTime());for(const i of Object.keys(e))this._layer.fieldsIndex?.has(i)||delete e[i];return s(e,{useNumbersForDates:t})}}export{c as Voxel};
