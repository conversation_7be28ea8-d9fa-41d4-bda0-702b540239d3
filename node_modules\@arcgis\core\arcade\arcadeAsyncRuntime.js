/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{toSymbolId as e}from"./arcadeEnvironment.js";import{ArcadeModule as t}from"./ArcadeModule.js";import{ArcadeModuleLoader as n}from"./ArcadeModuleLoader.js";import{getNestedOptionalValue as r,geometryMember as o,getGeometryKeys as a}from"./containerUtils.js";import i from"./Dictionary.js";import{ArcadeExecutionError as l,ExecutionErrorCodes as s,ensureArcadeExecutionError as c}from"./executionError.js";import u from"./Feature.js";import{NativeFunction as f,ScopeMarshalledFunction as p,ArcadeFunction as w,wrapModuleScopedResponse as d}from"./FunctionWrapper.js";import{D as h,B as g,i as m,w as y,t as b,n as v,m as S,z as x,f as F,A as I,C as k,R as M,I as R,x as A,y as C,u as O,p as N,l as j}from"../chunks/languageUtils.js";import{addFunctionDeclaration as B}from"./treeAnalysis.js";import{A as E}from"../chunks/array.js";import{registerFunctions as D}from"./functions/date.js";import{registerFunctions as K}from"./functions/feature.js";import{registerFunctions as U}from"./functions/geomasync.js";import{registerFunctions as Z}from"./functions/geometry.js";import{registerFunctions as G}from"./functions/maths.js";import{registerFunctions as L}from"./functions/stats.js";import{registerFunctions as q}from"./functions/string.js";import P from"../geometry/Geometry.js";import V from"../geometry/SpatialReference.js";import{isBoolean as W,isString as T,isArray as _,isNumber as z,isGraphic as Y}from"../support/guards.js";async function H(e,t){const n=[];for(let r=0;r<t.arguments.length;r++)n.push(await ee(e,t.arguments[r]));return n}async function J(e,t,n){if(!0===t.preparsed)return n(e,null,t.arguments);return n(e,t,await H(e,t))}class Q extends w{constructor(e,t){super(),this.definition=null,this.context=null,this.definition=e,this.context=t}createFunction(e){return(...t)=>{const n={spatialReference:this.context.spatialReference,console:this.context.console,lrucache:this.context.lrucache,timeZone:this.context.timeZone??null,exports:this.context.exports,libraryResolver:this.context.libraryResolver,interceptor:this.context.interceptor,localScope:{},depthCounter:{depth:e.depthCounter+1},globalScope:this.context.globalScope};if(n.depthCounter.depth>64)throw new l(e,s.MaximumCallDepth,null);return Ke(this.definition,n,t,null)}}call(e,t){return $(e,t,((n,r,o)=>{const a={spatialReference:e.spatialReference,services:e.services,console:e.console,libraryResolver:e.libraryResolver,exports:e.exports,lrucache:e.lrucache,timeZone:e.timeZone??null,interceptor:e.interceptor,localScope:{},abortSignal:e.abortSignal,globalScope:e.globalScope,depthCounter:{depth:e.depthCounter.depth+1}};if(a.depthCounter.depth>64)throw new l(e,s.MaximumCallDepth,t);return Ke(this.definition,a,o,t)}))}marshalledCall(e,t,n,r){return r(e,t,(async(o,a,i)=>{const l={spatialReference:e.spatialReference,globalScope:n.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,console:e.console,abortSignal:e.abortSignal,lrucache:e.lrucache,timeZone:e.timeZone??null,interceptor:e.interceptor,localScope:{}};return i=i.map((t=>!m(t)||t instanceof p?t:d(t,e,r))),d(await Ke(this.definition,l,i,t),n,r)}))}}class X extends t{constructor(e){super(),this.source=e}global(t){const n=this.executingContext.globalScope[e(t)];if(m(n.value)&&!(n.value instanceof p)){const e=new p;e.fn=n.value,e.parameterEvaluator=$,e.context=this.executingContext,n.value=e}return n.value}setGlobal(t,n){if(m(n))throw new l(null,s.AssignModuleFunction,null);this.executingContext.globalScope[e(t)]={value:n}}hasGlobal(t){return void 0===this.executingContext.exports[t]&&(t=e(t)),void 0!==this.executingContext.exports[t]}async loadModule(e){let t=e.spatialReference;null==t&&(t=new V({wkid:102100})),this.moduleScope=Ge({},e.customfunctions,e.timeZone),this.executingContext={spatialReference:t,services:e.services,libraryResolver:new n(e.libraryResolver._moduleSingletons,this.source.syntax.loadedModules),exports:{},abortSignal:void 0===e.abortSignal||null===e.abortSignal?{aborted:!1}:e.abortSignal,globalScope:this.moduleScope,console:e.console??Le,lrucache:e.lrucache,timeZone:e.timeZone??null,interceptor:e.interceptor,localScope:null,depthCounter:{depth:1}},await me(this.executingContext,this.source.syntax)}}async function $(e,t,n){if(!0===t.preparsed)return n(e,null,t.arguments);return n(e,t,await H(e,t))}async function ee(e,t){t.breakpoint&&await t.breakpoint();try{switch(t.type){case"UpdateExpression":return await pe(e,t);case"AssignmentExpression":return await de(e,t);case"TemplateLiteral":return await je(e,t);case"Identifier":return Oe(e,t);case"MemberExpression":return await ke(e,t);case"Literal":return t.value;case"CallExpression":return await Ne(e,t);case"UnaryExpression":return await Me(e,t);case"BinaryExpression":return await Ae(e,t);case"LogicalExpression":return await Ce(e,t);case"ArrayExpression":return await Re(e,t);case"ObjectExpression":return await ne(e,t);default:throw new l(e,s.Unrecognized,t)}}catch(n){throw c(e,t,n)}}async function te(e,t){t.breakpoint&&await t.breakpoint();try{switch(t.type){case"ImportDeclaration":return await Se(e,t);case"ExportNamedDeclaration":return await xe(e,t);case"VariableDeclaration":return await Fe(e,t,0);case"BlockStatement":return await me(e,t);case"FunctionDeclaration":return await ve(e,t);case"ReturnStatement":return await be(e,t);case"IfStatement":return await ge(e,t);case"ExpressionStatement":return await he(e,t);case"ForStatement":return await oe(e,t);case"WhileStatement":return await re(e,t);case"ForInStatement":return await ue(e,t);case"ForOfStatement":return await fe(e,t);case"BreakStatement":return A;case"EmptyStatement":return y;case"ContinueStatement":return C;default:throw new l(e,s.Unrecognized,t)}}catch(n){throw c(e,t,n)}}async function ne(e,t){const n=[];for(let i=0;i<t.properties.length;i++){const r=t.properties[i],o=await ee(e,r.value),a="Identifier"===r.key.type?r.key.name:await ee(e,r.key);n[i]={key:a,value:o}}const r={},o=new Map;for(let i=0;i<n.length;i++){const a=n[i];if(m(a.value))throw new l(e,s.NoFunctionInDictionary,t);if(!1===T(a.key))throw new l(e,s.KeyMustBeString,t);let c=a.key.toString();const u=c.toLowerCase();o.has(u)?c=o.get(u):o.set(u,c),a.value===y?r[c]=null:r[c]=a.value}const a=new i(r);return a.immutable=!1,a}async function re(e,t){let n=await ee(e,t.test);if(!1===n)return y;if(!0!==n)throw new l(e,s.BooleanConditionRequired,t);for(;!0===n;){const r=await te(e,t.body);if(r===A)break;if(r instanceof M)return r;if(n=await ee(e,t.test),!0!==n&&!1!==n)throw new l(e,s.BooleanConditionRequired,t)}return y}async function oe(e,t){try{for(null!==t.init&&("VariableDeclaration"===t.init.type?await te(e,t.init):await ee(e,t.init));;){if(null!==t.test){const n=await ee(e,t.test);if(!0===e.abortSignal?.aborted)throw new l(e,s.Cancelled,t);if(!1===n)break;if(!0!==n)throw new l(e,s.BooleanConditionRequired,t)}const n=await te(e,t.body);if(n===A)break;if(n instanceof M)return n;null!==t.update&&await ee(e,t.update)}return y}catch(n){throw n}}async function ae(e,t,n,r,o="i"){const a=n.length;for(let i=0;i<a;i++){if("k"===o){if(i>=n.length)throw new l(e,s.OutOfBounds,t);r.value=n[i]}else r.value=i;const a=await te(e,t.body);if(a===A)break;if(a instanceof M)return a}return y}async function ie(e,t,n,r,o="i"){const a=n.length();for(let i=0;i<a;i++){r.value="k"===o?n.get(i):i;const a=await te(e,t.body);if(a===A)break;if(a instanceof M)return a}return y}async function le(e,t,n,r){const o=n.iterator(e.abortSignal);let a;for(;null!=(a=await o.next());){const o=u.createFromGraphicLikeObject(a.geometry,a.attributes,n,e.timeZone);o._underlyingGraphic=a,r.value=o;const i=await te(e,t.body);if(i===A)break;if(i instanceof M)return i}return y}async function se(e,t,n,r){for(const o of n.keys()){const a=n.field(o);r.value=new i({key:o,value:a});const l=await te(e,t.body);if(l===A)break;if(l instanceof M)return l}return y}async function ce(e,t,n,r){for(const l of a(n)){const a=o(n,l,e,t,1);r.value=new i({key:l,value:a});const s=await te(e,t.body);if(s===A)break;if(s instanceof M)return s}return y}async function ue(t,n){const r=await ee(t,n.right);"VariableDeclaration"===n.left.type&&await te(t,n.left);const o=e("VariableDeclaration"===n.left.type?n.left.declarations[0].id:n.left);let c=null;if(null!=t.localScope&&void 0!==t.localScope[o]&&(c=t.localScope[o]),null===c&&void 0!==t.globalScope[o]&&(c=t.globalScope[o]),null===c)throw new l(t,s.InvalidIdentifier,n);return _(r)||T(r)?await ae(t,n,r,c):S(r)?await ie(t,n,r,c):r instanceof i||x(r)?await ae(t,n,r.keys(),c,"k"):N(r)?await le(t,n,r,c):j(r)?await ae(t,n,a(r),c,"k"):y}async function fe(t,n){const r=await ee(t,n.right);"VariableDeclaration"===n.left.type&&await te(t,n.left);const o=e("VariableDeclaration"===n.left.type?n.left.declarations[0].id:n.left);let a=null;if(null!=t.localScope&&void 0!==t.localScope[o]&&(a=t.localScope[o]),null===a&&void 0!==t.globalScope[o]&&(a=t.globalScope[o]),null===a)throw new l(t,s.InvalidIdentifier,n);return _(r)||T(r)?await ae(t,n,r,a,"k"):S(r)?await ie(t,n,r,a,"k"):r instanceof i||x(r)?await se(t,n,r,a):N(r)?await le(t,n,r,a):j(r)?await ce(t,n,r,a):y}async function pe(t,n){const r=n.argument;if("CallExpression"===r.type)throw new l(t,s.NeverReach,n);if("MemberExpression"===r.type){const e=await ee(t,r.object);let o,a;if(!0===r.computed)o=await ee(t,r.property);else{if("Identifier"!==r.property.type)throw new l(t,s.Unrecognized,n);o=r.property.name}if(_(e)){if(!z(o))throw new l(t,s.ArrayAccessorMustBeNumber,n);if(o<0&&(o=e.length+o),o<0||o>=e.length)throw new l(t,s.OutOfBounds,n);a=b(e[o]),e[o]="++"===n.operator?a+1:a-1}else if(e instanceof i){if(!1===T(o))throw new l(t,s.KeyAccessorMustBeString,n);if(!0!==e.hasField(o))throw new l(t,s.FieldNotFound,n,{key:o});a=b(e.field(o)),e.setField(o,"++"===n.operator?a+1:a-1)}else if(e instanceof X){if(!1===T(o))throw new l(t,s.ModuleAccessorMustBeString,n);if(!0!==e.hasGlobal(o))throw new l(t,s.ModuleExportNotFound,n);a=b(e.global(o)),e.setGlobal(o,"++"===n.operator?a+1:a-1)}else{if(!v(e))throw S(e)?new l(t,s.Immutable,n):new l(t,s.InvalidParameter,n);if(!1===T(o))throw new l(t,s.KeyAccessorMustBeString,n);if(!0!==e.hasField(o))throw new l(t,s.FieldNotFound,n,{key:o});a=b(e.field(o)),e.setField(o,"++"===n.operator?a+1:a-1)}return!1===n.prefix?a:"++"===n.operator?a+1:a-1}const o=e(r);let a;if(null!=t.localScope&&void 0!==t.localScope[o])return a=b(t.localScope[o].value),t.localScope[o]={value:"++"===n.operator?a+1:a-1},!1===n.prefix?a:"++"===n.operator?a+1:a-1;if(void 0!==t.globalScope[o])return a=b(t.globalScope[o].value),t.globalScope[o]={value:"++"===n.operator?a+1:a-1},!1===n.prefix?a:"++"===n.operator?a+1:a-1;throw new l(t,s.InvalidIdentifier,n)}function we(e,t,n,r,o){switch(t){case"=":return e===y?null:e;case"/=":return b(n)/b(e);case"*=":return b(n)*b(e);case"-=":return b(n)-b(e);case"+=":return T(n)||T(e)?F(n)+F(e):b(n)+b(e);case"%=":return b(n)%b(e);default:throw new l(o,s.UnsupportedOperator,r)}}async function de(t,n){const r=n.left;if("MemberExpression"===r.type){const e=await ee(t,r.object);let o;if(!0===r.computed)o=await ee(t,r.property);else{if("Identifier"!==r.property.type)throw new l(t,s.InvalidIdentifier,n);o=r.property.name}const a=await ee(t,n.right);if(_(e)){if(!z(o))throw new l(t,s.ArrayAccessorMustBeNumber,n);if(o<0&&(o=e.length+o),o<0||o>e.length)throw new l(t,s.OutOfBounds,n);if(o===e.length){if("="!==n.operator)throw new l(t,s.OutOfBounds,n);e[o]=we(a,n.operator,e[o],n,t)}else e[o]=we(a,n.operator,e[o],n,t)}else if(e instanceof i){if(!1===T(o))throw new l(t,s.KeyAccessorMustBeString,n);if(!0===e.hasField(o))e.setField(o,we(a,n.operator,e.field(o),n,t));else{if("="!==n.operator)throw new l(t,s.FieldNotFound,n,{key:o});e.setField(o,we(a,n.operator,null,n,t))}}else if(e instanceof X){if(!1===T(o))throw new l(t,s.KeyAccessorMustBeString,n);if(!0!==e.hasGlobal(o))throw new l(t,s.ModuleExportNotFound,n);e.setGlobal(o,we(a,n.operator,e.global(o),n,t))}else{if(!v(e))throw S(e)?new l(t,s.Immutable,n):new l(t,s.InvalidParameter,n);if(!1===T(o))throw new l(t,s.KeyAccessorMustBeString,n);if(!0===e.hasField(o))e.setField(o,we(a,n.operator,e.field(o),n,t));else{if("="!==n.operator)throw new l(t,s.FieldNotFound,n,{key:o});e.setField(o,we(a,n.operator,null,n,t))}}return y}const o=e(r);if(null!=t.localScope&&void 0!==t.localScope[o]){const e=await ee(t,n.right);return t.localScope[o]={value:we(e,n.operator,t.localScope[o].value,n,t)},y}if(void 0!==t.globalScope[o]){const e=await ee(t,n.right);return t.globalScope[o]={value:we(e,n.operator,t.globalScope[o].value,n,t)},y}throw new l(t,s.InvalidIdentifier,n)}async function he(e,t){const n=await ee(e,t.expression);return n===y?y:new R(n)}async function ge(e,t){const n=await ee(e,t.test);if(!0===n)return te(e,t.consequent);if(!1===n)return null!==t.alternate?te(e,t.alternate):y;throw new l(e,s.BooleanConditionRequired,t)}async function me(e,t){return ye(e,t,0)}async function ye(e,t,n){if(n>=t.body.length)return y;const r=await te(e,t.body[n]);return r instanceof M||r===A||r===C||n===t.body.length-1?r:ye(e,t,n+1)}async function be(e,t){if(null===t.argument)return new M(y);const n=await ee(e,t.argument);return new M(n)}async function ve(t,n){const r=e(n.id);return t.globalScope[r]={value:new Q(n,t)},y}async function Se(t,n){const r=e(n.specifiers[0].local),o=t.libraryResolver.loadLibrary(r);let a;return t.libraryResolver._moduleSingletons?.has(o.uri)?a=t.libraryResolver._moduleSingletons.get(o.uri):(a=new X(o),await a.loadModule(t),t.libraryResolver._moduleSingletons?.set(o.uri,a)),t.globalScope[r]={value:a},y}async function xe(t,n){if(await te(t,n.declaration),"FunctionDeclaration"===n.declaration.type)t.exports[e(n.declaration.id)]="function";else if("VariableDeclaration"===n.declaration.type)for(const r of n.declaration.declarations)t.exports[e(r.id)]="variable";return y}async function Fe(e,t,n){return n>=t.declarations.length?y:(await Ie(e,t.declarations[n]),n===t.declarations.length-1||await Fe(e,t,n+1),y)}async function Ie(t,n){let r=null;if(r=null===n.init?null:await ee(t,n.init),null!==t.localScope){if(r===y&&(r=null),"Identifier"!==n.id.type)throw new l(t,s.InvalidIdentifier,n);const o=e(n.id);return void(null!=t.localScope&&(t.localScope[o]={value:r}))}if("Identifier"!==n.id.type)throw new l(t,s.InvalidIdentifier,n);const o=e(n.id);r===y&&(r=null),t.globalScope[o]={value:r}}async function ke(e,t){const n=await ee(e,t.object);if(null===n)throw new l(e,s.MemberOfNull,t);if(!1===t.computed){if("Identifier"===t.property.type){if(n instanceof i||x(n))return n.field(t.property.name);if(n instanceof P)return o(n,t.property.name,e,t);if(n instanceof X){if(!n.hasGlobal(t.property.name))throw new l(e,s.InvalidIdentifier,t);return n.global(t.property.name)}throw new l(e,s.InvalidMemberAccessKey,t)}throw new l(e,s.InvalidMemberAccessKey,t)}let r=await ee(e,t.property);if(n instanceof i||x(n)){if(T(r))return n.field(r);throw new l(e,s.InvalidMemberAccessKey,t)}if(n instanceof X){if(T(r))return n.global(r);throw new l(e,s.InvalidMemberAccessKey,t)}if(n instanceof P){if(T(r))return o(n,r,e,t);throw new l(e,s.InvalidMemberAccessKey,t)}if(_(n)){if(z(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new l(e,s.OutOfBounds,t);return n[r]}throw new l(e,s.InvalidMemberAccessKey,t)}if(S(n)){if(z(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length()+r),r>=n.length()||r<0)throw new l(e,s.OutOfBounds,t);return n.get(r)}throw new l(e,s.InvalidMemberAccessKey,t)}if(T(n)){if(z(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new l(e,s.OutOfBounds,t);return n[r]}throw new l(e,s.InvalidMemberAccessKey,t)}throw new l(e,s.InvalidMemberAccessKey,t)}async function Me(e,t){const n=await ee(e,t.argument);if(W(n)){if("!"===t.operator)return!n;if("-"===t.operator)return-1*b(n);if("+"===t.operator)return 1*b(n);if("~"===t.operator)return~b(n);throw new l(e,s.UnsupportedUnaryOperator,t)}if("-"===t.operator)return-1*b(n);if("+"===t.operator)return 1*b(n);if("~"===t.operator)return~b(n);throw new l(e,s.UnsupportedUnaryOperator,t)}async function Re(e,t){const n=[];for(let r=0;r<t.elements.length;r++)n.push(await ee(e,t.elements[r]));for(let r=0;r<n.length;r++){if(m(n[r]))throw new l(e,s.NoFunctionInArray,t);n[r]===y&&(n[r]=null)}return n}async function Ae(e,t){const n=await ee(e,t.left),r=await ee(e,t.right);switch(t.operator){case"|":case"<<":case">>":case">>>":case"^":case"&":return k(b(n),b(r),t.operator);case"==":return g(n,r);case"!=":return!g(n,r);case"<":case">":case"<=":case">=":return I(n,r,t.operator);case"+":return T(n)||T(r)?F(n)+F(r):b(n)+b(r);case"-":return b(n)-b(r);case"*":return b(n)*b(r);case"/":return b(n)/b(r);case"%":return b(n)%b(r);default:throw new l(e,s.UnsupportedOperator,t)}}async function Ce(e,t){const n=await ee(e,t.left);if(!W(n))throw new l(e,s.LogicalExpressionOnlyBoolean,t);switch(t.operator){case"||":{if(!0===n)return n;const r=await ee(e,t.right);if(W(r))return r;throw new l(e,s.LogicExpressionOrAnd,t)}case"&&":{if(!1===n)return n;const r=await ee(e,t.right);if(W(r))return r;throw new l(e,s.LogicExpressionOrAnd,t)}default:throw new l(e,s.LogicExpressionOrAnd,t)}}function Oe(t,n){const r=e(n);if(null!=t.localScope&&void 0!==t.localScope[r])return t.localScope[r].value;if(void 0!==t.globalScope[r])return t.globalScope[r].value;throw new l(t,s.InvalidIdentifier,n)}async function Ne(t,n){if("MemberExpression"===n.callee.type){const e=await ee(t,n.callee.object);if(!(e instanceof X))throw new l(t,s.FunctionNotFound,n);const r=!1===n.callee.computed?n.callee.property.name:await ee(t,n.callee.property);if(!e.hasGlobal(r))throw new l(t,s.FunctionNotFound,n);const o=e.global(r);if(!m(o))throw new l(t,s.CallNonFunction,n);return o.call(t,n)}if("Identifier"!==n.callee.type)throw new l(t,s.FunctionNotFound,n);const r=e(n.callee);if(null!=t.localScope&&void 0!==t.localScope[r]){const e=t.localScope[r];if(m(e.value))return e.value.call(t,n);throw new l(t,s.CallNonFunction,n)}if(void 0!==t.globalScope[r]){const e=t.globalScope[r];if(m(e.value))return e.value.call(t,n);throw new l(t,s.CallNonFunction,n)}throw new l(t,s.FunctionNotFound,n)}async function je(e,t){let n="",r=0;for(const o of t.quasis)if(n+=o.value?o.value.cooked:"",!1===o.tail){if(t.expressions[r]){const o=await ee(e,t.expressions[r]);if(m(o))throw new l(e,s.NoFunctionInTemplateLiteral,t);n+=F(o)}r++}return n}const Be={};async function Ee(e,t,n,r){const o=await ee(e,t.arguments[n]);if(g(o,r))return ee(e,t.arguments[n+1]);const a=t.arguments.length-n;return 1===a?ee(e,t.arguments[n]):2===a?null:3===a?ee(e,t.arguments[n+2]):Ee(e,t,n+2,r)}async function De(e,t,n,r){if(!0===r)return ee(e,t.arguments[n+1]);if(3===t.arguments.length-n)return ee(e,t.arguments[n+2]);const o=await ee(e,t.arguments[n+2]);if(!1===W(o))throw new l(e,s.ModuleExportNotFound,t.arguments[n+2]);return De(e,t,n+2,o)}async function Ke(t,n,r,o){const a=t.body;if(r.length!==t.params.length)throw new l(n,s.WrongNumberOfParameters,null);for(let l=0;l<r.length;l++){const o=t.params[l];"Identifier"===o.type&&null!=n.localScope&&(n.localScope[e(o)]={value:r[l]})}const i=await te(n,a);if(i instanceof M)return i.value;if(i===A)throw new l(n,s.UnexpectedToken,o);if(i===C)throw new l(n,s.UnexpectedToken,o);return i instanceof R?i.value:i}D(Be,J),q(Be,J),K(Be,J,Oe),G(Be,J),Z(Be,J),L(Be,J),U({functions:Be,compiled:!1,signatures:null,evaluateIdentifier:null,mode:"async",standardFunction:J,standardFunctionAsync:$}),Be.iif=async function(e,t){h(null===t.arguments?[]:t.arguments,3,3,e,t);const n=await ee(e,t.arguments[0]);if(!1===W(n))throw new l(e,s.BooleanConditionRequired,t);return ee(e,n?t.arguments[1]:t.arguments[2])},Be.defaultvalue=async function(e,t){h(null===t.arguments?[]:t.arguments,2,3,e,t);const n=await ee(e,t.arguments[0]);if(3===t.arguments.length){const o=await ee(e,t.arguments[1]),a=r(n,o);return null!=a&&""!==a?a:ee(e,t.arguments[2])}return null==n||""===n?ee(e,t.arguments[1]):n},Be.decode=async function(e,t){if(t.arguments.length<2)throw new l(e,s.WrongNumberOfParameters,t);if(2===t.arguments.length)return ee(e,t.arguments[1]);if((t.arguments.length-1)%2==0)throw new l(e,s.WrongNumberOfParameters,t);return Ee(e,t,1,await ee(e,t.arguments[0]))},Be.when=async function(e,t){if(t.arguments.length<3)throw new l(e,s.WrongNumberOfParameters,t);if(t.arguments.length%2==0)throw new l(e,s.WrongNumberOfParameters,t);const n=await ee(e,t.arguments[0]);if(!1===W(n))throw new l(e,s.BooleanConditionRequired,t.arguments[0]);return De(e,t,0,n)};const Ue={fixSpatialReference:O,parseArguments:H,standardFunction:J,standardFunctionAsync:$,evaluateIdentifier:Oe};for(const We in Be)Be[We]={value:new f(Be[We])};const Ze=function(){};function Ge(e,t,n){const r=new Ze;null==e&&(e={}),null==t&&(t={});const o=new i({newline:"\n",tab:"\t",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});o.immutable=!1,r.textformatting={value:o};for(const a in t)r[a]={value:new f(t[a])};for(const a in e)r[a]={value:Y(e[a])?u.createFromGraphic(e[a],n):e[a]};return r}function Le(e){console.log(e)}Ze.prototype=Be,Ze.prototype.infinity={value:Number.POSITIVE_INFINITY},Ze.prototype.pi={value:Math.PI};const qe=Ue;function Pe(e){const t={mode:"async",compiled:!1,functions:{},signatures:[],standardFunction:J,standardFunctionAsync:$,evaluateIdentifier:Oe};for(let n=0;n<e.length;n++)e[n].registerFunctions(t);for(const n in t.functions)Be[n]={value:new f(t.functions[n])},Ze.prototype[n]=Be[n];for(let n=0;n<t.signatures.length;n++)B(t.signatures[n],"async")}async function Ve(e,t){let r=t.spatialReference;null==r&&(r=new V({wkid:102100}));let o=null;e.usesModules&&(o=new n(new Map,e.loadedModules));const a=Ge(t.vars,t.customfunctions,t.timeZone),i={spatialReference:r,services:t.services,exports:{},libraryResolver:o,abortSignal:void 0===t.abortSignal||null===t.abortSignal?{aborted:!1}:t.abortSignal,globalScope:a,console:t.console??Le,timeZone:t.timeZone??null,lrucache:t.lrucache,interceptor:t.interceptor,localScope:null,depthCounter:{depth:1}},c=await me(i,e);if(c instanceof M||c instanceof R){const e=c.value;if(e===y)return null;if(m(e))throw new l(i,s.IllegalResult,null);return e}if(c===y)return null;if(c===A)throw new l(i,s.IllegalResult,null);if(c===C)throw new l(i,s.IllegalResult,null);throw new l(i,s.NeverReach,null)}Pe([E]);export{Ve as executeScript,Pe as extend,qe as functionHelper};
