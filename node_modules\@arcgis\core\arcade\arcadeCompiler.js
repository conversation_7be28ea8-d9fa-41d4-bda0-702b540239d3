/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{toSymbolId as e}from"./arcadeEnvironment.js";import{ArcadeModule as n}from"./ArcadeModule.js";import{ArcadeModuleLoader as t}from"./ArcadeModuleLoader.js";import{getGeometryKeys as o,geometryMember as l,getNestedOptionalValue as r}from"./containerUtils.js";import a from"./Dictionary.js";import{ArcadeExecutionError as s,ExecutionErrorCodes as i,ArcadeCompilationError as c,ArcadeUncompilableError as u}from"./executionError.js";import p from"./Feature.js";import{NativeFunction as m,ArcadeFunction as g,ScopeMarshalledFunction as d,wrapModuleScopedResponse as f}from"./FunctionWrapper.js";import{i as h,u as y,v as b,R as S,I as w,w as v,x,y as M,m as F,z as $,q as C,l as I,f as A,t as _,A as O,B as k,C as G,n as E}from"../chunks/languageUtils.js";import{addFunctionDeclaration as j}from"./treeAnalysis.js";import{A as B}from"../chunks/array.js";import{registerFunctions as N}from"./functions/date.js";import{registerFunctions as R}from"./functions/feature.js";import{registerFunctions as D}from"./functions/geometry.js";import{registerFunctions as L}from"./functions/geomsync.js";import{registerFunctions as U}from"./functions/maths.js";import{registerFunctions as K}from"./functions/stats.js";import{registerFunctions as Z}from"./functions/string.js";import{isPromiseLike as P}from"../core/promiseUtils.js";import W from"../geometry/Geometry.js";import T from"../geometry/SpatialReference.js";import{isGraphic as V,isNumber as q,isArray as z,isString as J,isBoolean as Y}from"../support/guards.js";class H extends g{constructor(e,n){super(),this.paramCount=n,this.fn=e}createFunction(e){return(...n)=>{if(n.length!==this.paramCount)throw new s(e,i.WrongNumberOfParameters,null);return this.fn(...n)}}call(e,n){return this.fn(...n.arguments)}marshalledCall(e,n,t,o){return o(e,n,((n,l,r)=>{r=r.map((n=>!h(n)||n instanceof d?n:f(n,e,o)));const a=this.call(t,{arguments:r});return P(a)?a.then((e=>f(e,t,o))):a}))}}function Q(e,n){const t=[];for(let o=0;o<n.arguments.length;o++)t.push(ee(e,n.arguments[o]));return t}function X(e,n,t){try{return t(e,null,n.arguments)}catch(o){throw o}}function ee(e,n){switch(n.type){case"AssignmentExpression":return pe(e,n);case"UpdateExpression":return ce(e,n);case"TemplateLiteral":return Ce(e,n);case"Identifier":return _e(e,n);case"MemberExpression":return Me(e,n);case"Literal":return null===n.value||void 0===n.value?"null":JSON.stringify(n.value);case"CallExpression":return Oe(e,n);case"UnaryExpression":return Fe(e,n);case"BinaryExpression":return Ie(e,n);case"LogicalExpression":return Ae(e,n);case"ArrayExpression":return $e(e,n);case"ObjectExpression":return te(e,n);default:throw new c(e,i.Unrecognized,n)}}function ne(e,n){switch(n.type){case"EmptyStatement":return"lc.voidOperation";case"VariableDeclaration":return ve(e,n);case"BlockStatement":return fe(e,n);case"FunctionDeclaration":return we(e,n);case"ImportDeclaration":return ye(e,n);case"ExportNamedDeclaration":return be(e,n);case"ReturnStatement":return he(e,n);case"IfStatement":return de(e,n);case"ExpressionStatement":return me(e,n);case"BreakStatement":return"break";case"ContinueStatement":return"continue";case"ForStatement":return ie(e,n);case"ForInStatement":return ae(e,n);case"ForOfStatement":return se(e,n);case"WhileStatement":return ue(e,n);default:throw new c(e,i.Unrecognized,n)}}function te(e,n){let t="lang.dictionary([";for(let o=0;o<n.properties.length;o++){const l=n.properties[o];let r;"Identifier"===l.key.type?(Se(l.key.name),r="'"+l.key.name+"'"):r=ee(e,l.key);o>0&&(t+=","),t+="lang.strCheck("+r+",'ObjectExpression'),lang.aCheck("+ee(e,l.value)+", 'ObjectExpression')"}return t+="])",t}function oe(e,n,t,o,l=(e,n)=>`${e} = ${n}`){const r=Re(e),a=Re(e);return[`var ${r} = ${t};`,`for (var ${a} = 0; ${a} < ${r}; ${a}++) {`,`  ${l(n,a)}`,`  ${ne(e,o)}`,"}","lastStatement = lc.voidOperation;"].join("\n")}function le(e,n,t,o,l=e=>e){const r=Re(e),a=Re(e);return[`var ${r} = ${t};`,`for (var ${a} of ${r}) {`,`  ${n} = ${l(a)};`,`  ${ne(e,o)}`,"}","lastStatement = lc.voidOperation;"].join("\n")}function re(e,n,t,o){const l=Re(e);return[`var ${l} = ${t}.iterator(runtimeCtx.abortSignal);`,`while ((${n} = lang.graphicToFeature(yield ${l}.next(), ${t}, runtimeCtx)) != null) {`,`  ${ne(e,o)}`,"}","lastStatement = lc.voidOperation;"].join("\n")}function ae(n,t){const o=Re(n);let l="var "+o+" = "+ee(n,t.right)+";\n";"VariableDeclaration"===t.left.type&&(l+=ne(n,t.left));const r=e("VariableDeclaration"===t.left.type?t.left.declarations[0].id:t.left);Se(r);let a="";null!==n.localScope&&(void 0!==n.localScope[r]?a="lscope['"+r+"']":void 0!==n.localScope._SymbolsMap[r]&&(a="lscope['"+n.localScope._SymbolsMap[r]+"']"));let s="";if(""===a)if(void 0!==n.globalScope[r])a="gscope['"+r+"']";else if(void 0!==n.globalScope._SymbolsMap[r])a="gscope['"+n.globalScope._SymbolsMap[r]+"']";else if(null!==n.localScope)if(n.undeclaredGlobalsInFunctions.has(r))a="gscope['"+n.undeclaredGlobalsInFunctions.get(r).manglename+"']",s=n.undeclaredGlobalsInFunctions.get(r).manglename;else{const e={manglename:Ne(n),node:t.left};n.undeclaredGlobalsInFunctions.set(r,e),a="gscope['"+e.manglename+"']",s=e.manglename}return s&&(l+="lang.chkAssig('"+s+"',runtimeCtx); \n"),l+="if ("+o+"===null) {  lastStatement = lc.voidOperation; }\n ",l+="else if (lc.isArray("+o+") || lc.isString("+o+")) {\n",l+=oe(n,a,`${o}.length`,t.body),l+="}\n",l+="else if (lc.isImmutableArray("+o+")) {\n",l+=oe(n,a,`${o}.length()`,t.body),l+="}\n",l+="else if (( "+o+" instanceof lang.Dictionary) || lc.isDictionaryLike("+o+")) {\n",l+=le(n,a,`${o}.keys()`,t.body),l+="}\n",n.isAsync&&(l+="else if (lc.isFeatureSet("+o+")) {\n",l+=re(n,a,o,t.body),l+="}\n"),l+=`else if (lc.isGeometry(${o})) {\n`,l+=le(n,a,`lang.getGeometryKeys(${o})`,t.body),l+="}\n",l+="else { lastStatement = lc.voidOperation; } \n",l}function se(n,t){const o=Re(n);let l="var "+o+" = "+ee(n,t.right)+";\n";"VariableDeclaration"===t.left.type&&(l+=ne(n,t.left));const r=e("VariableDeclaration"===t.left.type?t.left.declarations[0].id:t.left);Se(r);let a="";null!==n.localScope&&(void 0!==n.localScope[r]?a="lscope['"+r+"']":void 0!==n.localScope._SymbolsMap[r]&&(a="lscope['"+n.localScope._SymbolsMap[r]+"']"));let s="";if(""===a)if(void 0!==n.globalScope[r])a="gscope['"+r+"']";else if(void 0!==n.globalScope._SymbolsMap[r])a="gscope['"+n.globalScope._SymbolsMap[r]+"']";else if(null!==n.localScope)if(n.undeclaredGlobalsInFunctions.has(r))a="gscope['"+n.undeclaredGlobalsInFunctions.get(r).manglename+"']",s=n.undeclaredGlobalsInFunctions.get(r).manglename;else{const e={manglename:Ne(n),node:t.left};n.undeclaredGlobalsInFunctions.set(r,e),a="gscope['"+e.manglename+"']",s=e.manglename}return s&&(l+="lang.chkAssig('"+s+"',runtimeCtx); \n"),l+="if ("+o+"===null) {  lastStatement = lc.voidOperation; }\n ",l+="else if (lc.isArray("+o+") || lc.isString("+o+")) {\n",l+=oe(n,a,`${o}.length`,t.body,((e,n)=>[`if (${n} >= ${o}.length) {`,`  lang.error('${i.OutOfBounds}');`,"}",`${e} = ${o}[${n}];`].join("\n"))),l+="}\n",l+="else if (lc.isImmutableArray("+o+")) {\n",l+=oe(n,a,`${o}.length()`,t.body,((e,n)=>`${e} = ${o}.get(${n});`)),l+="}\n",l+="else if (( "+o+" instanceof lang.Dictionary) || lc.isDictionaryLike("+o+")) {\n",l+=le(n,a,`${o}.keys()`,t.body,(e=>`lang.entry(${e}, ${o}.field(${e}))`)),l+="}\n",n.isAsync&&(l+="else if (lc.isFeatureSet("+o+")) {\n",l+=re(n,a,o,t.body),l+="}\n"),l+=`else if (lc.isGeometry(${o})) {\n`,l+=le(n,a,`lang.getGeometryKeys(${o})`,t.body,(e=>`lang.entry(${e}, lang.geometryMember(${o}, ${e}, runtimeCtx, null, 1))`)),l+="}\n",l+="else { lastStatement = lc.voidOperation; } \n",l}function ie(e,n){let t="lastStatement = lc.voidOperation; \n";null!==n.init&&("VariableDeclaration"===n.init.type?t+=ne(e,n.init):t+=ee(e,n.init)+"; ");const o=Re(e),l=Re(e);return t+="var "+o+" = true; ",t+="\n do { ",null!==n.update&&(t+=" if ("+o+"===false) {\n "+ee(e,n.update)+"  \n}\n "+o+"=false; \n"),null!==n.test&&(t+="var "+l+" = "+ee(e,n.test)+"; ",t+="if ("+l+"===false) { break; } else if ("+l+"!==true) { lang.error('"+i.BooleanConditionRequired+"');   }\n"),t+=ne(e,n.body),null!==n.update&&(t+="\n "+ee(e,n.update)),t+="\n"+o+" = true; \n} while(true);  lastStatement = lc.voidOperation; ",t}function ce(n,t){if("CallExpression"===t.argument.type)throw new c(n,i.NeverReach,t);let o;if("MemberExpression"===t.argument.type){const e=ee(n,t.argument.object);return!0===t.argument.computed?o=ee(n,t.argument.property):(Se(t.argument.property.name),o="'"+t.argument.property.name+"'"),"lang.memberupdate("+e+","+o+",'"+t.operator+"',"+t.prefix+")"}const l=e(t.argument);if(Se(l),null!==n.localScope){if(void 0!==n.localScope[l])return"lang.update(lscope, '"+l+"','"+t.operator+"',"+t.prefix+")";if(void 0!==n.localScope._SymbolsMap[l])return"lang.update(lscope, '"+n.localScope._SymbolsMap[l]+"','"+t.operator+"',"+t.prefix+")"}if(void 0!==n.globalScope[l])return"lang.update(gscope, '"+l+"','"+t.operator+"',"+t.prefix+")";if(void 0!==n.globalScope._SymbolsMap[l])return"lang.update(gscope, '"+n.globalScope._SymbolsMap[l]+"','"+t.operator+"',"+t.prefix+")";if(null!==n.localScope){if(n.undeclaredGlobalsInFunctions.has(l))return"lang.update(gscope,lang.chkAssig( '"+n.undeclaredGlobalsInFunctions.get(l).manglename+"',runtimeCtx),'"+t.operator+"',"+t.prefix+")";const e={manglename:Ne(n),node:t.argument};return n.undeclaredGlobalsInFunctions.set(l,e),"lang.update(gscope, lang.chkAssig('"+e.manglename+"',runtimeCtx),'"+t.operator+"',"+t.prefix+")"}throw new s(n,i.InvalidIdentifier,t)}function ue(e,n){let t="lastStatement = lc.voidOperation; \n";const o=Re(e);return t+=`\n  var ${o} = true;\n    do {\n      ${o} = ${ee(e,n.test)};\n      if (${o}==false) {\n        break;\n      }\n      if (${o}!==true) {\n        lang.error('${i.BooleanConditionRequired}');\n      }\n      ${ne(e,n.body)}\n    }\n    while (${o} !== false);\n    lastStatement = lc.voidOperation;\n  `,t}function pe(n,t){const o=ee(n,t.right);if("MemberExpression"===t.left.type){let e;const l=ee(n,t.left.object);return!0===t.left.computed?e=ee(n,t.left.property):(e="'"+t.left.property.name+"'",Se(t.left.property.name)),"lang.assignmember("+l+","+e+",'"+t.operator+"',"+o+")"}const l=e(t.left);if(Se(l),null!==n.localScope){if(void 0!==n.localScope[l])return"lscope['"+l+"']=lang.assign("+o+",'"+t.operator+"', lscope['"+l+"'])";if(void 0!==n.localScope._SymbolsMap[l])return"lscope['"+n.localScope._SymbolsMap[l]+"']=lang.assign("+o+",'"+t.operator+"', lscope['"+n.localScope._SymbolsMap[l]+"'])"}if(void 0!==n.globalScope[l])return"gscope['"+l+"']=lang.assign("+o+",'"+t.operator+"', gscope['"+l+"'])";if(void 0!==n.globalScope._SymbolsMap[l])return"gscope['"+n.globalScope._SymbolsMap[l]+"']=lang.assign("+o+",'"+t.operator+"', gscope['"+n.globalScope._SymbolsMap[l]+"'])";if(null!==n.localScope){if(n.undeclaredGlobalsInFunctions.has(l))return"gscope[lang.chkAssig('"+n.undeclaredGlobalsInFunctions.get(l).manglename+"',runtimeCtx)]=lang.assign("+o+",'"+t.operator+"', gscope['"+n.undeclaredGlobalsInFunctions.get(l).manglename+"'])";const e={manglename:Ne(n),node:t.left};return n.undeclaredGlobalsInFunctions.set(l,e),"gscope[lang.chkAssig('"+e.manglename+"',runtimeCtx)]=lang.assign("+o+",'"+t.operator+"', gscope['"+e.manglename+"'])"}throw new s(n,i.InvalidIdentifier,t)}function me(e,n){return"AssignmentExpression"===n.expression.type?"lastStatement = lc.voidOperation; "+ee(e,n.expression)+"; \n ":"lastStatement = "+ee(e,n.expression)+"; "}function ge(e,n){return"BlockStatement"===n.type?ne(e,n):"ReturnStatement"===n.type||"BreakStatement"===n.type||"ContinueStatement"===n.type?ne(e,n)+"; ":"ExpressionStatement"===n.type?ne(e,n):ne(e,n)+"; "}function de(e,n){return`if (lang.mustBoolean(${ee(e,n.test)}, runtimeCtx) === true) {\n    ${ge(e,n.consequent)}\n  } `+(null!==n.alternate?"IfStatement"===n.alternate.type?" else "+de(e,n.alternate):` else {\n      ${ge(e,n.alternate)}\n    }\n`:" else {\n      lastStatement = lc.voidOperation;\n    }\n")}function fe(e,n){let t="";for(let o=0;o<n.body.length;o++)"EmptyStatement"!==n.body[o].type&&("ReturnStatement"===n.body[o].type||"BreakStatement"===n.body[o].type||"ContinueStatement"===n.body[o].type?t+=ne(e,n.body[o])+"; \n":t+=ne(e,n.body[o])+" \n");return t}function he(e,n){if(null===n.argument)return"return lc.voidOperation";return"return "+ee(e,n.argument)}function ye(n,t){const o=e(t.specifiers[0].local);Se(o);const l=n.libraryResolver?.loadLibrary(o),r=Ne(n);void 0===n.moduleFactory[l.uri]&&(n.moduleFactory[l.uri]=Xe(l.syntax,{interceptor:n.interceptor,services:n.services,moduleFactory:n.moduleFactory,lrucache:n.lrucache,timeZone:n.timeZone??null,libraryResolver:n.libraryResolver,customfunctions:n.customfunctions,vars:{}},n.isAsync)),n.moduleFactoryMap[r]=l.uri;let a,s="";return s=n.isAsync?"(yield lang.loadModule('"+r+"', runtimeCtx) ); ":"lang.loadModule('"+r+"', runtimeCtx); ",void 0!==n.globalScope[o]?"gscope['"+o+"']="+s:void 0!==n.globalScope._SymbolsMap[o]?"gscope['"+n.globalScope._SymbolsMap[o]+"']="+s:(n.undeclaredGlobalsInFunctions.has(o)?(a=n.undeclaredGlobalsInFunctions.get(o).manglename,n.undeclaredGlobalsInFunctions.delete(o)):a=Ne(n),n.globalScope._SymbolsMap[o]=a,n.mangleMap[o]=a,"gscope[lang.setAssig('"+a+"', runtimeCtx)]="+s)}function be(n,t){const o=ne(n,t.declaration);if("FunctionDeclaration"===t.declaration.type)n.exports[e(t.declaration.id)]="function";else if("VariableDeclaration"===t.declaration.type)for(const l of t.declaration.declarations)n.exports[e(l.id)]="variable";return o}function Se(e){if("iif"===e)throw new u;if("decode"===e)throw new u;if("when"===e)throw new u;if("defaultvalue"===e)throw new u}function we(n,t){const o=e(t.id);let l;Se(o);let r=!1;void 0!==n.globalScope[o]?l=o:void 0!==n.globalScope._SymbolsMap[o]?l=n.globalScope._SymbolsMap[o]:n.undeclaredGlobalsInFunctions.has(o)?(l=n.undeclaredGlobalsInFunctions.get(o).manglename,n.globalScope._SymbolsMap[o]=l,n.mangleMap[o]=l,n.undeclaredGlobalsInFunctions.delete(o),r=!0):(l=Ne(n),n.globalScope._SymbolsMap[o]=l,n.mangleMap[o]=l);const a={isAsync:n.isAsync,console:n.console,exports:n.exports,undeclaredGlobalsInFunctions:n.undeclaredGlobalsInFunctions,customfunctions:n.customfunctions,moduleFactory:n.moduleFactory,moduleFactoryMap:n.moduleFactoryMap,libraryResolver:n.libraryResolver,lrucache:n.lrucache,interceptor:n.interceptor,services:n.services,symbols:n.symbols,mangleMap:n.mangleMap,localScope:{_SymbolsMap:{}},depthCounter:n.depthCounter,globalScope:n.globalScope};let s="new lang.UserDefinedCompiledFunction( lang.functionDepthchecker(function() { var lastStatement = lc.voidOperation; \n   var lscope = runtimeCtx.localStack[runtimeCtx.localStack.length-1];\n";for(let i=0;i<t.params.length;i++){const o=e(t.params[i]);Se(o);const l=Ne(n);a.localScope._SymbolsMap[o]=l,a.mangleMap[o]=l,s+="lscope['"+l+"']=arguments["+i.toString()+"];\n"}return!0===n.isAsync?(s+="return lang.__awaiter(this, void 0, void 0, function* () {\n",s+=fe(a,t.body)+"\n return lastStatement; ",s+="});  }",s+=", runtimeCtx),"+t.params.length+")",s+="\n lastStatement = lc.voidOperation; \n"):(s+=fe(a,t.body)+"\n return lastStatement; }, runtimeCtx),"+t.params.length+")",s+="\n lastStatement = lc.voidOperation; \n"),r?"gscope[lang.setAssig('"+l+"', runtimeCtx)]="+s:"gscope['"+l+"']="+s}function ve(e,n){const t=[];for(let o=0;o<n.declarations.length;o++)t.push(xe(e,n.declarations[o]));return t.join("\n")+" \n lastStatement=  lc.voidOperation; \n"}function xe(n,t){const o=null===t.init?null:ee(n,t.init),l=e(t.id);if(Se(l),null!==n.localScope){if(void 0!==n.localScope[l])return"lscope['"+l+"']="+o+"; ";if(void 0!==n.localScope._SymbolsMap[l])return"lscope['"+n.localScope._SymbolsMap[l]+"']="+o+"; ";const e=Ne(n);return n.localScope._SymbolsMap[l]=e,n.mangleMap[l]=e,"lscope['"+e+"']="+o+"; "}if(void 0!==n.globalScope[l])return"gscope['"+l+"']="+o+"; ";if(void 0!==n.globalScope._SymbolsMap[l])return"gscope['"+n.globalScope._SymbolsMap[l]+"']="+o+"; ";if(n.undeclaredGlobalsInFunctions.has(l)){const e=n.undeclaredGlobalsInFunctions.get(l).manglename;return n.globalScope._SymbolsMap[l]=e,n.mangleMap[l]=e,n.undeclaredGlobalsInFunctions.delete(l),"gscope[lang.setAssig('"+e+"', runtimeCtx)]="+o+"; "}const r=Ne(n);return n.globalScope._SymbolsMap[l]=r,n.mangleMap[l]=r,"gscope['"+r+"']="+o+"; "}function Me(e,n){try{let t;return!0===n.computed?t=ee(e,n.property):(Se(n.property.name),t="'"+n.property.name+"'"),"lang.member("+ee(e,n.object)+","+t+")"}catch(t){throw t}}function Fe(e,n){try{return"lang.unary("+ee(e,n.argument)+",'"+n.operator+"')"}catch(t){throw t}}function $e(e,n){try{const t=[];for(let o=0;o<n.elements.length;o++)"Literal"===n.elements[o].type?t.push(ee(e,n.elements[o])):t.push("lang.aCheck("+ee(e,n.elements[o])+",'ArrayExpression')");return"["+t.join(",")+"]"}catch(t){throw t}}function Ce(e,n){try{const t=[];let o=0;for(const l of n.quasis)t.push(l.value?JSON.stringify(l.value.cooked):JSON.stringify("")),!1===l.tail&&(t.push(n.expressions[o]?"lang.castString(lang.aCheck("+ee(e,n.expressions[o])+", 'TemplateLiteral'))":""),o++);return"(["+t.join(",")+"]).join('')"}catch(t){throw t}}function Ie(e,n){try{return"lang.binary("+ee(e,n.left)+","+ee(e,n.right)+",'"+n.operator+"')"}catch(t){throw t}}function Ae(e,n){try{if("AssignmentExpression"===n.left.type||"UpdateExpression"===n.left.type)throw new c(e,i.LogicalExpressionOnlyBoolean,n);if("AssignmentExpression"===n.right.type||"UpdateExpression"===n.right.type)throw new c(e,i.LogicalExpressionOnlyBoolean,n);if("&&"===n.operator||"||"===n.operator)return"(lang.logicalCheck("+ee(e,n.left)+") "+n.operator+" lang.logicalCheck("+ee(e,n.right)+"))";throw new c(null,i.LogicExpressionOrAnd,null)}catch(t){throw t}}function _e(n,t){try{const o=e(t);if(Se(o),null!==n.localScope){if(void 0!==n.localScope[o])return"lscope['"+o+"']";if(void 0!==n.localScope._SymbolsMap[o])return"lscope['"+n.localScope._SymbolsMap[o]+"']"}if(void 0!==n.globalScope[o])return"gscope['"+o+"']";if(void 0!==n.globalScope._SymbolsMap[o])return"gscope['"+n.globalScope._SymbolsMap[o]+"']";if(null!==n.localScope){if(n.undeclaredGlobalsInFunctions.has(o))return"gscope[lang.chkAssig('"+n.undeclaredGlobalsInFunctions.get(o).manglename+"',runtimeCtx)]";const e={manglename:Ne(n),node:t};return n.undeclaredGlobalsInFunctions.set(o,e),"gscope[lang.chkAssig('"+e.manglename+"',runtimeCtx)]"}throw new c(n,i.InvalidIdentifier,t)}catch(o){throw o}}function Oe(n,t){try{if("MemberExpression"===t.callee.type){let e;!0===t.callee.computed?e=ee(n,t.callee.property):(Se(t.callee.property.name),e="'"+t.callee.property.name+"'");let o="[";for(let l=0;l<t.arguments.length;l++)l>0&&(o+=", "),o+=ee(n,t.arguments[l]);return o+="]",n.isAsync?"(yield lang.callModuleFunction("+ee(n,t.callee.object)+","+o+","+e+",runtimeCtx))":"lang.callModuleFunction("+ee(n,t.callee.object)+","+o+","+e+",runtimeCtx)"}if("Identifier"!==t.callee.type)throw new c(n,i.FunctionNotFound,t);const o=e(t.callee);if("iif"===o)return ke(n,t);if("when"===o)return Ee(n,t);if("defaultvalue"===o)return Ge(n,t);if("decode"===o)return je(n,t);let l="";if(null!==n.localScope&&(void 0!==n.localScope[o]?l="lscope['"+o+"']":void 0!==n.localScope._SymbolsMap[o]&&(l="lscope['"+n.localScope._SymbolsMap[o]+"']")),""===l)if(void 0!==n.globalScope[o])l="gscope['"+o+"']";else if(void 0!==n.globalScope._SymbolsMap[o])l="gscope['"+n.globalScope._SymbolsMap[o]+"']";else if(null!==n.localScope)if(n.undeclaredGlobalsInFunctions.has(o))l="gscope[lang.chkAssig('"+n.undeclaredGlobalsInFunctions.get(o).manglename+"',runtimeCtx)]";else{const e={manglename:Ne(n),node:t.callee};n.undeclaredGlobalsInFunctions.set(o,e),l="gscope[lang.chkAssig('"+e.manglename+"',runtimeCtx)]"}if(""!==l){let e="[";for(let o=0;o<t.arguments.length;o++)o>0&&(e+=", "),e+=ee(n,t.arguments[o]);return e+="]",n.isAsync?"(yield lang.callfunc("+l+","+e+",runtimeCtx) )":"lang.callfunc("+l+","+e+",runtimeCtx)"}throw new c(n,i.FunctionNotFound,t)}catch(o){throw o}}function ke(e,n){try{if(3!==n.arguments.length)throw new c(e,i.WrongNumberOfParameters,n);const t=Re(e);return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        var ${t} = ${ee(e,n.arguments[0])};\n\n        if (${t} === true) {\n          return  ${ee(e,n.arguments[1])};\n        }\n        else if (${t} === false) {\n          return ${ee(e,n.arguments[2])};\n        }\n        else {\n          lang.error('ExecutionErrorCodes.BooleanConditionRequired');\n        }\n      ${e.isAsync?"})}()))":"}()"}`}catch(t){throw t}}function Ge(e,n){try{if(n.arguments.length<2||n.arguments.length>3)throw new c(e,i.WrongNumberOfParameters,n);const t=Re(e),o=Re(e);return 3===n.arguments.length?`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n      var ${t} = ${ee(e,n.arguments[0])};\n      var ${o} = ${ee(e,n.arguments[1])};\n      ${t} = lang.getNestedOptionalValue(${t}, ${o});\n      return ${t} != null && ${t} !== "" ? ${t} : ${ee(e,n.arguments[2])};\n      ${e.isAsync?"})}()))":"}()"}`:`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        var ${t} = ${ee(e,n.arguments[0])};\n        if (${t} === null) {\n          return  ${ee(e,n.arguments[1])};\n        }\n        if (${t} === "") {\n          return  ${ee(e,n.arguments[1])};\n        }\n        if (${t} === undefined) {\n          return  ${ee(e,n.arguments[1])};\n        }\n        return ${t};\n      ${e.isAsync?"})}()))":"}()"}`}catch(t){throw t}}function Ee(e,n){try{if(n.arguments.length<3)throw new c(e,i.WrongNumberOfParameters,n);if(n.arguments.length%2==0)throw new c(e,i.WrongNumberOfParameters,n);const t=Re(e);let o="var ";for(let l=0;l<n.arguments.length-1;l+=2)o+=`${t} = lang.mustBoolean(${ee(e,n.arguments[l])}, runtimeCtx);\n      if (${t} === true ) {\n        return ${ee(e,n.arguments[l+1])}\n      }\n`;return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        ${o}\n        return ${ee(e,n.arguments[n.arguments.length-1])}\n        ${e.isAsync?"})}()))":"}()"}`}catch(t){throw t}}function je(e,n){try{if(n.arguments.length<2)throw new c(e,i.WrongNumberOfParameters,n);if(2===n.arguments.length)return`(${ee(e,n.arguments[1])})`;if((n.arguments.length-1)%2==0)throw new c(e,i.WrongNumberOfParameters,n);const t=Re(e),o=Re(e);let l="var ";for(let r=1;r<n.arguments.length-1;r+=2)l+=`${o} = ${ee(e,n.arguments[r])};\n      if (lang.binary(${o}, ${t}, "==") === true ) {\n        return ${ee(e,n.arguments[r+1])}\n      }\n`;return`${e.isAsync?"(yield (function() { \n return lang.__awaiter(this, void 0, void 0, function* () {":"function() {"}\n        var ${t} = ${ee(e,n.arguments[0])};\n        ${l}\n        return ${ee(e,n.arguments[n.arguments.length-1])}\n        ${e.isAsync?"})}()))":"}()"}`}catch(t){throw t}}const Be={};function Ne(e){return e.symbols.symbolCounter++,`_T${e.symbols.symbolCounter}`}function Re(e){return e.symbols.symbolCounter++,`_Tvar${e.symbols.symbolCounter}`}N(Be,X),Z(Be,X),R(Be,X,Ve),U(Be,X),D(Be,X),K(Be,X),Be.iif=function(e,n){try{return X(e,n,((t,o,l)=>{throw new s(e,i.Unrecognized,n)}))}catch(t){throw t}},Be.decode=function(e,n){try{return X(e,n,((t,o,l)=>{throw new s(e,i.Unrecognized,n)}))}catch(t){throw t}},Be.when=function(e,n){try{return X(e,n,((t,o,l)=>{throw new s(e,i.Unrecognized,n)}))}catch(t){throw t}},Be.defaultvalue=function(e,n){try{return X(e,n,((t,o,l)=>{throw new s(e,i.Unrecognized,n)}))}catch(t){throw t}};const De={};for(const en in Be)De[en]=new m(Be[en]);L(Be,X);for(const en in Be)Be[en]=new m(Be[en]);const Le=function(){};Le.prototype=Be;const Ue=function(){};function Ke(e,n,t){const o={_SymbolsMap:{}};e||(e={}),t||(t={}),o.textformatting=1,o.infinity=1,o.pi=1;for(const l in n)o[l]=1;for(const l in t)o[l]=1;for(const l in e)o[l]=1;return o}function Ze(e,n,t,o){const l=t?new Ue:new Le;e||(e={}),n||(n={});const r=new a({newline:"\n",tab:"\t",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});r.immutable=!1,l._SymbolsMap={textformatting:1,infinity:1,pi:1},l.textformatting=r,l.infinity=Number.POSITIVE_INFINITY,l.pi=Math.PI;for(const a in n)l[a]=n[a],l._SymbolsMap[a]=1;for(const a in e){const n=e[a];l._SymbolsMap[a]=1,V(n)?l[a]=p.createFromGraphic(n,o??null):l[a]=n}return l}Ue.prototype=De;const Pe={fixSpatialReference:y,parseArguments:Q,standardFunction:X};function We(e,n){const t={mode:n,compiled:!0,functions:{},signatures:[],standardFunction:X,standardFunctionAsync:X,evaluateIdentifier:Ve};for(let o=0;o<e.length;o++)e[o].registerFunctions(t);if("sync"===n){for(const e in t.functions)Be[e]=new m(t.functions[e]),Le.prototype[e]=Be[e];for(let e=0;e<t.signatures.length;e++)j(t.signatures[e],"sync")}else{for(const e in t.functions)De[e]=new m(t.functions[e]),Ue.prototype[e]=De[e];for(let e=0;e<t.signatures.length;e++)j(t.signatures[e],"async")}}function Te(e,n){return e(n)}function Ve(e,n){const t=n.name;if("_SymbolsMap"===t)throw new s(e,i.InvalidIdentifier,null);if(e.localStack.length>0){const n=e.localStack[e.localStack.length-1];if(!t.toLowerCase().startsWith("_t")&&void 0!==n[t])return n[t];const o=e.mangleMap[t];if(void 0!==o&&void 0!==n[o])return n[o]}if(!t.toLowerCase().startsWith("_t")&&void 0!==e.globalScope[t])return e.globalScope[t];if(1===e.globalScope._SymbolsMap[t])return e.globalScope[t];const o=e.mangleMap[t];return void 0!==o?e.globalScope[o]:void 0}We([B],"sync"),We([B],"async");let qe=0;const ze={isNumber:e=>q(e),isArray:e=>z(e),isImmutableArray:e=>F(e),isDictionaryLike:e=>$(e),isString:e=>J(e),isDictionary:e=>C(e),isGeometry:e=>I(e),getGeometryKeys:e=>o(e),geometryMember:(e,n,t,o,r=1)=>l(e,n,t,o,r),error(e){throw new s(null,e,null)},__awaiter:(e,n,t,o)=>new Promise(((t,l)=>{function r(e){try{s(o.next(e))}catch(n){l(n)}}function a(e){try{s(o.throw(e))}catch(n){l(n)}}function s(e){e.done?t(e.value):e.value?.then?e.value.then(r,a):(qe++,qe%100==0?setTimeout((()=>{qe=0,r(e.value)}),0):r(e.value))}s((o=o.apply(e,n||[])).next())})),functionDepthchecker:(e,n)=>function(){if(n.depthCounter.depth++,n.localStack.push({}),n.depthCounter.depth>64)throw new s(null,i.MaximumCallDepth,null);const t=e.apply(this,arguments);return P(t)?t.then((e=>(n.depthCounter.depth--,n.localStack.length=n.localStack.length-1,e))):(n.depthCounter.depth--,n.localStack.length=n.localStack.length-1,t)},chkAssig(e,n){if(void 0===n.gdefs[e])throw new s(n,i.InvalidIdentifier,null);return e},mustBoolean(e,n){if(!0===e||!1===e)return e;throw new s(n,i.BooleanConditionRequired,null)},setAssig:(e,n)=>(n.gdefs[e]=1,e),castString:e=>A(e),aCheck(e,n){if(h(e)){if("ArrayExpression"===n)throw new s(null,i.NoFunctionInArray,null);if("ObjectExpression"===n)throw new s(null,i.NoFunctionInDictionary,null);throw new s(null,i.NoFunctionInTemplateLiteral,null)}return e===v?null:e},Dictionary:a,Feature:p,UserDefinedCompiledFunction:H,dictionary(e){const n={},t=new Map;for(let l=0;l<e.length;l+=2){if(h(e[l+1]))throw new s(null,i.NoFunctionInDictionary,null);if(!1===J(e[l]))throw new s(null,i.KeyMustBeString,null);let o=e[l].toString();const r=o.toLowerCase();t.has(r)?o=t.get(r):t.set(r,o),e[l+1]===v?n[o]=null:n[o]=e[l+1]}const o=new a(n);return o.immutable=!1,o},entry:(e,n)=>new a({key:e,value:n}),strCheck(e){if(!1===J(e))throw new s(null,i.KeyMustBeString,null);return e},unary(e,n){if(Y(e)){if("!"===n)return!e;if("-"===n)return-1*_(e);if("+"===n)return 1*_(e);if("~"===n)return~_(e);throw new s(null,i.UnsupportedUnaryOperator,null)}if("-"===n)return-1*_(e);if("+"===n)return 1*_(e);if("~"===n)return~_(e);throw new s(null,i.UnsupportedUnaryOperator,null)},logicalCheck(e){if(!1===Y(e))throw new s(null,i.LogicExpressionOrAnd,null);return e},logical(e,n,t){if(Y(e)&&Y(n))switch(t){case"||":return e||n;case"&&":return e&&n;default:throw new s(null,i.LogicExpressionOrAnd,null)}throw new s(null,i.LogicExpressionOrAnd,null)},binary(e,n,t){switch(t){case"|":case"<<":case">>":case">>>":case"^":case"&":return G(_(e),_(n),t);case"==":case"=":return k(e,n);case"!=":return!k(e,n);case"<":case">":case"<=":case">=":return O(e,n,t);case"+":return J(e)||J(n)?A(e)+A(n):_(e)+_(n);case"-":return _(e)-_(n);case"*":return _(e)*_(n);case"/":return _(e)/_(n);case"%":return _(e)%_(n);default:throw new s(null,i.UnsupportedOperator,null)}},assign(e,n,t){switch(n){case"=":return e===v?null:e;case"/=":return _(t)/_(e);case"*=":return _(t)*_(e);case"-=":return _(t)-_(e);case"+=":return J(t)||J(e)?A(t)+A(e):_(t)+_(e);case"%=":return _(t)%_(e);default:throw new s(null,i.UnsupportedOperator,null)}},update(e,n,t,o){const l=_(e[n]);return e[n]="++"===t?l+1:l-1,!1===o?l:"++"===t?l+1:l-1},graphicToFeature:(e,n,t)=>null===e?null:p.createFromGraphicLikeObject(e.geometry,e.attributes,n,t.timeZone),memberupdate(e,n,t,o){let l;if(z(e)){if(!q(n))throw new s(null,i.ArrayAccessorMustBeNumber,null);if(n<0&&(n=e.length+n),n<0||n>=e.length)throw new s(null,i.OutOfBounds,null);l=_(e[n]),e[n]="++"===t?l+1:l-1}else if(e instanceof a){if(!1===J(n))throw new s(null,i.KeyAccessorMustBeString,null);if(!0!==e.hasField(n))throw new s(null,i.FieldNotFound,null,{key:n});l=_(e.field(n)),e.setField(n,"++"===t?l+1:l-1)}else if(E(e)){if(!1===J(n))throw new s(null,i.KeyAccessorMustBeString,null);if(!0!==e.hasField(n))throw new s(null,i.FieldNotFound,null);l=_(e.field(n)),e.setField(n,"++"===t?l+1:l-1)}else{if(F(e))throw new s(null,i.Immutable,null);if(!(e instanceof Qe))throw new s(null,i.InvalidIdentifier,null);if(!1===J(n))throw new s(null,i.ModuleAccessorMustBeString,null);if(!0!==e.hasGlobal(n))throw new s(null,i.ModuleExportNotFound,null);l=_(e.global(n)),e.setGlobal(n,"++"===t?l+1:l-1)}return!1===o?l:"++"===t?l+1:l-1},assignmember(e,n,t,o){if(z(e)){if(!q(n))throw new s(null,i.ArrayAccessorMustBeNumber,null);if(n<0&&(n=e.length+n),n<0||n>e.length)throw new s(null,i.OutOfBounds,null);if(n===e.length){if("="!==t)throw new s(null,i.OutOfBounds,null);e[n]=this.assign(o,t,e[n])}else e[n]=this.assign(o,t,e[n])}else if(e instanceof a){if(!1===J(n))throw new s(null,i.KeyAccessorMustBeString,null);if(!0===e.hasField(n))e.setField(n,this.assign(o,t,e.field(n)));else{if("="!==t)throw new s(null,i.FieldNotFound,null);e.setField(n,this.assign(o,t,null))}}else if(E(e)){if(!1===J(n))throw new s(null,i.KeyAccessorMustBeString,null);if(!0===e.hasField(n))e.setField(n,this.assign(o,t,e.field(n)));else{if("="!==t)throw new s(null,i.FieldNotFound,null);e.setField(n,this.assign(o,t,null))}}else{if(F(e))throw new s(null,i.Immutable,null);if(!(e instanceof Qe))throw new s(null,i.InvalidIdentifier,null);if(!1===J(n))throw new s(null,i.ModuleAccessorMustBeString,null);if(!e.hasGlobal(n))throw new s(null,i.ModuleExportNotFound,null);e.setGlobal(n,this.assign(o,t,e.global(n)))}},member(e,n){if(null===e)throw new s(null,i.MemberOfNull,null);if(e instanceof a||$(e)){if(J(n))return e.field(n);throw new s(null,i.InvalidMemberAccessKey,null)}if(e instanceof W){if(J(n))return l(e,n,null,null);throw new s(null,i.InvalidMemberAccessKey,null)}if(z(e)){if(q(n)&&isFinite(n)&&Math.floor(n)===n){if(n<0&&(n=e.length+n),n>=e.length||n<0)throw new s(null,i.OutOfBounds,null);return e[n]}throw new s(null,i.InvalidMemberAccessKey,null)}if(J(e)){if(q(n)&&isFinite(n)&&Math.floor(n)===n){if(n<0&&(n=e.length+n),n>=e.length||n<0)throw new s(null,i.OutOfBounds,null);return e[n]}throw new s(null,i.InvalidMemberAccessKey,null)}if(F(e)){if(q(n)&&isFinite(n)&&Math.floor(n)===n){if(n<0&&(n=e.length()+n),n>=e.length()||n<0)throw new s(null,i.OutOfBounds,null);return e.get(n)}throw new s(null,i.InvalidMemberAccessKey,null)}if(e instanceof Qe){if(J(n))return e.global(n);throw new s(null,i.InvalidMemberAccessKey,null)}throw new s(null,i.InvalidMemberAccessKey,null)},callfunc:(e,n,t)=>e.call(t,{arguments:n,preparsed:!0}),loadModule(e,n){const t=n.moduleFactoryMap[e];if(n.moduleSingletons[t])return n.moduleSingletons[t];const o=n.moduleFactory[t]({vars:{},moduleSingletons:n.moduleSingletons,depthCounter:n.depthCounter,console:n.console,abortSignal:n.abortSignal,isAsync:n.isAsync,services:n.services,lrucache:n.lrucache,timeZone:n.timeZone??null,interceptor:n.interceptor},n.spatialReference);return n.moduleSingletons[t]=o,o},callModuleFunction(e,n,t,o){if(!(e instanceof Qe))throw new s(null,i.FunctionNotFound,null);const l=e.global(t);if(!1===h(l))throw new s(null,i.CallNonFunction,null);return l.call(o,{preparsed:!0,arguments:n})},getNestedOptionalValue:(e,n)=>r(e,n)};function Je(e){console.log(e)}function Ye(e,n,o=!1){null===n&&(n={vars:{},customfunctions:{}});let l=null;e.usesModules&&(l=new t(null,e.loadedModules));const r={isAsync:o,globalScope:Ke(n.vars,o?De:Be,n.customfunctions),moduleFactory:{},moduleFactoryMap:{},undeclaredGlobalsInFunctions:new Map,customfunctions:n.customfunctions,libraryResolver:l,localScope:null,mangleMap:{},depthCounter:{depth:1},exports:{},console:Je,lrucache:n.lrucache,timeZone:n.timeZone??null,interceptor:n.interceptor,services:n.services,symbols:{symbolCounter:0}};let a=fe(r,e);""===a&&(a="lc.voidOperation; "),r.undeclaredGlobalsInFunctions.size>0&&r.undeclaredGlobalsInFunctions.forEach((e=>{throw new c(n,i.InvalidIdentifier,e.node)}));let u="";u=o?"var runtimeCtx=this.prepare(context, true);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \nreturn lang.__awaiter(this, void 0, void 0, function* () {\n\n function mainBody() {\n var lastStatement=lc.voidOperation;\n return lang.__awaiter(this, void 0, void 0, function* () {\n"+a+"\n return lastStatement; }); } \n return this.postProcess(yield mainBody()); }); ":"var runtimeCtx=this.prepare(context, false);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \n function mainBody() {\n var lastStatement=lc.voidOperation;\n "+a+"\n return lastStatement; } \n return this.postProcess(mainBody()); ";const p=r.moduleFactory,m=r.moduleFactoryMap,g=r.exports,d={};let f;for(f in g)d[f]=r.mangleMap[f]??f;const y={lc:b,lang:ze,mangles:r.mangleMap,postProcess(e){if(e instanceof S&&(e=e.value),e instanceof w&&(e=e.value),e===v&&(e=null),e===x)throw new s(null,i.IllegalResult,null);if(e===M)throw new s(null,i.IllegalResult,null);if(h(e))throw new s(null,i.IllegalResult,null);return e},prepare(e,n){let t=e.spatialReference;null==t&&(t=T.WebMercator);const o=Ze(e.vars,e.customfunctions,n,e.timeZone);return{localStack:[],isAsync:n,moduleFactory:p,moduleFactoryMap:m,mangleMap:this.mangles,moduleSingletons:{},exports:g,gdefs:{},exportmangle:d,spatialReference:t,globalScope:o,abortSignal:void 0===e.abortSignal||null===e.abortSignal?{aborted:!1}:e.abortSignal,localScope:null,services:e.services,console:e.console??Je,lrucache:e.lrucache,timeZone:e.timeZone??null,interceptor:e.interceptor,symbols:{symbolCounter:0},depthCounter:{depth:1}}}};return new Function("context","spatialReference",u).bind(y)}async function He(){return We([await import("./functions/geomasync.js")],"async"),!0}class Qe extends n{constructor(e){super(),this.moduleContext=e}hasGlobal(e){return void 0===this.moduleContext.exports[e]&&(e=e.toLowerCase()),void 0!==this.moduleContext.exports[e]}setGlobal(e,n){const t=this.moduleContext.globalScope,o=e.toLowerCase();if(h(n))throw new s(null,i.AssignModuleFunction,null);t[this.moduleContext.exportmangle[o]]=n}global(e){const n=this.moduleContext.globalScope,t=e.toLowerCase(),o=n[this.moduleContext.exportmangle[t]];if(void 0===o)throw new s(null,i.InvalidIdentifier,null);if(h(o)&&!(o instanceof d)){const e=new d;return e.fn=o,e.parameterEvaluator=X,e.context=this.moduleContext,n[this.moduleContext.exportmangle[t]]=e,e}return o}}function Xe(e,n,o=!1){const l={isAsync:o,moduleFactory:n.moduleFactory,moduleFactoryMap:{},libraryResolver:new t(null,e.loadedModules),globalScope:Ke(n.vars,o?De:Be,n.customfunctions),customfunctions:n.customfunctions,localScope:null,mangleMap:{},undeclaredGlobalsInFunctions:new Map,depthCounter:{depth:1},exports:{},console:Je,lrucache:n.lrucache,timeZone:n.timeZone??null,interceptor:n.interceptor,services:n.services,symbols:{symbolCounter:0}};let r=fe(l,e);""===r&&(r="lc.voidOperation; ");let a="";a=o?"var runtimeCtx=this.prepare(context, true);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \nreturn lang.__awaiter(this, void 0, void 0, function* () {\n\n function mainBody() {\n var lastStatement=lc.voidOperation;\n return lang.__awaiter(this, void 0, void 0, function* () {\n"+r+"\n return lastStatement; }); } \n yield mainBody(); \n return this.prepareModule(runtimeCtx); }); ":"var runtimeCtx=this.prepare(context, false);\n var lc = this.lc;  var lang = this.lang; var gscope=runtimeCtx.globalScope; \n function mainBody() {\n var lastStatement=lc.voidOperation;\n "+r+"\n return lastStatement; } \n mainBody(); \n return this.prepareModule(runtimeCtx); ";const s=l.moduleFactory,i=l.moduleFactoryMap,c=l.exports,u={};let p;for(p in c)u[p]=l.mangleMap[p]??p;const m={lc:b,lang:ze,mangles:l.mangleMap,prepareModule:e=>new Qe(e),prepare(e,n){let t=e.spatialReference;null==t&&(t=new T({wkid:102100}));const o=Ze(e.vars,e.customfunctions,n,e.timeZone);return{localStack:[],isAsync:n,exports:c,exportmangle:u,gdefs:{},moduleFactory:s,moduleFactoryMap:i,moduleSingletons:e.moduleSingletons,mangleMap:this.mangles,spatialReference:t,globalScope:o,abortSignal:void 0===e.abortSignal||null===e.abortSignal?{aborted:!1}:e.abortSignal,localScope:null,services:e.services,console:e.console??Je,lrucache:e.lrucache,timeZone:e.timeZone??null,interceptor:e.interceptor,symbols:{symbolCounter:0},depthCounter:e.depthCounter}}};return new Function("context","spatialReference",a).bind(m)}export{H as UserDefinedCompiledFunction,Ye as compileScript,He as enableAsyncSupport,Te as executeScript,We as extend,Pe as functionHelper};
