/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{toSymbolId as e}from"./arcadeEnvironment.js";import{ArcadeModule as t}from"./ArcadeModule.js";import{ArcadeModuleLoader as n}from"./ArcadeModuleLoader.js";import{getNestedOptionalValue as r,geometryMember as o,getGeometryKeys as i}from"./containerUtils.js";import l from"./Dictionary.js";import{ArcadeExecutionError as a,ExecutionErrorCodes as s,ensureArcadeExecutionError as c}from"./executionError.js";import u from"./Feature.js";import{NativeFunction as f,ScopeMarshalledFunction as p,ArcadeFunction as h,wrapModuleScopedResponse as d}from"./FunctionWrapper.js";import{D as w,B as m,i as g,t as y,n as b,m as v,w as S,z as x,f as R,A as F,C as I,R as A,I as M,x as C,y as N,u as O,l as B}from"../chunks/languageUtils.js";import{addFunctionDeclaration as k}from"./treeAnalysis.js";import{A as j}from"../chunks/array.js";import{registerFunctions as E}from"./functions/date.js";import{registerFunctions as D}from"./functions/feature.js";import{registerFunctions as U}from"./functions/geometry.js";import{registerFunctions as K}from"./functions/geomsync.js";import{registerFunctions as Z}from"./functions/maths.js";import{registerFunctions as q}from"./functions/stats.js";import{registerFunctions as L}from"./functions/string.js";import G from"../geometry/Geometry.js";import P from"../geometry/SpatialReference.js";import{isBoolean as V,isString as W,isArray as T,isNumber as _,isGraphic as z}from"../support/guards.js";class Y extends h{constructor(e,t){super(),this.definition=e,this.context=t}createFunction(e){return(...t)=>{const n={spatialReference:this.context.spatialReference,console:this.context.console,services:this.context.services,timeZone:this.context.timeZone??null,lrucache:this.context.lrucache,exports:this.context.exports,libraryResolver:this.context.libraryResolver,interceptor:this.context.interceptor,localScope:{},depthCounter:{depth:e.depthCounter.depth+1},globalScope:this.context.globalScope};if(n.depthCounter.depth>64)throw new a(e,s.MaximumCallDepth,null);return Oe(this.definition,n,t,null)}}call(e,t){return Q(e,t,((n,r,o)=>{const i={spatialReference:e.spatialReference,services:e.services,globalScope:e.globalScope,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,timeZone:e.timeZone??null,console:e.console,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};if(i.depthCounter.depth>64)throw new a(e,s.MaximumCallDepth,t);return Oe(this.definition,i,o,t)}))}marshalledCall(e,t,n,r){return r(e,t,((o,i,l)=>{const a={spatialReference:e.spatialReference,globalScope:n.globalScope,services:e.services,depthCounter:{depth:e.depthCounter.depth+1},libraryResolver:e.libraryResolver,exports:e.exports,console:e.console,timeZone:e.timeZone??null,lrucache:e.lrucache,interceptor:e.interceptor,localScope:{}};return l=l.map((t=>!g(t)||t instanceof p?t:d(t,e,r))),d(Oe(this.definition,a,l,t),n,r)}))}}class H extends t{constructor(e){super(),this.source=e}global(t){const n=this.executingContext.globalScope[e(t)];if(g(n.value)&&!(n.value instanceof p)){const e=new p;e.fn=n.value,e.parameterEvaluator=Q,e.context=this.executingContext,n.value=e}return n.value}setGlobal(t,n){if(g(n))throw new a(null,s.AssignModuleFunction,null);this.executingContext.globalScope[e(t)]={value:n}}hasGlobal(t){return void 0===this.executingContext.exports[t]&&(t=e(t)),void 0!==this.executingContext.exports[t]}loadModule(e){let t=e.spatialReference;null==t&&(t=new P({wkid:102100})),this.moduleScope=ke({},e.customfunctions,e.timeZone),this.executingContext={spatialReference:t,globalScope:this.moduleScope,localScope:null,libraryResolver:new n(e.libraryResolver._moduleSingletons,this.source.syntax.loadedModules),exports:{},services:e.services,console:e.console??Ee,timeZone:e.timeZone??null,lrucache:e.lrucache,interceptor:e.interceptor,depthCounter:{depth:1}},fe(this.executingContext,this.source.syntax)}}function J(e,t){const n=[];for(let r=0;r<t.arguments.length;r++)n.push(X(e,t.arguments[r]));return n}function Q(e,t,n){try{return!0===t.preparsed?n(e,null,t.arguments):n(e,t,J(e,t))}catch(r){throw r}}function X(e,t){try{switch(t.type){case"AssignmentExpression":return se(e,t);case"UpdateExpression":return le(e,t);case"TemplateLiteral":return Fe(e,t);case"Identifier":return Ie(e,t);case"MemberExpression":return ye(e,t);case"Literal":return t.value;case"CallExpression":return Ae(e,t);case"UnaryExpression":return be(e,t);case"BinaryExpression":return Se(e,t);case"LogicalExpression":return xe(e,t);case"ArrayExpression":return ve(e,t);case"ObjectExpression":return ee(e,t);default:throw new a(e,s.Unrecognized,t)}}catch(n){throw c(e,t,n)}}function $(e,t){switch(t.type){case"EmptyStatement":return S;case"VariableDeclaration":return me(e,t);case"ImportDeclaration":return de(e,t);case"ExportNamedDeclaration":return we(e,t);case"BlockStatement":return fe(e,t);case"FunctionDeclaration":return he(e,t);case"ReturnStatement":return pe(e,t);case"IfStatement":return ue(e,t);case"ExpressionStatement":return ce(e,t);case"BreakStatement":return C;case"ContinueStatement":return N;case"ForStatement":return re(e,t);case"ForInStatement":return te(e,t);case"ForOfStatement":return ne(e,t);case"WhileStatement":return oe(e,t);default:throw new a(e,s.Unrecognized,t)}}function ee(e,t){const n={},r=new Map;for(let i=0;i<t.properties.length;i++){const o=t.properties[i],l="Identifier"===o.key.type?o.key.name:X(e,o.key),c=X(e,o.value);if(g(c))throw new a(e,s.NoFunctionInDictionary,t);if(!1===W(l))throw new a(e,s.KeyMustBeString,t);let u=l.toString();const f=u.toLowerCase();r.has(f)?u=r.get(f):r.set(f,u),n[u]=c===S?null:c}const o=new l(n);return o.immutable=!1,o}function te(t,n){const r=X(t,n.right);"VariableDeclaration"===n.left.type&&me(t,n.left);const o=e("VariableDeclaration"===n.left.type?n.left.declarations[0].id:n.left);let c=null;if(null!=t.localScope&&void 0!==t.localScope[o]&&(c=t.localScope[o]),null===c&&void 0!==t.globalScope[o]&&(c=t.globalScope[o]),null===c)throw new a(t,s.InvalidIdentifier,n);if(T(r)||W(r)){const e=r.length;for(let r=0;r<e;r++){c.value=r;const e=$(t,n.body);if(e===C)break;if(e instanceof A)return e}return S}if(v(r)){for(let e=0;e<r.length();e++){c.value=e;const r=$(t,n.body);if(r===C)break;if(r instanceof A)return r}return S}if(r instanceof l||x(r)){const e=r.keys();for(let r=0;r<e.length;r++){c.value=e[r];const o=$(t,n.body);if(o===C)break;if(o instanceof A)return o}return S}if(B(r)){for(const e of i(r)){c.value=e;const r=$(t,n.body);if(r===C)break;if(r instanceof A)return r}return S}return S}function ne(t,n){const r=X(t,n.right);"VariableDeclaration"===n.left.type&&$(t,n.left);const c=e("VariableDeclaration"===n.left.type?n.left.declarations[0].id:n.left);let u=null;if(null!=t.localScope&&void 0!==t.localScope[c]&&(u=t.localScope[c]),null===u&&void 0!==t.globalScope[c]&&(u=t.globalScope[c]),null===u)throw new a(t,s.InvalidIdentifier,n);if(T(r)||W(r)){const e=r.length;for(let o=0;o<e;o++){if(o>=r.length)throw new a(t,s.OutOfBounds,n);u.value=r[o];const e=$(t,n.body);if(e===C)break;if(e instanceof A)return e}return S}if(v(r)){for(let e=0;e<r.length();e++){u.value=r.get(e);const o=$(t,n.body);if(o===C)break;if(o instanceof A)return o}return S}if(r instanceof l||x(r)){for(const e of r.keys()){const o=r.field(e);u.value=new l({key:e,value:o});const i=$(t,n.body);if(i===C)break;if(i instanceof A)return i}return S}if(B(r)){for(const e of i(r)){const i=o(r,e,t,n,1);u.value=new l({key:e,value:i});const a=$(t,n.body);if(a===C)break;if(a instanceof A)return a}return S}return S}function re(e,t){null!==t.init&&("VariableDeclaration"===t.init.type?$(e,t.init):X(e,t.init));const n={testResult:!0,lastAction:S};do{ie(e,t,n)}while(!0===n.testResult);return n.lastAction instanceof A?n.lastAction:S}function oe(e,t){const n={testResult:!0,lastAction:S};if(n.testResult=X(e,t.test),!1===n.testResult)return S;if(!0!==n.testResult)throw new a(e,s.BooleanConditionRequired,t);for(;!0===n.testResult&&(n.lastAction=$(e,t.body),n.lastAction!==C)&&!(n.lastAction instanceof A);)if(n.testResult=X(e,t.test),!0!==n.testResult&&!1!==n.testResult)throw new a(e,s.BooleanConditionRequired,t);return n.lastAction instanceof A?n.lastAction:S}function ie(e,t,n){if(null!==t.test){if(n.testResult=X(e,t.test),!1===n.testResult)return;if(!0!==n.testResult)throw new a(e,s.BooleanConditionRequired,t)}n.lastAction=$(e,t.body),n.lastAction!==C?n.lastAction instanceof A?n.testResult=!1:null!==t.update&&X(e,t.update):n.testResult=!1}function le(t,n){if("CallExpression"===n.argument.type)throw new a(t,s.NeverReach,n);let r;if("MemberExpression"===n.argument.type){const e=X(t,n.argument.object);let o;if(!0===n.argument.computed)o=X(t,n.argument.property);else{if("Identifier"!==n.argument.property.type)throw new a(t,s.Unrecognized,n);o=n.argument.property.name}if(T(e)){if(!_(o))throw new a(t,s.ArrayAccessorMustBeNumber,n);if(o<0&&(o=e.length+o),o<0||o>=e.length)throw new a(t,s.OutOfBounds,n);r=y(e[o]),e[o]="++"===n.operator?r+1:r-1}else if(e instanceof l){if(!1===W(o))throw new a(t,s.KeyAccessorMustBeString,n);if(!0!==e.hasField(o))throw new a(t,s.FieldNotFound,n);r=y(e.field(o)),e.setField(o,"++"===n.operator?r+1:r-1)}else if(b(e)){if(!1===W(o))throw new a(t,s.KeyAccessorMustBeString,n);if(!0!==e.hasField(o))throw new a(t,s.FieldNotFound,n);r=y(e.field(o)),e.setField(o,"++"===n.operator?r+1:r-1)}else{if(v(e))throw new a(t,s.Immutable,n);if(!(e instanceof H))throw new a(t,s.InvalidParameter,n);if(!1===W(o))throw new a(t,s.ModuleAccessorMustBeString,n);if(!0!==e.hasGlobal(o))throw new a(t,s.ModuleExportNotFound,n);r=y(e.global(o)),e.setGlobal(o,"++"===n.operator?r+1:r-1)}return!1===n.prefix?r:"++"===n.operator?r+1:r-1}const o=e(n.argument);if(null!=t.localScope&&void 0!==t.localScope[o])return r=y(t.localScope[o].value),t.localScope[o]={value:"++"===n.operator?r+1:r-1},!1===n.prefix?r:"++"===n.operator?r+1:r-1;if(void 0!==t.globalScope[o])return r=y(t.globalScope[o].value),t.globalScope[o]={value:"++"===n.operator?r+1:r-1},!1===n.prefix?r:"++"===n.operator?r+1:r-1;throw new a(t,s.InvalidIdentifier,n)}function ae(e,t,n,r,o){switch(t){case"=":return e===S?null:e;case"/=":return y(n)/y(e);case"*=":return y(n)*y(e);case"-=":return y(n)-y(e);case"+=":return W(n)||W(e)?R(n)+R(e):y(n)+y(e);case"%=":return y(n)%y(e);default:throw new a(o,s.UnsupportedOperator,r)}}function se(t,n){if("MemberExpression"===n.left.type){const e=X(t,n.left.object);let r;if(!0===n.left.computed)r=X(t,n.left.property);else{if("Identifier"!==n.left.property.type)throw new a(t,s.InvalidIdentifier,n);r=n.left.property.name}const o=X(t,n.right);if(T(e)){if(!_(r))throw new a(t,s.ArrayAccessorMustBeNumber,n);if(r<0&&(r=e.length+r),r<0||r>e.length)throw new a(t,s.OutOfBounds,n);if(r===e.length){if("="!==n.operator)throw new a(t,s.OutOfBounds,n);e[r]=ae(o,n.operator,e[r],n,t)}else e[r]=ae(o,n.operator,e[r],n,t)}else if(e instanceof l){if(!1===W(r))throw new a(t,s.KeyAccessorMustBeString,n);if(!0===e.hasField(r))e.setField(r,ae(o,n.operator,e.field(r),n,t));else{if("="!==n.operator)throw new a(t,s.FieldNotFound,n,{key:r});e.setField(r,ae(o,n.operator,null,n,t))}}else if(b(e)){if(!1===W(r))throw new a(t,s.KeyAccessorMustBeString,n);if(!0===e.hasField(r))e.setField(r,ae(o,n.operator,e.field(r),n,t));else{if("="!==n.operator)throw new a(t,s.FieldNotFound,n,{key:r});e.setField(r,ae(o,n.operator,null,n,t))}}else{if(v(e))throw new a(t,s.Immutable,n);if(!(e instanceof H))throw new a(t,s.InvalidIdentifier,n);if(!1===W(r))throw new a(t,s.ModuleAccessorMustBeString,n);if(!0!==e.hasGlobal(r))throw new a(t,s.ModuleExportNotFound,n);e.setGlobal(r,ae(o,n.operator,e.global(r),n,t))}return S}const r=e(n.left),o=X(t,n.right);if(null!=t.localScope&&void 0!==t.localScope[r])return t.localScope[r]={value:ae(o,n.operator,t.localScope[r].value,n,t)},S;if(void 0!==t.globalScope[r])return t.globalScope[r]={value:ae(o,n.operator,t.globalScope[r].value,n,t)},S;throw new a(t,s.InvalidIdentifier,n)}function ce(e,t){const n=X(e,t.expression);return n===S?S:new M(n)}function ue(e,t){const n=X(e,t.test);if(!0===n)return $(e,t.consequent);if(!1===n)return null!==t.alternate?$(e,t.alternate):S;throw new a(e,s.BooleanConditionRequired,t)}function fe(e,t){let n=S;for(let r=0;r<t.body.length;r++)if(n=$(e,t.body[r]),n instanceof A||n===C||n===N)return n;return n}function pe(e,t){if(null===t.argument)return new A(S);const n=X(e,t.argument);return new A(n)}function he(t,n){const r=e(n.id);return t.globalScope[r]={value:new Y(n,t)},S}function de(t,n){const r=e(n.specifiers[0].local),o=t.libraryResolver.loadLibrary(r);let i;return t.libraryResolver._moduleSingletons?.has(o.uri)?i=t.libraryResolver._moduleSingletons.get(o.uri):(i=new H(o),i.loadModule(t),t.libraryResolver._moduleSingletons?.set(o.uri,i)),t.globalScope[r]={value:i},S}function we(t,n){if($(t,n.declaration),"FunctionDeclaration"===n.declaration.type)t.exports[e(n.declaration.id)]="function";else if("VariableDeclaration"===n.declaration.type)for(const r of n.declaration.declarations)t.exports[e(r.id)]="variable";return S}function me(e,t){for(let n=0;n<t.declarations.length;n++)ge(e,t.declarations[n]);return S}function ge(t,n){let r=null===n.init?null:X(t,n.init);if(r===S&&(r=null),"Identifier"!==n.id.type)throw new a(t,s.InvalidIdentifier,n);const o=e(n.id);null!=t.localScope?t.localScope[o]={value:r}:t.globalScope[o]={value:r}}function ye(e,t){try{const n=X(e,t.object);if(null===n)throw new a(e,s.MemberOfNull,t);if(!1===t.computed){if("Identifier"===t.property.type){if(n instanceof l||x(n))return n.field(t.property.name);if(n instanceof G)return o(n,t.property.name,e,t);if(n instanceof H){if(!n.hasGlobal(t.property.name))throw new a(e,s.InvalidIdentifier,t);return n.global(t.property.name)}}throw new a(e,s.InvalidMemberAccessKey,t)}let r=X(e,t.property);if(n instanceof l||x(n)){if(W(r))return n.field(r);throw new a(e,s.InvalidMemberAccessKey,t)}if(n instanceof H){if(W(r))return n.global(r);throw new a(e,s.InvalidMemberAccessKey,t)}if(n instanceof G){if(W(r))return o(n,r,e,t);throw new a(e,s.InvalidMemberAccessKey,t)}if(T(n)){if(_(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new a(e,s.OutOfBounds,t);return n[r]}throw new a(e,s.InvalidMemberAccessKey,t)}if(W(n)){if(_(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length+r),r>=n.length||r<0)throw new a(e,s.OutOfBounds,t);return n[r]}throw new a(e,s.InvalidMemberAccessKey,t)}if(v(n)){if(_(r)&&isFinite(r)&&Math.floor(r)===r){if(r<0&&(r=n.length()+r),r>=n.length()||r<0)throw new a(e,s.OutOfBounds,t);return n.get(r)}throw new a(e,s.InvalidMemberAccessKey,t)}throw new a(e,s.InvalidMemberAccessKey,t)}catch(n){throw n}}function be(e,t){try{const n=X(e,t.argument);if(V(n)){if("!"===t.operator)return!n;if("-"===t.operator)return-1*y(n);if("+"===t.operator)return 1*y(n);if("~"===t.operator)return~y(n);throw new a(e,s.UnsupportedUnaryOperator,t)}if("~"===t.operator)return~y(n);if("-"===t.operator)return-1*y(n);if("+"===t.operator)return 1*y(n);throw new a(e,s.UnsupportedUnaryOperator,t)}catch(n){throw n}}function ve(e,t){try{const n=[];for(let r=0;r<t.elements.length;r++){const o=X(e,t.elements[r]);if(g(o))throw new a(e,s.NoFunctionInArray,t);o===S?n.push(null):n.push(o)}return n}catch(n){throw n}}function Se(e,t){try{const n=X(e,t.left),r=X(e,t.right);switch(t.operator){case"|":case"<<":case">>":case">>>":case"^":case"&":return I(y(n),y(r),t.operator);case"==":return m(n,r);case"!=":return!m(n,r);case"<":case">":case"<=":case">=":return F(n,r,t.operator);case"+":return W(n)||W(r)?R(n)+R(r):y(n)+y(r);case"-":return y(n)-y(r);case"*":return y(n)*y(r);case"/":return y(n)/y(r);case"%":return y(n)%y(r);default:throw new a(e,s.UnsupportedOperator,t)}}catch(n){throw n}}function xe(e,t){try{const n=X(e,t.left);if(V(n))switch(t.operator){case"||":{if(!0===n)return n;const r=X(e,t.right);if(V(r))return r;throw new a(e,s.LogicExpressionOrAnd,t)}case"&&":{if(!1===n)return n;const r=X(e,t.right);if(V(r))return r;throw new a(e,s.LogicExpressionOrAnd,t)}default:throw new a(e,s.LogicExpressionOrAnd,t)}throw new a(e,s.LogicalExpressionOnlyBoolean,t)}catch(n){throw n}}function Re(e,t,n){if(g(e))throw new a(t,s.NoFunctionInTemplateLiteral,n);return e}function Fe(e,t){let n="",r=0;for(const o of t.quasis)if(n+=o.value?o.value.cooked:"",!1===o.tail){n+=t.expressions[r]?R(Re(X(e,t.expressions[r]),e,t)):"",r++}return n}function Ie(t,n){try{const r=e(n);if(null!=t.localScope&&void 0!==t.localScope[r])return t.localScope[r].value;if(void 0!==t.globalScope[r])return t.globalScope[r].value;throw new a(t,s.InvalidIdentifier,n)}catch(r){throw r}}function Ae(t,n){try{if("MemberExpression"===n.callee.type){const e=X(t,n.callee.object);if(!(e instanceof H))throw new a(t,s.FunctionNotFound,n);const r=!1===n.callee.computed?n.callee.property.name:X(t,n.callee.property);if(!e.hasGlobal(r))throw new a(t,s.FunctionNotFound,n);const o=e.global(r);if(!g(o))throw new a(t,s.CallNonFunction,n);return o.call(t,n)}if("Identifier"!==n.callee.type)throw new a(t,s.FunctionNotFound,n);const r=e(n.callee);if(null!=t.localScope&&void 0!==t.localScope[r]){const e=t.localScope[r];if(g(e.value))return e.value.call(t,n);throw new a(t,s.CallNonFunction,n)}if(void 0!==t.globalScope[r]){const e=t.globalScope[r];if(g(e.value))return e.value.call(t,n);throw new a(t,s.CallNonFunction,n)}throw new a(t,s.FunctionNotFound,n)}catch(r){throw r}}const Me={};function Ce(e,t,n,r){try{const o=t.arguments,i=X(e,o[n]);if(m(i,r))return X(e,o[n+1]);{const i=o.length-n;return 1===i?X(e,o[n]):2===i?null:3===i?X(e,o[n+2]):Ce(e,t,n+2,r)}}catch(o){throw o}}function Ne(e,t,n,r){try{const o=t.arguments;if(!0===r)return X(e,o[n+1]);if(3===o.length-n)return X(e,o[n+2]);{const r=X(e,o[n+2]);if(!1===V(r))throw new a(e,s.BooleanConditionRequired,o[n+2]);return Ne(e,t,n+2,r)}}catch(o){throw o}}function Oe(t,n,r,o){try{const i=t.body;if(r.length!==t.params.length)throw new a(n,s.WrongNumberOfParameters,o);if(null!=n.localScope)for(let o=0;o<r.length;o++)n.localScope[e(t.params[o])]={value:r[o]};const l=$(n,i);if(l instanceof A)return l.value;if(l===C)throw new a(n,s.UnexpectedToken,o);if(l===N)throw new a(n,s.UnexpectedToken,o);return l instanceof M?l.value:l}catch(i){throw i}}E(Me,Q),L(Me,Q),D(Me,Q,Ie),Z(Me,Q),U(Me,Q),q(Me,Q),K(Me,Q),Me.iif=function(e,t){try{if(!0===t.preparsed)throw new a(e,s.NeverReach,t);const n=t.arguments;w(null===n?[]:n,3,3,e,t);const r=X(e,n[0]);if(!1===V(r))throw new a(e,s.BooleanConditionRequired,t);return X(e,!0===r?n[1]:n[2])}catch(n){throw n}},Me.defaultvalue=function(e,t){try{if(!0===t.preparsed)throw new a(e,s.NeverReach,t);const n=t.arguments;w(null===n?[]:n,2,3,e,t);const o=X(e,n[0]);if(3===n.length){const t=X(e,n[1]),i=r(o,t);return null!=i&&""!==i?i:X(e,n[2])}return null===o||""===o||void 0===o?X(e,n[1]):o}catch(n){throw n}},Me.decode=function(e,t){try{if(!0===t.preparsed)throw new a(e,s.NeverReach,t);const n=t.arguments;if(n.length<2)throw new a(e,s.WrongNumberOfParameters,t);if(2===n.length)return X(e,n[1]);{if((n.length-1)%2==0)throw new a(e,s.WrongNumberOfParameters,t);const r=X(e,n[0]);return Ce(e,t,1,r)}}catch(n){throw n}},Me.when=function(e,t){try{if(!0===t.preparsed)throw new a(e,s.NeverReach,t);const n=t.arguments;if(n.length<3)throw new a(e,s.WrongNumberOfParameters,t);if(n.length%2==0)throw new a(e,s.WrongNumberOfParameters,t);const r=X(e,n[0]);if(!1===V(r))throw new a(e,s.BooleanConditionRequired,n[0]);return Ne(e,t,0,r)}catch(n){throw n}};for(const Ke in Me)Me[Ke]={value:new f(Me[Ke])};const Be=function(){};function ke(e,t,n){const r=new Be;e||(e={}),t||(t={});const o=new l({newline:"\n",tab:"\t",singlequote:"'",doublequote:'"',forwardslash:"/",backwardslash:"\\"});o.immutable=!1,r.textformatting={value:o};for(const i in t)r[i]={value:new f(t[i])};for(const i in e)r[i]={value:z(e[i])?u.createFromGraphic(e[i],n):e[i]};return r}Be.prototype=Me,Be.prototype.infinity={value:Number.POSITIVE_INFINITY},Be.prototype.pi={value:Math.PI};const je={fixSpatialReference:O,parseArguments:J,standardFunction:Q};function Ee(e){console.log(e)}function De(e){const t={mode:"sync",compiled:!1,functions:{},signatures:[],standardFunction:Q,evaluateIdentifier:Ie};for(let n=0;n<e.length;n++)e[n].registerFunctions(t);for(const n in t.functions)Me[n]={value:new f(t.functions[n])},Be.prototype[n]=Me[n];for(let n=0;n<t.signatures.length;n++)k(t.signatures[n],"sync")}function Ue(e,t){let r=t.spatialReference;null==r&&(r=new P({wkid:102100}));let o=null;e.usesModules&&(o=new n(new Map,e.loadedModules));const i={spatialReference:r,globalScope:ke(t.vars,t.customfunctions,t.timeZone),localScope:null,services:t.services,exports:{},libraryResolver:o,console:t.console??Ee,timeZone:t.timeZone??null,lrucache:t.lrucache,interceptor:t.interceptor,depthCounter:{depth:1}},l=fe(i,e);if(l instanceof A||l instanceof M){const e=l.value;if(e===S)return null;if(g(e))throw new a(i,s.IllegalResult,null);return e}if(l===S)return null;if(l===C)throw new a(i,s.IllegalResult,null);if(l===N)throw new a(i,s.IllegalResult,null);throw new a(i,s.NeverReach,null)}De([j]);export{Ue as executeScript,De as extend,je as functionHelper};
