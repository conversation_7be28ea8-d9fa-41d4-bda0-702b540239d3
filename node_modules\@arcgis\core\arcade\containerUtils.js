/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"./Dictionary.js";import{ArcadeExecutionError as a,ExecutionErrorCodes as t}from"./executionError.js";import r from"./ImmutablePathArray.js";import n from"./ImmutablePointArray.js";import{m as s,z as i,l as c}from"../chunks/languageUtils.js";import l from"../core/Accessor.js";import{isArray as o,isString as u,isNumber as h}from"../support/guards.js";const p={point:["hasZ","hasM","spatialReference","type","x","y","z","m"],multipoint:["hasZ","hasM","spatialReference","type","points"],polyline:["hasZ","hasM","spatialReference","type","paths"],polygon:["hasZ","hasM","spatialReference","type","rings"],extent:["hasZ","hasM","spatialReference","type","xmin","xmax","ymin","ymax","zmin","zmax","mmin","mmax"]};function f(e){return p[e.type]}let m=0;function d(s,i,c,o,u=1){let h;switch(i=i.toLowerCase()){case"hasz":{const e=s.hasZ;return void 0!==e&&e}case"hasm":{const e=s.hasM;return void 0!==e&&e}case"spatialreference":{let a=s.spatialReference._arcadeCacheId;if(void 0===a){let e=!0;l.isFrozen(s.spatialReference)&&(e=!1),e&&(m++,s.spatialReference._arcadeCacheId=m,a=m)}const t=new e({wkt:s.spatialReference.wkt,wkid:s.spatialReference.wkid});return void 0!==a&&(t._arcadeCacheId="SPREF"+a.toString()),t}}switch(s.type){case"extent":switch(i){case"xmin":case"xmax":case"ymin":case"ymax":case"zmin":case"zmax":case"mmin":case"mmax":{const e=s[i];return void 0!==e?e:null}case"type":return"Extent"}break;case"polygon":switch(i){case"rings":h=s.cache._arcadeCacheId,void 0===h&&(m++,h=m,s.cache._arcadeCacheId=h);return new r(s.rings,s.spatialReference,!0===s.hasZ,!0===s.hasM,h);case"type":return"Polygon"}break;case"point":switch(i){case"x":case"y":case"z":case"m":return s[i]??null;case"type":return"Point"}break;case"polyline":switch(i){case"paths":h=s.cache._arcadeCacheId,void 0===h&&(m++,h=m,s.cache._arcadeCacheId=h);return new r(s.paths,s.spatialReference,!0===s.hasZ,!0===s.hasM,h);case"type":return"Polyline"}break;case"multipoint":switch(i){case"points":h=s.cache._arcadeCacheId,void 0===h&&(m++,h=m,s.cache._arcadeCacheId=h);return new n(s.points,s.spatialReference,!0===s.hasZ,!0===s.hasM,h,1);case"type":return"Multipoint"}}if(1===u)throw new a(c,t.InvalidIdentifier,o);return 2===u?{keystate:"notfound"}:null}function y(e,a){let t,r=e;if(null==r)return null;if(o(a))t=a;else if(s(a))t=a.toArray();else{if(null==a)return null;t=[a]}for(const n of t){if(i(r)){if(!1===u(n))return null;if(!r.hasField(n))return null;r=r.field(n)}else if(c(r)){if(!1===u(n))return null;r=d(r,n,null,null,0)}else if(o(r)){if(!1===h(n))return null;r=n>=0?r[n]:r[r.length+n]}else{if(!s(r))return null;if(!1===h(n))return null;r=n>=0?r.get(n):r.get(r.length()+n)}if(null==r)return null}return r}export{d as geometryMember,f as getGeometryKeys,y as getNestedOptionalValue};
