/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeModule as a}from"./ArcadeModule.js";import{g as e,d as r,l as s,m as o,n as t,o as l,p as n,q as c,r as d,i as m}from"../chunks/languageUtils.js";import{isArray as u}from"../support/guards.js";function i(a){p=a}let p;function f(i){return null===i?null:e(i)?i.clone():r(i)?i:s(i)?i.clone():o(i)?i.toArray().map((a=>f(a))):u(i)?i.map((a=>f(a))):t(i)?p.createFromArcadeFeature(i):l(i)||n(i)?i:c(i)||"esri.arcade.Attachment"===i?.declaredClass?i.deepClone():("esri.arcade.Portal"===i?.declaredClass||d(i)||i instanceof a||m(i),i)}export{i as configureDeepClone,f as deepClone};
