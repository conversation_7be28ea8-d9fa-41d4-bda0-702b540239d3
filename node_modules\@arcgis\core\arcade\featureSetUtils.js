/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../request.js";import t from"./featureSetCollection.js";import{p as a,Z as r}from"../chunks/languageUtils.js";import n from"./featureset/actions/AttributeFilter.js";import s from"./featureset/actions/GroupBy.js";import l from"./featureset/actions/OrderBy.js";import i from"./featureset/actions/SpatialFilter.js";import o from"./featureset/actions/Top.js";import u from"./featureset/sources/FeatureLayerDynamic.js";import c from"./featureset/sources/FeatureLayerMemory.js";import d from"./featureset/sources/FeatureLayerRelated.js";import p from"./featureset/support/cache.js";import{FeatureSetError as f,FeatureSetErrorCodes as y}from"./featureset/support/errorsupport.js";import{isSupportedSourceLayer as m,extractServiceUrl as h,isSupportedLayer as I}from"./featureset/support/shared.js";import L from"../core/Loadable.js";import w from"../core/sql/WhereClause.js";import S from"../layers/FeatureLayer.js";import _ from"../layers/Layer.js";import C from"../portal/PortalItem.js";function g(){null===p.applicationCache&&(p.applicationCache=new p)}async function k(e,t,a){if(p.applicationCache){const a=p.applicationCache.getLayerInfo(e);if(a){const r=await a;return new S({url:e,outFields:t,sourceJSON:r})}const n=new S({url:e,outFields:t}),s=(async()=>(await n.load(),n.sourceJSON))();if(p.applicationCache){p.applicationCache.setLayerInfo(e,s);try{return await s,n}catch(r){throw p.applicationCache.clearLayerInfo(e),r}}return await s,n}if(null!=a){const r=a.getCachedLayerMetadata(e);if(r){const a=await r;return new S({url:e,outFields:t,sourceJSON:a})}const s=new S({url:e,outFields:t}),l=(async()=>(await s.load(),s.sourceJSON))();a.setCachedLayerMetadata(e,l);try{return await l,s}catch(n){throw a.removeCachedLayerMetadata(e,l),n}}return new S({url:e,outFields:t})}async function F(e,t,a,r,n,s=null){return N(await k(e,["*"],n),t,a,r,n,s)}function N(e,t=null,a=null,r=!0,n=null,s=null){switch(e.type){case"catalog-footprint":return N(e.parent,t,a,r,n,s);case"subtype-sublayer":{const l=N(e.parent,t,a,r,n,s);return l.filter(w.create(e.parent.subtypeField+"="+e.subtypeCode.toString(),{fieldsIndex:e.parent.fieldsIndex,timeZone:l.dateFieldsTimeZoneDefaultUTC}))}case"csv":case"geojson":case"knowledge-graph-sublayer":case"wfs":return new c({layer:e,spatialReference:t,outFields:a,includeGeometry:r,lrucache:n,interceptor:s});case"catalog":case"feature":case"oriented-imagery":case"subtype-group":{const l={layer:e,spatialReference:t,outFields:a,includeGeometry:r,lrucache:n,interceptor:s};return!e.url&&e.source?new c(l):new u(l)}default:throw new Error(`Unsupported layer type: ${e.type}`)}}async function A(t){if(null!==p.applicationCache){const e=p.applicationCache.getLayerInfo(t);if(null!==e)return e}const a=(async()=>{const a=await e(t,{responseType:"json",query:{f:"json"}});return a.data?a.data:null})();if(null!==p.applicationCache){p.applicationCache.setLayerInfo(t,a);try{return await a}catch(r){throw p.applicationCache.clearLayerInfo(t),r}}return a}async function T(t,a){const r="QUERYDATAELEMTS:"+a.toString()+":"+t;if(null!==p.applicationCache){const e=p.applicationCache.getLayerInfo(r);if(null!==e)return e}const n=(async()=>{const r=await e(t+"/queryDataElements",{method:"post",responseType:"json",query:{layers:JSON.stringify([a.toString()]),f:"json"}});if(r.data){const e=r.data;if(e.layerDataElements?.[0])return e.layerDataElements[0]}throw new f(y.DataElementsNotFound)})();if(null!==p.applicationCache){p.applicationCache.setLayerInfo(r,n);try{return await n}catch(s){throw p.applicationCache.clearLayerInfo(r),s}}return n}async function O(t,a){if(null!==p.applicationCache){const e=p.applicationCache.getLayerInfo(t);if(null!==e)return e}if(null!=a){const e=a.getCachedServiceMetadata(t);if(null!=e)return e}const r=(async()=>{const a=await e(t,{responseType:"json",query:{f:"json"}});if(a.data){const e=a.data;return e.layers||(e.layers=[]),e.tables||(e.tables=[]),e}return{layers:[],tables:[]}})();if(null!==p.applicationCache){p.applicationCache.setLayerInfo(t,r);try{return await r}catch(n){throw p.applicationCache.clearLayerInfo(t),n}}if(null!=a){a.setCachedServiceMetadata(t,r);try{return await r}catch(s){throw a.removeCachedServiceMetadata(t,r),s}}return r}async function E(e,t,a){const r={metadata:null,networkId:-1,unVersion:3,terminals:[],layerIdLookup:new Map,sourceIdLookup:new Map,queryelem:null,layerNameLkp:{},lkp:null},n=await O(e,null);if(r.metadata=n,void 0!==n.controllerDatasetLayers?.utilityNetworkLayerId&&null!==n.controllerDatasetLayers.utilityNetworkLayerId){if(n.layers)for(const e of n.layers)r.layerNameLkp[e.id]=e.name;if(n.tables)for(const e of n.tables)r.layerNameLkp[e.id]=e.name;const s=n.controllerDatasetLayers.utilityNetworkLayerId;r.networkId=s;const l=await T(e,s);if(l){r.queryelem=l,r.queryelem?.dataElement&&void 0!==r.queryelem.dataElement.schemaGeneration&&(r.unVersion=r.queryelem.dataElement.schemaGeneration),r.lkp={},r.queryelem.dataElement.domainNetworks||(r.queryelem.dataElement.domainNetworks=[]);for(const e of r.queryelem.dataElement.domainNetworks){for(const t of e.edgeSources??[]){const e={layerId:t.layerId,sourceId:t.sourceId,className:r.layerNameLkp[t.layerId]??null};r.layerIdLookup.set(e.layerId,e),r.sourceIdLookup.set(e.sourceId,e),e.className&&(r.lkp[e.className]=e)}for(const t of e.junctionSources??[]){const e={layerId:t.layerId,sourceId:t.sourceId,className:r.layerNameLkp[t.layerId]??null};r.layerIdLookup.set(e.layerId,e),r.sourceIdLookup.set(e.sourceId,e),e.className&&(r.lkp[e.className]=e)}}if(r.queryelem.dataElement.terminalConfigurations)for(const e of r.queryelem.dataElement.terminalConfigurations)for(const t of e.terminals)r.terminals.push({terminalId:t.terminalId,terminalName:t.terminalName});const n=await A(e+"/"+s);if(void 0!==n.systemLayers?.associationsTableId&&null!==n.systemLayers.associationsTableId){let s=null;if(a&&r.unVersion<8){const a=[];r.unVersion>=4&&(a.push("STATUS"),a.push("PERCENTALONG")),s=await F(e+"/"+n.systemLayers.associationsTableId,t,["OBJECTID","FROMNETWORKSOURCEID","TONETWORKSOURCEID","FROMGLOBALID","TOGLOBALID","TOTERMINALID","FROMTERMINALID","ASSOCIATIONTYPE","ISCONTENTVISIBLE","GLOBALID",...a],!1,null,null),await s.load(),r.unVersion>=4&&(s=s.filter(w.create("STATUS NOT IN (1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60, 61, 62, 63)",{fieldsIndex:s.getFieldsIndex(),timeZone:s.dateFieldsTimeZoneDefaultUTC})),await s.load())}return{lkp:r.lkp,associations:s,unVersion:r.unVersion,terminals:r.terminals,layerIdLookup:r.layerIdLookup,sourceIdLookup:r.sourceIdLookup}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[],layerIdLookup:new Map,sourceIdLookup:new Map}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[],layerIdLookup:new Map,sourceIdLookup:new Map}}return{associations:null,unVersion:r.unVersion,lkp:null,terminals:[],layerIdLookup:new Map,sourceIdLookup:new Map}}async function j(e,t,a,r=null,n=null,s=!0,l=null,i=null){let o=e.serviceUrl();if(!o)return null;o="/"===o.charAt(o.length-1)?o+t.relatedTableId.toString():o+"/"+t.relatedTableId.toString();const u=await F(o,r,n,s,l,i);return new d({layer:e,relatedLayer:u,relationship:t,objectId:a,spatialReference:r,outFields:n,includeGeometry:s,lrucache:l,interceptor:i})}n.registerAction(),s.registerAction(),l.registerAction(),i.registerAction(),o.registerAction();class b extends t{constructor(e,t=null,a=null,r=null){super(),this._map=e,this._overrideSpatialReference=t,this._lrucache=a,this._interceptor=r,this._instantLayers=[]}_makeAndAddFeatureSet(e,t=!0,a=null){const r=N(e,this._overrideSpatialReference,null===a?["*"]:a,t,this._lrucache,this._interceptor);return this._instantLayers.push({featureset:r,opitem:e,includeGeometry:t,outFields:JSON.stringify(a)}),r}async featureSetByName(e,t=!0,a=null){if(L.isLoadable(this._map)&&!this._map.loaded)return await this._map.load(),this.featureSetByName(e,t,a);null===a&&(a=["*"]),a=(a=a.slice()).sort();const r=JSON.stringify(a);for(let s=0;s<this._instantLayers.length;s++){const a=this._instantLayers[s];if(a.opitem.title===e&&a.includeGeometry===t&&a.outFields===r)return this._instantLayers[s].featureset}const n=this._map.allLayers.find((t=>I(t)&&t.title===e));if(null!=n)return this._makeAndAddFeatureSet(n,t,a);if(this._map.tables){const r=this._map.tables.find((t=>I(t)&&t.title===e));if(null!=r)return this._makeAndAddFeatureSet(r,t,a)}return null}async featureSetById(e,t=!0,a=["*"]){if(L.isLoadable(this._map)&&!this._map.loaded)return await this._map.load(),this.featureSetById(e,t,a);null===a&&(a=["*"]),a=(a=a.slice()).sort();const r=JSON.stringify(a);for(let s=0;s<this._instantLayers.length;s++){const a=this._instantLayers[s];if(a.opitem.id===e&&a.includeGeometry===t&&a.outFields===r)return this._instantLayers[s].featureset}const n=this._map.allLayers.find((t=>I(t)&&t.id===e));if(n)return this._makeAndAddFeatureSet(n,t,a);if(this._map.tables){const r=this._map.tables.find((t=>I(t)&&t.id===e));if(null!=r)return this._makeAndAddFeatureSet(r,t,a)}return null}}class D extends t{constructor(e,t=null,a=null,r=null){super(),this._url=e,this._overrideSpatialReference=t,this._lrucache=a,this._interceptor=r,this.metadata=null,this._instantLayers=[]}get url(){return this._url}_makeAndAddFeatureSet(e,t=!0,a=null){const r=N(e,this._overrideSpatialReference,null===a?["*"]:a,t,this._lrucache);return this._instantLayers.push({featureset:r,opitem:e,includeGeometry:t,outFields:JSON.stringify(a)}),r}async _loadMetaData(){const e=await O(this._url,this._lrucache);return this.metadata=e,e}load(){return this._loadMetaData()}clone(){return new D(this._url,this._overrideSpatialReference,this._lrucache,this._interceptor)}async featureSetByName(e,t=!0,a=null){null===a&&(a=["*"]),a=(a=a.slice()).sort();const r=JSON.stringify(a);for(let l=0;l<this._instantLayers.length;l++){const a=this._instantLayers[l];if(a.opitem.title===e&&a.includeGeometry===t&&a.outFields===r)return this._instantLayers[l].featureset}const n=await this._loadMetaData();let s=null;for(const l of n.layers??[])l.name===e&&(s=l);if(!s)for(const l of n.tables??[])l.name===e&&(s=l);if(s){const e=await k(this._url+"/"+s.id,["*"],this._lrucache);return this._makeAndAddFeatureSet(e,t,a)}return null}async featureSetById(e,t=!0,a=["*"]){null===a&&(a=["*"]),a=(a=a.slice()).sort();const r=JSON.stringify(a);e=null!=e?e.toString():"";for(let l=0;l<this._instantLayers.length;l++){const a=this._instantLayers[l];if(a.opitem.id===e&&a.includeGeometry===t&&a.outFields===r)return this._instantLayers[l].featureset}const n=await this._loadMetaData();let s=null;for(const l of n.layers??[])null!==l.id&&void 0!==l.id&&l.id.toString()===e&&(s=l);if(!s)for(const l of n.tables??[])null!==l.id&&void 0!==l.id&&l.id.toString()===e&&(s=l);if(s){const e=await k(this._url+"/"+s.id,["*"],this._lrucache);return this._makeAndAddFeatureSet(e,t,a)}return null}}function M(e,t,a=null,r=null){return new b(e,t,a,r)}function v(e,t,a=null,r=null){return new D(e,t,a,r)}function R(e,t,n,s,l){if(null===e)return null;if(a(e)){switch(t){case"datasource":return e.getDataSourceFeatureSet();case"parent":return e;case"root":return e.getRootFeatureSet()}return null}if(e instanceof _&&m(e)){const a=e;switch(t){case"datasource":return N(a,l,"outFields"in a?a.outFields:null,!0,n,s).getDataSourceFeatureSet();case"parent":case"root":return N(a,l,"outFields"in a?a.outFields:null,!0,n,s)}return null}if(r(e)){switch(t){case"datasource":return N(e.parent,l,e.parent.outFields,!0,n,s).getDataSourceFeatureSet();case"parent":case"root":return N(e,l,e.parent.outFields,!0,n,s)}return null}return null}async function q(e,t,a,r,n,s,l,i=null){if(p.applicationCache){const o=p.applicationCache.getLayerInfo(e+":"+s.url);if(o)return G(await o,t,a,r,n,l,i)}if(null!=l){const o=l.getCachedPortalItem(s.url,e);if(null!=o)return await G(await o,t,a,r,n,l,i)}const o=new C({id:e,portal:s}).load();p.applicationCache?p.applicationCache.setLayerInfo(e+":"+s.url,o):null!=l&&l.setCachedPortalItem(s.url,e,o);try{return await G(await o,t,a,r,n,l,i)}catch(u){throw p.applicationCache&&p.applicationCache.clearLayerInfo(e+":"+s.url),null!=l&&l.removeCachedPortalItem(s.url,e,o),u}}async function G(e,t,a,r,n,s,l){let i;if("Feature Service"===e.type||"Map Service"===e.type)i=await k(h(e.url??"")+"/"+t,["*"],s);else{if(t)throw new Error(`layerId=${t} provided for ${e.type} item`);if(null!=s){const t=s.getCachedPortalItemLayer(e.portal.url,e.id);if(null!=t)i=await t;else{const t=_.fromPortalItem(e);s.setCachedPortalItemLayer(e.portal.url,e.id,t);try{i=await t}catch(o){throw s.removeCachedPortalItemLayer(e.portal.url,e.id,t),o}}}else i=await _.fromPortalItem(e)}return N(i,a,r,n,s,l)}export{E as constructAssociationMetaDataFeatureSetFromUrl,N as constructFeatureSet,q as constructFeatureSetFromPortalItem,j as constructFeatureSetFromRelationship,F as constructFeatureSetFromUrl,R as convertToFeatureSet,M as createFeatureSetCollectionFromMap,v as createFeatureSetCollectionFromService,g as initialiseMetaDataCache};
