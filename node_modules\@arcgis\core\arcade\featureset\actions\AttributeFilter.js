/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{FeatureSetError as e,FeatureSetErrorCodes as t}from"../support/errorsupport.js";import s from"../support/FeatureSet.js";import i from"../support/IdSet.js";import{layerGeometryEsriConstants as r,IdState as n}from"../support/shared.js";import{combine as a}from"../support/sqlUtils.js";import{isPromiseLike as l}from"../../../core/promiseUtils.js";import h from"../../../core/sql/WhereClause.js";import u from"../../../geometry/SpatialReference.js";class c extends s{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.actions.AttributeFilter",this._maxProcessing=1e3,this._parent=e.parentfeatureset,e.whereclause instanceof h?(this._whereclause=e.whereclause,this._whereClauseFunction=null):(this._whereClauseFunction=e.whereclause,this._whereclause=null)}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types,this.subtypeField=this._parent.subtypeField,this.subtypes=this._parent.subtypes):(this.fields=[],this.typeIdField="",this.subtypeField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new u({wkid:4326}),this.geometryType=r.point)}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._parent._getFilteredSet("",null,this._whereclause,null,e);return this._checkCancelled(e),null!==this._whereClauseFunction?this._wset=new i(t._candidates.slice().concat(t._known.slice()),[],t._ordered,this._clonePageDefinition(t.pagesDefinition)):this._wset=new i(t._candidates.slice(),t._known.slice(),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){let t=this._parent?._isInFeatureSet(e);return t===n.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?n.Unknown:t)}_getFeature(e,t,s){return this._parent._getFeature(e,t,s)}_getFeatures(e,t,s,i){return this._parent._getFeatures(e,t,s,i)}_featureFromCache(e){return this._parent._featureFromCache(e)}executeWhereClause(e){return this._whereclause?.testFeature(e)??!1}async executeWhereClauseDeferred(e){if(null!==this._whereClauseFunction){const t=this._whereClauseFunction(e);return l(t),t}return this.executeWhereClause(e)}async _fetchAndRefineFeatures(e,t,s){const r=new i([],e,!1,null),a=Math.min(t,e.length);if(await(this._parent?._getFeatures(r,-1,a,s)),this._checkCancelled(s),null==this._whereClauseFunction){for(let t=0;t<a;t++){const s=this._parent?._featureFromCache(e[t]);!0===this.executeWhereClause(s)?this._idstates[e[t]]=n.InFeatureSet:this._idstates[e[t]]=n.NotInFeatureSet}return"success"}const l=[];for(let i=0;i<a;i++){const t=this._parent?._featureFromCache(e[i]);l.push(await this.executeWhereClauseDeferred(t))}for(let i=0;i<t;i++)!0===l[i]?this._idstates[e[i]]=n.InFeatureSet:this._idstates[e[i]]=n.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,s,r,n){null!==this._whereClauseFunction||(null!==s?null!==this._whereclause&&(s=a(this._whereclause,s)):s=this._whereclause),await this._ensureLoaded();const l=await this._parent._getFilteredSet(e,t,s,r,n);let h;return this._checkCancelled(n),h=null!==this._whereClauseFunction?new i(l._candidates.slice().concat(l._known.slice()),[],l._ordered,this._clonePageDefinition(l.pagesDefinition)):new i(l._candidates.slice(),l._known.slice(),l._ordered,this._clonePageDefinition(l.pagesDefinition)),h}async _stat(e,t,s,i,r,n,l){if(null!==this._whereClauseFunction)return null===r&&""===s&&null===i?this._manualStat(e,t,n,l):{calculated:!1};let h=this._whereclause;null!==r&&null!==this._whereclause&&(h=a(this._whereclause,r));const u=await this._parent._stat(e,t,s,i,h,n,l);return!1===u.calculated?null===r&&""===s&&null===i?this._manualStat(e,t,n,l):{calculated:!1}:u}async _canDoAggregates(e,t,s,i,r){return null===this._whereClauseFunction&&(null!==r?null!==this._whereclause&&(r=a(this._whereclause,r)):r=this._whereclause,null!==this._parent&&this._parent._canDoAggregates(e,t,s,i,r))}async _getAggregatePagesDataSourceDefinition(s,i,r,n,l,h,u){if(null===this._parent)throw new e(t.NeverReach);return null!==l?null!==this._whereclause&&(l=a(this._whereclause,l)):l=this._whereclause,this._parent._getAggregatePagesDataSourceDefinition(s,i,r,n,l,h,u)}static registerAction(){s._featuresetFunctions.filter=function(e){if("function"==typeof e)return new c({parentfeatureset:this,whereclause:e});let t=null;return e instanceof h&&(t=e),new c({parentfeatureset:this,whereclause:t})}}getFieldsIndex(){return this._parent.getFieldsIndex()}}export{c as default};
