/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{_ as t}from"../../../chunks/languageUtils.js";import{SqlExpressionAdapted as i,OriginalField as s,AdaptedFeatureSet as n}from"./Adapted.js";import r from"./AttributeFilter.js";import a from"./OrderBy.js";import{FeatureSetError as o,FeatureSetErrorCodes as l}from"../support/errorsupport.js";import d from"../support/FeatureSet.js";import u from"../support/IdSet.js";import f from"../support/OrderbyClause.js";import{IdState as c,layerGeometryEsriConstants as p,FeatureServiceDatabaseType as h}from"../support/shared.js";import{isSingleField as g,toWhereClause as _,predictType as m,scanForField as y,reformulateWithoutField as b}from"../support/sqlUtils.js";import w from"../support/StatsField.js";import{createMD5Hash as F,outputTypes as I}from"../../../core/MD5.js";import{aggregateFunction as S}from"../../../core/sql/AggregateFunctions.js";import{DateOnly as x}from"../../../core/sql/DateOnly.js";import{SqlTimeStampOffset as D}from"../../../core/sql/SqlTimestampOffset.js";import{TimeOnly as j}from"../../../core/sql/TimeOnly.js";import k from"../../../core/sql/WhereClause.js";import C from"../../../geometry/SpatialReference.js";import A from"../../../layers/support/Field.js";import T from"../../../layers/support/FieldsIndex.js";function G(e){if(!e)return"COUNT";switch(e.toLowerCase()){case"max":return"MAX";case"var":case"variance":return"VAR";case"avg":case"average":case"mean":return"AVG";case"min":return"MIN";case"sum":return"SUM";case"stdev":case"stddev":return"STDDEV";case"count":return"COUNT"}return"COUNT"}class O extends d{constructor(e){super(e),this._decodedStatsfield=[],this._decodedGroupbyfield=[],this._candosimplegroupby=!0,this.phsyicalgroupbyfields=[],this.objectIdField="ROW__ID",this._internalObjectIdField="ROW__ID",this._adaptedFields=[],this.declaredClass="esri.arcade.featureset.actions.Aggregate",this._uniqueIds=1,this._maxQuery=10,this._maxProcessing=10,this._parent=e.parentfeatureset,this._config=e}isTable(){return!0}async _getSet(e){if(null===this._wset){const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,this._wset}return this._wset}_isInFeatureSet(){return c.InFeatureSet}_nextUniqueName(e){for(;1===e["T"+this._uniqueIds.toString()];)this._uniqueIds++;const t="T"+this._uniqueIds.toString();return e[t]=1,t}_convertToEsriFieldType(e){return e}_initialiseFeatureSet(){const e={};let t=!1,n=1;const r=this._parent?this._parent.getFieldsIndex():new T([]);for(this.objectIdField="ROW__ID",this.globalIdField="";!1===t;){let e=!1;for(let t=0;t<this._config.groupbyfields.length;t++)if(this._config.groupbyfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}if(!1===e)for(let t=0;t<this._config.statsfields.length;t++)if(this._config.statsfields[t].name.toLowerCase()===this.objectIdField.toLowerCase()){e=!0;break}!1===e?t=!0:(this.objectIdField="ROW__ID"+n.toString(),n++)}for(const i of this._config.statsfields){const e=new w;e.field=i.name,e.tofieldname=i.name,e.workingexpr=i.expression instanceof k?i.expression:k.create(i.expression,{fieldsIndex:r,timeZone:this.dateFieldsTimeZoneDefaultUTC}),e.typeofstat=G(i.statistic),this._decodedStatsfield.push(e)}this._decodedGroupbyfield=[];for(const i of this._config.groupbyfields){const e={name:i.name,singlefield:null,tofieldname:i.name,expression:i.expression instanceof k?i.expression:k.create(i.expression,{fieldsIndex:r,timeZone:this.dateFieldsTimeZoneDefaultUTC}),sqlType:null};this._decodedGroupbyfield.push(e)}if(null!==this._parent){this.geometryType=this._parent.geometryType,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField="";for(const t of this._parent.fields)e[t.name.toUpperCase()]=1;this.types=null,this.subtypes=null,this.subtypeField=""}else this.geometryType=p.point,this.typeIdField="",this.types=null,this.subtypes=null,this.subtypeField="",this.spatialReference=new C({wkid:4326});this.fields=[];const a=new w;a.field=this._nextUniqueName(e),a.tofieldname=this.objectIdField,a.workingexpr=k.create(this._parent.objectIdField,{fieldsIndex:this._parent.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC}),a.typeofstat="MIN",this._decodedStatsfield.push(a);for(const s of this._decodedGroupbyfield){const t=new A;if(s.name=this._nextUniqueName(e),t.name=s.tofieldname,t.alias=t.name,g(s.expression)){const e=this._parent.getField(_(s.expression,h.Standardised));if(!e)throw new o(l.AggregationFieldNotFound);s.name=e.name,s.singlefield=e.name,this.phsyicalgroupbyfields.push(e.name),t.type=e.type,s.sqlType=e.type}else{t.type=this._convertToEsriFieldType(m(s.expression,this._parent.fields));const e=new A;e.name=s.name,e.alias=e.name,this.phsyicalgroupbyfields.push(s.name),this._adaptedFields.push(new i(e,s.expression)),this._candosimplegroupby=!1,s.sqlType=t.type}this.fields.push(t)}if(this._adaptedFields.length>0)for(const i of this._parent.fields)this._adaptedFields.push(new s(i));for(let i=0;i<this._decodedStatsfield.length;i++){const t=new A;let s=null;const n=this._decodedStatsfield[i];n.field=this._nextUniqueName(e),n.tofieldname===this.objectIdField&&(this._internalObjectIdField=n.field),t.name=n.tofieldname,t.alias=t.name;const r=null!==n.workingexpr&&g(n.workingexpr)?_(n.workingexpr,h.Standardised):"";switch(this._decodedStatsfield[i].typeofstat){case"SUM":if(""!==r){if(s=this._parent.getField(r),!s)throw new o(l.AggregationFieldNotFound);t.type=s.type}else t.type="double";break;case"MIN":case"MAX":if(""!==r){if(s=this._parent.getField(r),!s)throw new o(l.AggregationFieldNotFound);t.type=s.type}else t.type="double";break;case"COUNT":t.type="integer";break;case"STDDEV":case"VAR":case"AVG":if(""!==r&&(s=this._parent.getField(r),!s))throw new o(l.AggregationFieldNotFound);t.type="double"}this.fields.push(t)}}async _canDoAggregates(){return!1}async _getFeatures(e,t,i,s){-1!==t&&this._featureCache[t];const n=this._maxQuery;return!0===this._checkIfNeedToExpandKnownPage(e,n)?(await this._expandPagedSet(e,n,0,0,s),this._getFeatures(e,t,i,s)):"success"}async _getFilteredSet(e,t,i,s,o){if(""!==e)return new u([],[],!0,null);let l=null;const d={ordered:!1,nowhereclause:!1};if(await this._ensureLoaded(),null!==i)for(let n=0;n<this._decodedStatsfield.length;n++)if(!0===y(i,this._decodedStatsfield[n].tofieldname)){d.nowhereclause=!0,i=null;break}if(null!==s){d.ordered=!0;for(let e=0;e<this._decodedStatsfield.length;e++)if(!0===s.scanForField(this._decodedStatsfield[e].tofieldname)){s=null,d.ordered=!1;break}if(null!==s)for(const e of this._decodedGroupbyfield)if(null===e.singlefield&&!0===s.scanForField(e.tofieldname)){s=null,d.ordered=!1;break}}if(!1!==this._candosimplegroupby&&await this._parent._canDoAggregates(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,null)){let e=null;i&&(e=this._reformulateWhereClauseWithoutGroupByFields(i));let t=null;s&&(t=this._reformulateOrderClauseWithoutGroupByFields(s));const n=await this._parent._getAggregatePagesDataSourceDefinition(this.phsyicalgroupbyfields,this._decodedStatsfield,"",null,e,t,this._internalObjectIdField);return this._checkCancelled(o),l=!0===d.nowhereclause?new u(n._candidates.slice().concat(n._known.slice()),[],!0===d.ordered&&n._ordered,this._clonePageDefinition(n.pagesDefinition)):new u(n._candidates.slice(),n._known.slice(),!0===d.ordered&&n._ordered,this._clonePageDefinition(n.pagesDefinition)),l}let c=this._parent;if(this._adaptedFields.length>0&&(c=new n({parentfeatureset:this._parent,adaptedFields:this._adaptedFields,extraFilter:null})),!0===d.nowhereclause)l=new u(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new a({parentfeatureset:c,orderbyclause:new f(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}});else{let e=c;if(null!==i){let t=null;i&&(t=this._reformulateWhereClauseWithoutGroupByFields(i)),e=new r({parentfeatureset:e,whereclause:t})}l=new u(["GETPAGES"],[],!1,{aggregatefeaturesetpagedefinition:!0,resultOffset:0,resultRecordCount:this._maxQuery,internal:{fullyResolved:!1,workingItem:null,type:"manual",iterator:null,set:[],subfeatureset:new a({parentfeatureset:e,orderbyclause:new f(this.phsyicalgroupbyfields.join(",")+","+this._parent.objectIdField+" ASC")})}})}return l}_reformulateWhereClauseWithoutStatsFields(e){for(const t of this._decodedStatsfield)e=b(e,t.tofieldname,_(t.workingexpr,h.Standardised),this._parent.getFieldsIndex());return e}_reformulateWhereClauseWithoutGroupByFields(e){for(const t of this._decodedGroupbyfield)t.tofieldname!==t.name&&(e=b(e,t.tofieldname,_(t.expression,h.Standardised),this._parent.getFieldsIndex()));return e}_reformulateOrderClauseWithoutGroupByFields(e){const t=[];for(const i of this._decodedGroupbyfield)i.tofieldname!==i.name&&t.push({field:i.tofieldname,newfield:i.name});return t.length>0?e.replaceFields(t):e}_clonePageDefinition(e){return null===e?null:!0===e.aggregatefeaturesetpagedefinition?{aggregatefeaturesetpagedefinition:!0,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,internal:e.internal}:this._parent._clonePageDefinition(e)}async _refineSetBlock(e,t,i){if(!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQuery))return await this._expandPagedSet(e,this._maxQuery,0,0,i),this._refineSetBlock(e,t,i);this._checkCancelled(i);const s=e._candidates.length;this._refineKnowns(e,t);e._candidates.length;return e._candidates.length,e}_expandPagedSet(e,t,i,s,n){return this._expandPagedSetFeatureSet(e,t,i,s,n)}async _getPhysicalPage(t,i,s){if(!0===t.pagesDefinition.aggregatefeaturesetpagedefinition)return this._sequentialGetPhysicalItem(t,t.pagesDefinition.resultRecordCount,s,[]);const n=await this._getAgregagtePhysicalPage(t,i,s);for(const r of n){const t={geometry:r.geometry,attributes:{}},i={};for(const e in r.attributes)i[e.toLowerCase()]=r.attributes[e];for(const e of this._decodedGroupbyfield)t.attributes[e.tofieldname]=i[e.name.toLowerCase()];for(const e of this._decodedStatsfield)t.attributes[e.tofieldname]=i[e.field.toLowerCase()];this._featureCache[t.attributes[this.objectIdField]]=new e(t)}return n.length}_sequentialGetPhysicalItem(e,t,i,s){return new Promise(((n,r)=>{null===e.pagesDefinition.internal.iterator&&(e.pagesDefinition.internal.iterator=e.pagesDefinition.internal.subfeatureset.iterator(i)),!0===e.pagesDefinition.internal.fullyResolved||0===t?n(s.length):this._nextAggregateItem(e,t,i,s,(r=>{null===r?n(s.length):(t-=1,n(this._sequentialGetPhysicalItem(e,t,i,s)))}),r)}))}_nextAggregateItem(e,i,s,n,r,a){try{t(e.pagesDefinition.internal.iterator.next()).then((t=>{if(null===t)if(null!==e.pagesDefinition.internal.workingItem){const t=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);n.push(t),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(t.attributes[this.objectIdField]),e.pagesDefinition.internal.fullyResolved=!0,r(null)}else e.pagesDefinition.internal.fullyResolved=!0,r(null);else{const o=this._generateAggregateHash(t);if(null===e.pagesDefinition.internal.workingItem)e.pagesDefinition.internal.workingItem={features:[t],id:o};else{if(o!==e.pagesDefinition.internal.workingItem.id){const s=this._calculateAndAppendAggregateItem(e.pagesDefinition.internal.workingItem);return n.push(s),e.pagesDefinition.internal.workingItem=null,e.pagesDefinition.internal.set.push(s.attributes[this.objectIdField]),i-=1,e.pagesDefinition.internal.workingItem={features:[t],id:o},void r(s)}e.pagesDefinition.internal.workingItem.features.push(t)}this._nextAggregateItem(e,i,s,n,r,a)}}),a)}catch(o){a(o)}}_calculateFieldStat(e,t,i){const s=[];for(const n of e.features)if(null!==t.workingexpr){const e=t.workingexpr.calculateValue(n);null!==e&&(e instanceof x||e instanceof j?s.push(e.toNumber()):e instanceof D?s.push(e.toMilliseconds()):s.push(e))}else s.push(null);i.attributes[t.tofieldname]=S(t.typeofstat,[s])}_calculateAndAppendAggregateItem(t){const i={attributes:{},geometry:null};for(const e of this._decodedGroupbyfield){const s=e.singlefield?t.features[0].attributes[e.singlefield]:k.convertValueToStorageFormat(e.expression.calculateValue(t.features[0]),e.sqlType);i.attributes[e.tofieldname]=s}for(const e of this._decodedStatsfield)this._calculateFieldStat(t,e,i);const s=[];for(let e=0;e<this._decodedStatsfield.length;e++)s.push(this._calculateFieldStat(t,this._decodedStatsfield[e],i));return this._featureCache[i.attributes[this.objectIdField]]=new e({attributes:i.attributes,geometry:i.geometry}),i}_generateAggregateHash(e){let t="";for(const i of this._decodedGroupbyfield){const s=i.singlefield?e.attributes[i.singlefield]:i.expression.calculateValue(e);t+=null==s?":":":"+s.toString()}return F(t,I.String)}async _stat(){return{calculated:!1}}async getFeatureByObjectId(){return null}static registerAction(){d._featuresetFunctions.groupby=function(e,t){return new O({parentfeatureset:this,groupbyfields:e,statsfields:t})}}}export{O as default};
