/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{_ as e}from"../../../chunks/languageUtils.js";import{FeatureSetError as t,FeatureSetErrorCodes as r}from"../support/errorsupport.js";import n from"../support/FeatureSet.js";import s from"../support/IdSet.js";import i from"../support/OrderbyClause.js";class a extends n{constructor(e){super(e),this._orderbyclause=null,this.declaredClass="esri.arcade.featureset.actions.OrderBy",this._maxProcessing=100,this._orderbyclause=e.orderbyclause,this._parent=e.parentfeatureset}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,this._orderbyclause,e);return this._checkCancelled(e),this._wset=t,this._wset}return this._wset}async manualOrderSet(e,t){const r=await this.getIdColumnDictionary(e,[],-1,t);this._orderbyclause?.order(r);const n=new s([],[],!0,null);for(let s=0;s<r.length;s++)n._known.push(r[s].id);return n}async getIdColumnDictionary(t,r,n,s){if(n<t._known.length-1){const i=this._maxQueryRate();if("GETPAGES"===t._known[n+1])return await e(this._parent._expandPagedSet(t,i,0,0,s)),this.getIdColumnDictionary(t,r,n,s);let a=n+1;const o=[];for(;a<t._known.length&&"GETPAGES"!==t._known[a];)o.push(t._known[a]),a++;n+=o.length;const u=await e(this._parent._getFeatureBatch(o,s));this._checkCancelled(s);for(const e of u)r.push({id:e.attributes[this.objectIdField],feature:e});return this.getIdColumnDictionary(t,r,n,s)}return t._candidates.length>0?(await e(this._refineSetBlock(t,this._maxProcessingRate(),s)),this._checkCancelled(s),this.getIdColumnDictionary(t,r,n,s)):r}_isInFeatureSet(e){return this._parent._isInFeatureSet(e)}_getFeatures(e,t,r,n){return this._parent._getFeatures(e,t,r,n)}_featureFromCache(e){if(void 0===this._featureCache[e]){const t=this._parent._featureFromCache(e);if(void 0===t)return;return null===t?null:(this._featureCache[e]=t,t)}return this._featureCache[e]}async _fetchAndRefineFeatures(){throw new t(r.NeverReach)}async _getFilteredSet(e,t,r,n,i){await this._ensureLoaded();const a=await this._parent._getFilteredSet(e,t,r,null===n?this._orderbyclause:n,i);this._checkCancelled(i);const o=new s(a._candidates.slice(),a._known.slice(),a._ordered,this._clonePageDefinition(a.pagesDefinition));let u=!0;if(a._candidates.length>0&&(u=!1),!1===o._ordered){let e=await this.manualOrderSet(o,i);return!1===u&&(null===t&&null===r||(e=new s(e._candidates.slice().concat(e._known.slice()),[],e._ordered,this._clonePageDefinition(e.pagesDefinition)))),e}return o}static registerAction(){n._featuresetFunctions.orderBy=function(e){return""===e?this:new a({parentfeatureset:this,orderbyclause:new i(e)})}}getFieldsIndex(){return this._parent.getFieldsIndex()}}export{a as default};
