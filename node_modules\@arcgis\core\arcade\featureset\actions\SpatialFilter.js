/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{shapeExtent as e}from"../../kernel.js";import t from"../sources/Empty.js";import{FeatureSetError as i,FeatureSetErrorCodes as r}from"../support/errorsupport.js";import n from"../support/FeatureSet.js";import a from"../support/IdSet.js";import{IdState as s}from"../support/shared.js";import{invokeRemoteGeometryOp as o}from"../../geometry/operatorsWorkerConnection.js";class l extends n{constructor(e){super(e),this._relation="",this._relationString="",this.declaredClass="esri.arcade.featureset.actions.SpatialFilter",this._relationString=e.relationString,this._parent=e.parentfeatureset,this._maxProcessing=40,this._relation=e.relation,this._relationGeom=e.relationGeom}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,null,null,e);return this._checkCancelled(e),this._wset=new a(t._candidates.slice(),t._known.slice(),t._ordered,this._clonePageDefinition(t.pagesDefinition)),this._wset}return this._wset}_isInFeatureSet(e){let t=this._parent._isInFeatureSet(e);return t===s.NotInFeatureSet?t:(t=this._idstates[e],void 0===t?s.Unknown:t)}_getFeature(e,t,i){return this._parent._getFeature(e,t,i)}_getFeatures(e,t,i,r){return this._parent._getFeatures(e,t,i,r)}_featureFromCache(e){return this._parent._featureFromCache(e)}async executeSpatialRelationTest(t){if(null==t.geometry)return!1;switch(this._relation){case"esriSpatialRelEnvelopeIntersects":{const i=e(this._relationGeom),r=e(t.geometry);return null!=i&&null!=r&&o("intersects",[i.toJSON(),r.toJSON()])}case"esriSpatialRelIntersects":return o("intersects",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelContains":return o("contains",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelOverlaps":return o("overlaps",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelWithin":return o("within",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelTouches":return o("touches",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelCrosses":return o("crosses",[this._relationGeom.toJSON(),t.geometry.toJSON()]);case"esriSpatialRelRelation":return o("relate",[this._relationGeom.toJSON(),t.geometry.toJSON(),this._relationString??""])}}async _fetchAndRefineFeatures(e,t,i){const r=new a([],e,!1,null),n=Math.min(t,e.length);await(this._parent?._getFeatures(r,-1,n,i)),this._checkCancelled(i);const o=[];for(let a=0;a<n;a++){const t=this._parent._featureFromCache(e[a]);o.push(await this.executeSpatialRelationTest(t))}for(let a=0;a<t;a++)!0===o[a]?this._idstates[e[a]]=s.InFeatureSet:this._idstates[e[a]]=s.NotInFeatureSet;return"success"}async _getFilteredSet(e,t,i,r,n){await this._ensureLoaded();const s=await this._parent._getFilteredSet("esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,i,r,n);let o;return this._checkCancelled(n),o=null!==t?new a(s._candidates.slice().concat(s._known.slice()),[],s._ordered,this._clonePageDefinition(s.pagesDefinition)):new a(s._candidates.slice(),s._known.slice(),s._ordered,this._clonePageDefinition(s.pagesDefinition)),o}async _stat(e,t,i,r,n,a,s){if(""!==i)return{calculated:!1};const o=await this._parent._stat(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n,a,s);return!1===o.calculated?null===n&&""===i&&null===r?this._manualStat(e,t,a,s):{calculated:!1}:o}async _canDoAggregates(e,t,i,r,n){return""===i&&null===r&&(null!==this._parent&&this._parent._canDoAggregates(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,n))}async _getAggregatePagesDataSourceDefinition(e,t,n,a,s,o,l){if(null===this._parent)throw new i(r.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,t,"esriSpatialRelRelation"!==this._relation?this._relation:this._relation+":"+this._relationString,this._relationGeom,s,o,l)}static registerAction(){n._featuresetFunctions.intersects=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelIntersects",relationGeom:e})},n._featuresetFunctions.envelopeIntersects=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelEnvelopeIntersects",relationGeom:e})},n._featuresetFunctions.contains=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelContains",relationGeom:e})},n._featuresetFunctions.overlaps=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelOverlaps",relationGeom:e})},n._featuresetFunctions.within=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelWithin",relationGeom:e})},n._featuresetFunctions.touches=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelTouches",relationGeom:e})},n._featuresetFunctions.crosses=function(e){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelCrosses",relationGeom:e})},n._featuresetFunctions.relate=function(e,i){return null==e?new t({parentfeatureset:this}):new l({parentfeatureset:this,relation:"esriSpatialRelRelation",relationGeom:e,relationString:i})}}getFieldsIndex(){return this._parent.getFieldsIndex()}}export{l as default};
