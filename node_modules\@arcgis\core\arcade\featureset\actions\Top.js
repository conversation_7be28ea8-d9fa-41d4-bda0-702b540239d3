/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{FeatureSetError as t,FeatureSetErrorCodes as e}from"../support/errorsupport.js";import n from"../support/FeatureSet.js";import s from"../support/IdSet.js";import{IdState as i}from"../support/shared.js";class a extends n{constructor(t){super(t),this._topnum=0,this.declaredClass="esri.arcade.featureset.actions.Top",this._countedin=0,this._maxProcessing=100,this._topnum=t.topnum,this._parent=t.parentfeatureset}async _getSet(t){if(null===this._wset){await this._ensureLoaded();const e=await this._parent._getSet(t);return this._wset=new s(e._candidates.slice(),e._known.slice(),!1,this._clonePageDefinition(e.pagesDefinition)),this._setKnownLength(this._wset)>this._topnum&&(this._wset._known=this._wset._known.slice(0,this._topnum)),this._setKnownLength(this._wset)>=this._topnum&&(this._wset._candidates=[]),this._wset}return this._wset}_setKnownLength(t){return t._known.length>0&&"GETPAGES"===t._known[t._known.length-1]?t._known.length-1:t._known.length}_isInFeatureSet(t){const e=this._parent._isInFeatureSet(t);if(e===i.NotInFeatureSet)return e;const n=this._idstates[t];return n===i.InFeatureSet||n===i.NotInFeatureSet?n:e===i.InFeatureSet&&void 0===n?this._countedin<this._topnum?(this._idstates[t]=i.InFeatureSet,this._countedin++,i.InFeatureSet):(this._idstates[t]=i.NotInFeatureSet,i.NotInFeatureSet):i.Unknown}async _expandPagedSet(n,s,i,a,o){if(null===this._parent)throw new t(e.NotImplemented);if(s>this._topnum&&(s=this._topnum),this._countedin>=this._topnum&&n.pagesDefinition.internal.set.length<=n.pagesDefinition.resultOffset){let t=n._known.length;return t>0&&"GETPAGES"===n._known[t-1]&&(n._known.length=t-1),t=n._candidates.length,t>0&&"GETPAGES"===n._candidates[t-1]&&(n._candidates.length=t-1),"success"}const r=await this._parent._expandPagedSet(n,s,i,a,o);return this._setKnownLength(n)>this._topnum&&(n._known.length=this._topnum),this._setKnownLength(n)>=this._topnum&&(n._candidates.length=0),r}async _getFeatures(t,e,n,i){const a=[],o=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(t,o))return await this._expandPagedSet(t,o,0,0,i),this._getFeatures(t,e,n,i);-1!==e&&void 0===this._featureCache[e]&&a.push(e);let r=0;for(let s=t._lastFetchedIndex;s<t._known.length&&(r++,r<=n&&(t._lastFetchedIndex+=1),!(void 0===this._featureCache[t._known[s]]&&(t._known[s]!==e&&a.push(t._known[s]),a.length>o)));s++);if(0===a.length)return"success";const _=new s([],a,!1,null),h=Math.min(a.length,n);await this._parent._getFeatures(_,-1,h,i);for(let s=0;s<h;s++){const t=this._parent._featureFromCache(a[s]);void 0!==t&&(this._featureCache[a[s]]=t)}return"success"}async _getFilteredSet(t,e,n,i,a){await this._ensureLoaded();const o=await this._getSet(a);return new s(o._candidates.slice().concat(o._known.slice()),[],!1,this._clonePageDefinition(o.pagesDefinition))}_refineKnowns(t,e){let n=0,s=null;const a=[];for(let o=0;o<t._candidates.length;o++){const r=this._isInFeatureSet(t._candidates[o]);if(r===i.InFeatureSet){if(t._known.push(t._candidates[o]),n+=1,null===s?s={start:o,end:o}:s.end===o-1?s.end=o:(a.push(s),s={start:o,end:o}),t._known.length>=this._topnum)break}else if(r===i.NotInFeatureSet)null===s?s={start:o,end:o}:s.end===o-1?s.end=o:(a.push(s),s={start:o,end:o}),n+=1;else if(r===i.Unknown)break;if(n>=e)break}null!==s&&a.push(s);for(let i=a.length-1;i>=0;i--)t._candidates.splice(a[i].start,a[i].end-a[i].start+1);this._setKnownLength(t)>this._topnum&&(t._known=t._known.slice(0,this._topnum)),this._setKnownLength(t)>=this._topnum&&(t._candidates=[])}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}static registerAction(){n._featuresetFunctions.top=function(t){return new a({parentfeatureset:this,topnum:t})}}getFieldsIndex(){return this._parent.getFieldsIndex()}}export{a as default};
