/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{FeatureSetError as e,FeatureSetErrorCodes as t}from"../support/errorsupport.js";import r from"../support/FeatureSet.js";import s from"../support/IdSet.js";import{FeatureServiceDatabaseType as a,IdState as n}from"../support/shared.js";class u extends r{constructor(e){super(e),this.declaredClass="esri.layers.featureset.sources.Empty",this._maxProcessing=1e3,this._wset=new s([],[],!1,null),this._parent=e.parentfeatureset,this._databaseType=a.Standardised}async _getSet(){return this._wset}optimisePagingFeatureQueries(){}_isInFeatureSet(){return n.NotInFeatureSet}async _getFeature(){throw new e(t.<PERSON>Reach)}async queryAttachments(){return[]}async _getFeatures(){return"success"}_featureFromCache(){return null}async _fetchAndRefineFeatures(){throw new e(t.NeverReach)}async _getFilteredSet(){return new s([],[],!1,null)}_stat(e,t,r,s,a,n,u){return this._manualStat(e,t,n,u)}async _canDoAggregates(){return!1}}export{u as default};
