/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{id as t}from"../../../kernel.js";import r from"../../../request.js";import i from"../../Attachment.js";import s from"../../Dictionary.js";import{FeatureSetError as a,FeatureSetErrorCodes as n}from"../support/errorsupport.js";import l from"../support/FeatureSet.js";import o from"../support/IdSet.js";import{defaultMaxRecords as u,stableStringify as d,FeatureServiceDatabaseType as c,IdState as h,extractServiceUrl as p}from"../support/shared.js";import{toWhereClause as y,isSingleField as f}from"../support/sqlUtils.js";import{decodeStatType as _}from"../support/stats.js";import{createMD5Hash as g,outputTypes as m}from"../../../core/MD5.js";import{sqlAnd as R}from"../../../core/sql.js";import{fromJSON as w}from"../../../geometry/support/jsonUtils.js";import{srToRESTValue as b}from"../../../geometry/support/spatialReferenceUtils.js";import S from"../../../layers/FeatureLayer.js";import F from"../../../rest/support/AttachmentQuery.js";import I from"../../../rest/support/Query.js";import D from"../../../rest/support/StatisticDefinition.js";class P extends l{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerDynamic",this._removeGeometry=!1,this._overrideFields=null,this.formulaCredential=null,this._pageJustIds=!1,this._requestStandardised=!1,this._useDefinitionExpression=!0,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return u}end(){return this._layer}optimisePagingFeatureQueries(e){this._pageJustIds=e}get urlQueryPath(){return this._layer.parsedUrl.path||""}convertQueryToLruCacheKey(e){const t=this.urlQueryPath+","+d(e.toJSON());return g(t,m.String)}async loadImpl(){return!0===this._layer.loaded?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType??"",this.fields=this._layer.fields.slice(),this.hasZ=!0===this._layer?.capabilities?.data?.supportsZ,this.hasM=!0===this._layer?.capabilities?.data?.supportsM,null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const i of this._overrideFields)if(i.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}if(this._layer.source&&this._layer.source.sourceJSON){const e=this._layer.source.sourceJSON.currentVersion;!0===this._layer.source.sourceJSON.useStandardizedQueries?(this._databaseType=c.StandardisedNoInterval,null!=e&&e>=10.61&&(this._databaseType=c.Standardised)):null!=e&&(e>=10.5&&(this._databaseType=c.StandardisedNoInterval,this._requestStandardised=!0),e>=10.61&&(this._databaseType=c.Standardised))}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this.subtypeField=this._layer.subtypeField??"",this.subtypes=this._layer.subtypes,this.typeIdField=("typeIdField"in this._layer?this._layer.typeIdField:null)??"",this.types="types"in this._layer?this._layer.types:null}_isInFeatureSet(){return h.InFeatureSet}async _refineSetBlock(e){return e}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}async _runDatabaseProbe(e){await this._ensureLoaded();const t=new I;this.datesInUnknownTimezone&&(t.timeReferenceUnknownClient=!0),t.where=e.replace("OBJECTID",this._layer.objectIdField);try{return await this._layer.queryObjectIds(t),!0}catch(r){return!1}}_canUsePagination(){return!(!this._layer.capabilities||!this._layer.capabilities.query||!0!==this._layer.capabilities.query.supportsPagination)}_cacheableFeatureSetSourceKey(){return this._layer.url}get gdbVersion(){return this._layer&&this._layer.capabilities&&this._layer.capabilities.data&&this._layer.capabilities.data.isVersioned?this._layer.gdbVersion||"SDE.DEFAULT":""}nativeCapabilities(){return{title:this._layer.title??"",source:this,canQueryRelated:!0,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:this._requestStandardised}}_createQuery(){const e=this._layer.createQuery();return e.returnZ=this.hasZ,e.returnM=this.hasM,this.datesInUnknownTimezone&&(e.timeReferenceUnknownClient=!0),this._requestStandardised&&(e.sqlFormat="standard"),this._useDefinitionExpression?"subtype-group"===this._layer.type&&(e.where=this._layer.definitionExpression):e.where=null,e}executeQuery(e,t){const r="execute"===t?this._layer.queryFeatures.bind(this._layer):"executeForCount"===t?this._layer.queryFeatureCount.bind(this._layer):this._layer.queryObjectIds.bind(this._layer);let i=null;if(this.recentlyUsedQueries){const t=this.convertQueryToLruCacheKey(e);i=this.recentlyUsedQueries.getFromCache(t),null===i&&(i=r(e),this.recentlyUsedQueries.addToCache(t,i),i=i.catch((e=>{throw this.recentlyUsedQueries?.removeFromCache(t),e})))}return this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:e,method:t}),null===i&&(i=r(e)),i}async _getFilteredSet(e,t,r,i,s){const a=await this.databaseType();if(this.isTable()&&t&&null!==e&&""!==e){return new o([],[],!0,null)}if(this._canUsePagination())return this._getFilteredSetUsingPaging(e,t,r,i,s);let n="",l=!1;null!==i&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(n=i.constructClause(),l=!0);const u=this._createQuery();u.where=R(u.where,null===r?null===t?"1=1":"":y(r,a)),u.spatialRelationship=this._makeRelationshipEnum(e),u.outSpatialReference=this.spatialReference,u.orderByFields=""!==n?n.split(","):null,u.geometry=null===t?null:t,u.relationParameter=this._makeRelationshipParam(e);let d=await this.executeQuery(u,"executeForIds");null===d&&(d=[]),this._checkCancelled(s);return new o([],d,l,null)}_expandPagedSet(e,t,r,i,s){return this._expandPagedSetFeatureSet(e,t,r,i,s)}async _getFilteredSetUsingPaging(e,t,r,i,s){let a="",n=!1;null!==i&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(a=i.constructClause(),n=!0);const l=await this.databaseType(),u=null===r?null===t?"1=1":"":y(r,l);let d=this._maxQueryRate();const c=this._layer.capabilities?.query.maxRecordCount;null!=c&&c<d&&(d=c);let h=null;if(!0===this._pageJustIds)h=new o([],["GETPAGES"],n,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:this._layer.objectIdField,resultRecordCount:d,resultOffset:0,geometry:null===t?null:t,where:u,orderByFields:a,returnGeometry:!1,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}});else{let r=!0;!0===this._removeGeometry&&(r=!1);const i=this._overrideFields??this._fieldsIncludingObjectId(["*"]);h=new o([],["GETPAGES"],n,{spatialRel:this._makeRelationshipEnum(e),relationParam:this._makeRelationshipParam(e),outFields:i.join(","),resultRecordCount:d,resultOffset:0,geometry:null===t?null:t,where:u,orderByFields:a,returnGeometry:r,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}return await this._expandPagedSet(h,d,0,1,s),h}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,spatialRel:e.spatialRel,relationParam:e.relationParam,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,r){const i=e.pagesDefinition.internal.lastRetrieved,s=i,a=e.pagesDefinition.internal.lastPage,n=this._createQuery();n.spatialRelationship=e.pagesDefinition.spatialRel,n.relationParameter=e.pagesDefinition.relationParam,n.outFields=e.pagesDefinition.outFields.split(","),n.num=e.pagesDefinition.resultRecordCount,n.start=e.pagesDefinition.internal.lastPage,n.geometry=e.pagesDefinition.geometry,n.where=R(n.where,e.pagesDefinition.where),n.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,n.returnGeometry=e.pagesDefinition.returnGeometry,n.outSpatialReference=this.spatialReference;const l=await this.executeQuery(n,"execute");if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==a)return"done";const o=this._layer.objectIdField;for(let u=0;u<l.features.length;u++)e.pagesDefinition.internal.set[s+u]=l.features[u].attributes[o];if(!1===this._pageJustIds)for(let u=0;u<l.features.length;u++)this._featureCache[l.features[u].attributes[o]]=l.features[u];return(void 0===l.exceededTransferLimit&&l.features.length!==e.pagesDefinition.resultRecordCount||!1===l.exceededTransferLimit)&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=i+l.features.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,"done"}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice();if(t.includes("*"))return t;let r=!1;for(const i of t)if(i.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}async _getFeatures(e,t,r,i){const s=[];if(-1!==t&&void 0===this._featureCache[t]&&s.push(t),!0===this._checkIfNeedToExpandKnownPage(e,this._maxProcessingRate()))return await this._expandPagedSet(e,this._maxProcessingRate(),0,0,i),this._getFeatures(e,t,r,i);let l=0;for(let a=e._lastFetchedIndex;a<e._known.length;a++){if(e._lastFetchedIndex+=1,l++,void 0===this._featureCache[e._known[a]]){let r=!1;if(null!==this._layer._mode&&void 0!==this._layer._mode){const t=this._layer._mode;if(void 0!==t._featureMap[e._known[a]]){const i=t._featureMap[e._known[a]];null!==i&&(r=!0,this._featureCache[e._known[a]]=i)}}if(!1===r&&(e._known[a]!==t&&s.push(e._known[a]),s.length>=this._maxProcessingRate()-1))break}if(l>=r&&0===s.length)break}if(0===s.length)return"success";const o=this._createQuery();o.objectIds=s,o.outFields=this._overrideFields??this._fieldsIncludingObjectId(["*"]),o.returnGeometry=!0,!0===this._removeGeometry&&(o.returnGeometry=!1),o.outSpatialReference=this.spatialReference;const u=await this.executeQuery(o,"execute");if(this._checkCancelled(i),void 0!==u.error)throw new a(n.RequestFailed,{reason:u.error});const d=this._layer.objectIdField;for(let a=0;a<u.features.length;a++)this._featureCache[u.features[a].attributes[d]]=u.features[a];return"success"}async _getDistinctPages(e,t,r,i,s,l,o,u,d){await this._ensureLoaded();const c=await this.databaseType();let h=r.parseTree.column;const p=this._layer.fields??[];for(let a=0;a<p.length;a++)if(p[a].name.toLowerCase()===h.toLowerCase()){h=p[a].name;break}const f=this._createQuery();f.where=R(f.where,null===l?null===s?"1=1":"":y(l,c)),f.spatialRelationship=this._makeRelationshipEnum(i),f.relationParameter=this._makeRelationshipParam(i),f.geometry=null===s?null:s,f.returnDistinctValues=!0,f.returnGeometry=!1,f.outFields=[h];const _=await this.executeQuery(f,"execute");if(this._checkCancelled(d),!_.hasOwnProperty("features"))throw new a(n.InvalidStatResponse);let g=!1;for(let a=0;a<p.length;a++)if(p[a].name===h){"date"===p[a].type&&(g=!0);break}for(let a=0;a<_.features.length;a++){if(g){const e=_.features[a].attributes[h];null!==e?u.push(new Date(e)):u.push(e)}else u.push(_.features[a].attributes[h]);if(u.length>=o)break}if(0===_.features.length)return u;if(_.features.length===this._layer.capabilities?.query.maxRecordCount&&u.length<o){return{calculated:!0,result:await this._getDistinctPages(e+_.features.length,t,r,i,s,l,o,u,d)}}return u}async _distinctStat(e,t,r,i,s,a,n){return{calculated:!0,result:await this._getDistinctPages(0,e,t,r,i,s,a,[],n)}}isTable(){return this._layer.isTable||null===this._layer.geometryType||"table"===this._layer.type||""===this._layer.geometryType||"esriGeometryNull"===this._layer.geometryType}async _countstat(e,t,r,i){const s=await this.databaseType();if(this.isTable()&&r&&null!==t&&""!==t)return{calculated:!0,result:0};const a=this._createQuery();a.where=R(a.where,null===i?null===r?"1=1":"":y(i,s)),a.spatialRelationship=this._makeRelationshipEnum(t),a.relationParameter=this._makeRelationshipParam(t),a.geometry=null===r?null:r,a.returnGeometry=!1;return{calculated:!0,result:await this.executeQuery(a,"executeForCount")}}async _stats(e,t,r,i,s,l,o){await this._ensureLoaded();const u=this._layer.capabilities?.query,d=!!u?.supportsSqlExpression,c=!!u?.supportsStatistics,h=!!u?.supportsDistinct;if("count"===e)return h?this._countstat(e,r,i,s):{calculated:!1};if(!1===c||!1===f(t)&&!1===d||!1===t.isStandardized)return""!==r||null!==s?{calculated:!1}:this._manualStat(e,t,l,o);if("distinct"===e)return!1===h?""!==r||null!==s?{calculated:!1}:this._manualStat(e,t,l,o):this._distinctStat(e,t,r,i,s,l,o);const p=await this.databaseType();if(this.isTable()&&i&&null!==r&&""!==r)return{calculated:!0,result:null};const g=this._createQuery();g.where=R(g.where,null===s?null===i?"1=1":"":y(s,p)),g.spatialRelationship=this._makeRelationshipEnum(r),g.relationParameter=this._makeRelationshipParam(r),g.geometry=null===i?null:i;const m=new D;m.statisticType=_(e),m.onStatisticField=y(t,p),m.outStatisticFieldName="ARCADE_STAT_RESULT",g.returnGeometry=!1;let w="ARCADE_STAT_RESULT";g.outStatistics=[m];const b=await this.executeQuery(g,"execute");if(!b.hasOwnProperty("features")||0===b.features.length)throw new a(n.InvalidStatResponse);let S=!1;const F=b.fields??[];for(let a=0;a<F.length;a++)if("ARCADE_STAT_RESULT"===F[a].name.toUpperCase()){w=F[a].name,"date"===F[a].type&&(S=!0);break}if(S){let e=b.features[0].attributes[w];return null!==e&&(e=new Date(b.features[0].attributes[w])),{calculated:!0,result:e}}return{calculated:!0,result:b.features[0].attributes[w]}}_stat(e,t,r,i,s,a,n){return this._stats(e,t,r,i,s,a,n)}async _canDoAggregates(e,t){await this._ensureLoaded();let r=!1;const i=this._layer.capabilities?.query,s=!0===i?.supportsSqlExpression;if(null!=i&&!0===i.supportsStatistics&&!0===i.supportsOrderBy&&(r=!0),r)for(let a=0;a<t.length-1;a++)(!1===t[a].workingexpr?.isStandardized||!1===f(t[a].workingexpr)&&!1===s)&&(r=!1);return!1!==r}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _getAggregatePagesDataSourceDefinition(e,t,r,i,s,a,n){await this._ensureLoaded();const l=await this.databaseType();let u="",d=!1,c=!1;null!==a&&this._layer.capabilities&&this._layer.capabilities.query&&!0===this._layer.capabilities.query.supportsOrderBy&&(u=a.constructClause(),c=!0),this._layer.capabilities&&this._layer.capabilities.query&&!1===this._layer.capabilities.query.supportsPagination&&(c=!1,d=!0,u=this._layer.objectIdField);const h=[];for(let o=0;o<t.length;o++){const e=new D;e.onStatisticField=null!==t[o].workingexpr?y(t[o].workingexpr,l):"",e.outStatisticFieldName=t[o].field,e.statisticType=t[o].toStatisticsName(),h.push(e)}""===u&&(u=e.join(","));let p=this._maxQueryRate();const f=this._layer.capabilities?.query.maxRecordCount;null!=f&&f<p&&(p=f);const _=null===s?null===i?"1=1":"":y(s,l);return new o([],["GETPAGES"],c,{groupbypage:!0,spatialRel:this._makeRelationshipEnum(r),relationParam:this._makeRelationshipParam(r),outFields:["*"],useOIDpagination:d,generatedOid:n,resultRecordCount:p,resultOffset:0,groupByFieldsForStatistics:e,outStatistics:h,geometry:null===i?null:i,where:_,orderByFields:u,returnGeometry:!1,returnIdsOnly:!1,internal:{lastMaxId:-1,set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}})}async _getAgregagtePhysicalPage(t,r,i){let s=t.pagesDefinition.where;!0===t.pagesDefinition.useOIDpagination&&(s=R(s,t.pagesDefinition.generatedOid+">"+t.pagesDefinition.internal.lastMaxId.toString()));const l=t.pagesDefinition.internal.lastRetrieved,o=l,u=t.pagesDefinition.internal.lastPage,d=this._createQuery();if(d.where=R(d.where,s),d.spatialRelationship=t.pagesDefinition.spatialRel,d.relationParameter=t.pagesDefinition.relationParam,d.outFields=t.pagesDefinition.outFields,d.outStatistics=t.pagesDefinition.outStatistics,d.geometry=t.pagesDefinition.geometry,d.groupByFieldsForStatistics=t.pagesDefinition.groupByFieldsForStatistics,d.num=t.pagesDefinition.resultRecordCount,d.start=t.pagesDefinition.internal.lastPage,d.returnGeometry=t.pagesDefinition.returnGeometry,d.orderByFields=""!==t.pagesDefinition.orderByFields?t.pagesDefinition.orderByFields.split(","):null,this.isTable()&&d.geometry&&d.spatialRelationship)return[];const c=await this.executeQuery(d,"execute");if(this._checkCancelled(i),!c.hasOwnProperty("features"))throw new a(n.InvalidStatResponse);const h=[];if(t.pagesDefinition.internal.lastPage!==u)return[];c.features.length>0&&void 0===c.features[0].attributes[t.pagesDefinition.generatedOid]&&(t.pagesDefinition.generatedOid=t.pagesDefinition.generatedOid.toLowerCase());for(let e=0;e<c.features.length;e++)t.pagesDefinition.internal.set[o+e]=c.features[e].attributes[t.pagesDefinition.generatedOid];for(let a=0;a<c.features.length;a++)h.push(new e({attributes:c.features[a].attributes,geometry:null}));return!0===t.pagesDefinition.useOIDpagination?0===c.features.length?t.pagesDefinition.internal.fullyResolved=!0:t.pagesDefinition.internal.lastMaxId=c.features[c.features.length-1].attributes[t.pagesDefinition.generatedOid]:(void 0===c.exceededTransferLimit&&c.features.length!==t.pagesDefinition.resultRecordCount||!1===c.exceededTransferLimit)&&(t.pagesDefinition.internal.fullyResolved=!0),t.pagesDefinition.internal.lastRetrieved=l+c.features.length,t.pagesDefinition.internal.lastPage+=t.pagesDefinition.resultRecordCount,h}static create(e,t,r,i,s){const a=new S({url:e,outFields:null===t?["*"]:t});return new P({layer:a,spatialReference:r,lrucache:i,interceptor:s})}relationshipMetaData(){return this._layer&&this._layer.source&&this._layer.source.sourceJSON?.relationships?this._layer.source.sourceJSON.relationships:[]}serviceUrl(){return p(this._layer.parsedUrl.path)}async queryAttachments(e,t,r,a,n){function l(e){const t=e.capabilities;return t?.data.supportsAttachment&&t?.operations.supportsQueryAttachments}const o=this._layer;if(l(o)){const l={objectIds:[e],returnMetadata:n};(t&&t>0||r&&r>0)&&(l.size=[t&&t>0?t:0,r&&r>0?r:t+1]),a&&a.length>0&&(l.attachmentTypes=a),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:o,query:l,method:"attachments"});const u=await o.queryAttachments(new F(l)),d=[];return u&&u[e]&&u[e].forEach((t=>{const r=this._layer.parsedUrl.path+"/"+e.toString()+"/attachments/"+t.id.toString();let a=null;n&&t.exifInfo&&(a=s.convertJsonToArcade(t.exifInfo,"system",!0)),d.push(new i(t.id,t.name,t.contentType,t.size,r,a,t.keywords??null))})),d}return[]}async queryRelatedFeatures(t){const i={f:"json",relationshipId:t.relationshipId.toString(),definitionExpression:t.where,outFields:t.outFields?.join(","),returnGeometry:t.returnGeometry.toString()};void 0!==t.resultOffset&&null!==t.resultOffset&&(i.resultOffset=t.resultOffset.toString()),void 0!==t.resultRecordCount&&null!==t.resultRecordCount&&(i.resultRecordCount=t.resultRecordCount.toString()),t.orderByFields&&(i.orderByFields=t.orderByFields.join(",")),t.objectIds&&t.objectIds.length>0&&(i.objectIds=t.objectIds.join(",")),t.outSpatialReference&&(i.outSR=b(t.outSpatialReference)),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preRequestCallback({layer:this._layer,queryPayload:i,method:"relatedrecords",url:this._layer.parsedUrl.path+"/queryRelatedRecords"});const s=await r(this._layer.parsedUrl.path+"/queryRelatedRecords",{responseType:"json",query:i});if(s.data){const t={},r=s.data;if(r?.relatedRecordGroups){const i=r.spatialReference;for(const s of r.relatedRecordGroups){const a=s.objectId,n=[];for(const t of s.relatedRecords){t.geometry&&(t.geometry.spatialReference=i);const r=new e({geometry:t.geometry?w(t.geometry):null,attributes:t.attributes});n.push(r)}t[a]={features:n,exceededTransferLimit:!0===r.exceededTransferLimit}}}return t}throw new a(n.InvalidRequest)}async getFeatureByObjectId(e,t){const r=this._createQuery();r.outFields=t,r.returnGeometry=!1,r.outSpatialReference=this.spatialReference,r.where=R(r.where,this.objectIdField+"="+e.toString()),this.featureSetQueryInterceptor&&this.featureSetQueryInterceptor.preLayerQueryCallback({layer:this._layer,query:r,method:"execute"});const i=await this._layer.queryFeatures(r);return 1===i.features.length?i.features[0]:null}async getIdentityUser(){await this.load();const e=t?.findCredential(this._layer.url);return e?e.userId:null}async getOwningSystemUrl(){await this.load();const e=t?.findServerInfo(this._layer.url);if(e)return e.owningSystemUrl;let i=this._layer.url;const s=i.toLowerCase().indexOf("/rest/services");if(i=s>-1?i.slice(0,s):i,i){i+="/rest/info";try{const e=await r(i,{query:{f:"json"}});let t="";return e.data?.owningSystemUrl&&(t=e.data.owningSystemUrl),t}catch(a){return""}}return""}getDataSourceFeatureSet(){const e=new P({layer:this._layer,spatialReference:this.spatialReference??void 0,outFields:this._overrideFields??void 0,includeGeometry:!this._removeGeometry,lrucache:this.recentlyUsedQueries??void 0,interceptor:this.featureSetQueryInterceptor??void 0});return e._useDefinitionExpression=!1,e}get preferredTimeZone(){return this._layer.preferredTimeZone??null}get dateFieldsTimeZone(){return this._layer.dateFieldsTimeZone??null}get datesInUnknownTimezone(){return this._layer.datesInUnknownTimezone??!1}get editFieldsInfo(){return this._layer.editFieldsInfo??null}get timeInfo(){return this._layer.timeInfo??null}async getFeatureSetInfo(){if(this.fsetInfo)return this.fsetInfo;let e=null,t="serviceItemId"in this._layer?this._layer.serviceItemId:null;const i=this._layer.parsedUrl.path;if(i){const s=await r(i,{responseType:"json",query:{f:"json"}});e=s?.data?.name??null,t=s?.data?.serviceItemId??null}const s=this._layer.title&&null!==(this._layer.parent??null);return this.featureSetInfo={layerId:this._layer.layerId,layerName:""===e?null:e,itemId:""===t?null:t,serviceLayerUrl:""===i?null:i,webMapLayerId:s?this._layer.id??null:null,webMapLayerTitle:s?this._layer.title??null:null,className:null,objectClassId:null},this.fsetInfo}}export{P as default};
