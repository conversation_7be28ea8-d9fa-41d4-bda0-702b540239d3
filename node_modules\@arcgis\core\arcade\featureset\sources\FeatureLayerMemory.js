/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{FeatureSetError as t,FeatureSetErrorCodes as s}from"../support/errorsupport.js";import r from"../support/FeatureSet.js";import i from"../support/IdSet.js";import{defaultMaxRecords as a,FeatureServiceDatabaseType as l,IdState as n}from"../support/shared.js";import{toWhereClause as o}from"../support/sqlUtils.js";import{sqlAnd as u}from"../../../core/sql.js";import h from"../../../geometry/Geometry.js";import y from"../../../layers/FeatureLayer.js";import c from"../../../layers/support/FeatureType.js";import d from"../../../layers/support/Field.js";class p extends r{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerMemory",this._removeGeometry=!1,this._overrideFields=null,this._forceIsTable=!1,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,!0===e.isTable&&(this._forceIsTable=!0),void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return a}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){return!0===this._layer.loaded?(this._initialiseFeatureSet(),this):(await this._layer.load(),this._initialiseFeatureSet(),this)}get gdbVersion(){return""}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._layer.geometryType??"",this.fields=this._layer.fields.slice(),null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const s of this.fields)if("oid"===s.type||this._layer.objectIdField===s.name)e.push(s),t.push(s.name);else for(const r of this._overrideFields)if(r.toLowerCase()===s.name.toLowerCase()){e.push(s),t.push(s.name);break}this.fields=e,this._overrideFields=t}this.objectIdField=this._layer.objectIdField;for(const e of this.fields)"global-id"===e.type&&(this.globalIdField=e.name);this._databaseType=l.Standardised,this.hasZ=!0===this._layer?.capabilities?.data?.supportsZ,this.hasM=!0===this._layer?.capabilities?.data?.supportsM,this.subtypeField=("subtypeField"in this._layer?this._layer.subtypeField:null)??"",this.subtypes="subtypes"in this._layer?this._layer.subtypes:null,this.typeIdField=("typeIdField"in this._layer?this._layer.typeIdField:null)??"",this.types="types"in this._layer?this._layer.types:null}isTable(){return this._forceIsTable||"isTable"in this._layer&&this._layer.isTable||"table"===this._layer.type||!this._layer.geometryType}_isInFeatureSet(){return n.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_createQuery(){const e=this._layer.createQuery();return e.returnZ=this.hasZ,e.returnM=this.hasM,e.outFields=this._overrideFields??["*"],e.returnGeometry=!this._removeGeometry,e}_changeFeature(t){const s={};for(const e of this.fields)s[e.name]=t.attributes[e.name];return new e({geometry:!0===this._removeGeometry?null:t.geometry,attributes:s})}async _getFilteredSet(e,t,s,r,a){let n="",h=!1;if(null!==r&&(n=r.constructClause(),h=!0),this.isTable()&&t&&null!==e&&""!==e){return new i([],[],!0,null)}const y=this._createQuery();y.where=u(y.where,null===s?null===t?"1=1":"":o(s,l.Standardised)),y.spatialRelationship=this._makeRelationshipEnum(e),y.outSpatialReference=this.spatialReference,y.orderByFields=""!==n?n.split(","):null,y.geometry=null===t?null:t,y.relationParameter=this._makeRelationshipParam(e);const c=await this._layer.queryFeatures(y);if(null===c)return new i([],[],h,null);this._checkCancelled(a);const d=[];c.features.forEach((e=>{const t=e.attributes[this._layer.objectIdField];d.push(t),this._featureCache[t]=this._changeFeature(e)}));return new i([],d,h,null)}_makeRelationshipEnum(e){if(e.includes("esriSpatialRelRelation"))return"relation";switch(e){case"esriSpatialRelRelation":return"relation";case"esriSpatialRelIntersects":return"intersects";case"esriSpatialRelContains":return"contains";case"esriSpatialRelOverlaps":return"overlaps";case"esriSpatialRelWithin":return"within";case"esriSpatialRelTouches":return"touches";case"esriSpatialRelCrosses":return"crosses";case"esriSpatialRelEnvelopeIntersects":return"envelope-intersects"}return e}_makeRelationshipParam(e){return e.includes("esriSpatialRelRelation")?e.split(":")[1]:""}async _queryAllFeatures(){if(this._wset)return this._wset;if(await this._ensureLoaded(),this._layer.source&&this._layer.source.items){const e=[];return this._layer.source.items.forEach((t=>{const s=t.attributes[this._layer.objectIdField];e.push(s),this._featureCache[s]=this._changeFeature(t)})),this._wset=new i([],e,!1,null),this._wset}const e=this._createQuery();e.where="1=1";const t=await this._layer.queryFeatures(e),s=[];return t.features.forEach((e=>{const t=e.attributes[this._layer.objectIdField];s.push(t),this._featureCache[t]=this._changeFeature(e)})),this._wset=new i([],s,!1,null),this._wset}async _getFeatures(e,r,i){const a=[];-1!==r&&void 0===this._featureCache[r]&&a.push(r);for(let t=e._lastFetchedIndex;t<e._known.length&&(e._lastFetchedIndex+=1,!(void 0===this._featureCache[e._known[t]]&&(e._known[t]!==r&&a.push(e._known[t]),a.length>i)));t++);if(0===a.length)return"success";throw new t(s.MissingFeatures)}async _refineSetBlock(e){return e}async _stat(){return{calculated:!1}}async _canDoAggregates(){return!1}relationshipMetaData(){return[]}static _cloneAttr(e){const t={};for(const s in e)t[s]=e[s];return t}nativeCapabilities(){return{title:this._layer.title??"",canQueryRelated:!1,source:this,capabilities:this._layer.capabilities,databaseType:this._databaseType,requestStandardised:!0}}static create(e,t){let s=e.layerDefinition.objectIdField;const r=e.layerDefinition.typeIdField??"",i=[];if(e.layerDefinition.types)for(const h of e.layerDefinition.types)i.push(c.fromJSON(h));let a=e.layerDefinition.geometryType;void 0===a&&(a=e.featureSet.geometryType||"");let l=e.featureSet.features;const n=t.toJSON();if(!s){let t=!1;for(const r of e.layerDefinition.fields)if("oid"===r.type||"esriFieldTypeOID"===r.type){s=r.name,t=!0;break}if(!1===t){let t="FID",r=!0,i=0;for(;r;){let s=!0;for(const r of e.layerDefinition.fields)if(r.name===t){s=!1;break}!0===s?r=!1:(i++,t="FID"+i.toString())}e.layerDefinition.fields.push({type:"esriFieldTypeOID",name:t,alias:t});const a=[];for(let s=0;s<l.length;s++)a.push({geometry:e.featureSet.features[s].geometry,attributes:e.featureSet.features[s].attributes?this._cloneAttr(e.featureSet.features[s].attributes):{}}),a[s].attributes[t]=s;l=a,s=t}}const o=[];for(const h of e.layerDefinition.fields)h instanceof d?o.push(h):o.push(d.fromJSON(h));let u=a;switch(u||(u=""),u){case"esriGeometryPoint":u="point";break;case"esriGeometryPolyline":u="polyline";break;case"esriGeometryPolygon":u="polygon";break;case"esriGeometryEnvelope":u="extent";break;case"esriGeometryMultipoint":u="multipoint";break;case"":case"esriGeometryNull":u="esriGeometryNull"}if("esriGeometryNull"!==u)for(const y of l)y.geometry&&y.geometry instanceof h==!1&&(y.geometry.type=u,void 0===y.geometry.spatialReference&&(y.geometry.spatialReference=n));else for(const h of l)h.geometry&&(h.geometry=null);const f={outFields:["*"],source:l,fields:o,hasZ:!0===e?.layerDefinition?.hasZ||!0===e?.featureSet?.hasZ,hasM:!0===e?.layerDefinition?.hasM||!0===e?.featureSet?.hasM,types:i,typeIdField:r,objectIdField:s,spatialReference:t},_="esriGeometryNull"===u||null===u;_||(f.geometryType=u);const m=new y(f);e?.layerDefinition?.subtypeField&&e?.layerDefinition?.subtypes&&m.read({subtypes:e.layerDefinition.subtypes,subtypeField:e.layerDefinition.subtypeField});return new p({layer:m,spatialReference:t,isTable:_})}async queryAttachments(){return[]}async getFeatureByObjectId(e){const t=this._createQuery();t.where=this.objectIdField+"="+e.toString();const s=await this._layer.queryFeatures(t);return 1===s.features.length?s.features[0]:null}async getOwningSystemUrl(){return""}async getIdentityUser(){return""}get preferredTimeZone(){return"preferredTimeZone"in this._layer?this._layer.preferredTimeZone:null}get dateFieldsTimeZone(){return"dateFieldsTimeZone"in this._layer?this._layer.dateFieldsTimeZone:null}get datesInUnknownTimezone(){return"datesInUnknownTimezone"in this._layer&&this._layer.datesInUnknownTimezone}get editFieldsInfo(){return"editFieldsInfo"in this._layer?this._layer.editFieldsInfo:null}get timeInfo(){return this._layer?.timeInfo}async getFeatureSetInfo(){const e=this._layer.title&&this._layer.parent;return this.fsetInfo??{layerId:null,layerName:null,itemId:null,serviceLayerUrl:null,webMapLayerId:e?this._layer.id??null:null,webMapLayerTitle:e?this._layer.title??null:null,className:null,objectClassId:null}}}export{p as default};
