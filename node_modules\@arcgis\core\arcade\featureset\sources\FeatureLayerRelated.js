/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../../Graphic.js";import{FeatureSetError as t,FeatureSetErrorCodes as r}from"../support/errorsupport.js";import i from"../support/FeatureSet.js";import s from"../support/IdSet.js";import{defaultMaxRecords as a,IdState as n}from"../support/shared.js";import l from"../../../rest/support/RelationshipQuery.js";class d extends i{constructor(e){super(e),this.declaredClass="esri.arcade.featureset.sources.FeatureLayerRelated",this._findObjectId=-1,this._requestStandardised=!1,this._removeGeometry=!1,this._overrideFields=null,this.featureObjectId=null,e.spatialReference&&(this.spatialReference=e.spatialReference),this._transparent=!0,this._maxProcessing=1e3,this._layer=e.layer,this._wset=null,this._findObjectId=e.objectId,this.featureObjectId=e.objectId,this.relationship=e.relationship,this._relatedLayer=e.relatedLayer,void 0!==e.outFields&&(this._overrideFields=e.outFields),void 0!==e.includeGeometry&&(this._removeGeometry=!1===e.includeGeometry)}_maxQueryRate(){return a}end(){return this._layer}optimisePagingFeatureQueries(){}async loadImpl(){return await Promise.all([this._layer.load(),this._relatedLayer?.load()]),this._initialiseFeatureSet(),this}nativeCapabilities(){return this._relatedLayer.nativeCapabilities()}_initialiseFeatureSet(){if(null==this.spatialReference&&(this.spatialReference=this._layer.spatialReference),this.geometryType=this._relatedLayer.geometryType,this.fields=this._relatedLayer.fields.slice(),this.hasZ=this._relatedLayer.hasZ,this.hasM=this._relatedLayer.hasM,null!==this._overrideFields)if(1===this._overrideFields.length&&"*"===this._overrideFields[0])this._overrideFields=null;else{const e=[],t=[];for(const r of this.fields)if("oid"===r.type)e.push(r),t.push(r.name);else for(const i of this._overrideFields)if(i.toLowerCase()===r.name.toLowerCase()){e.push(r),t.push(r.name);break}this.fields=e,this._overrideFields=t}const e=this._layer.nativeCapabilities();e&&(this._databaseType=e.databaseType,this._requestStandardised=e.requestStandardised),this.objectIdField=this._relatedLayer.objectIdField,this.globalIdField=this._relatedLayer.globalIdField,this.hasM=this._relatedLayer.supportsM,this.hasZ=this._relatedLayer.supportsZ,this.typeIdField=this._relatedLayer.typeIdField,this.types=this._relatedLayer.types,this.subtypeField=this._relatedLayer.subtypeField,this.subtypes=this._relatedLayer.subtypes}async databaseType(){return await this._relatedLayer.databaseType(),this._databaseType=this._relatedLayer._databaseType,this._databaseType}isTable(){return this._relatedLayer.isTable()}_isInFeatureSet(){return n.InFeatureSet}_candidateIdTransform(e){return e}async _getSet(e){if(null===this._wset){await this._ensureLoaded();const t=await this._getFilteredSet("",null,null,null,e);return this._wset=t,t}return this._wset}_changeFeature(t){const r={};for(const e of this.fields)r[e.name]=t.attributes[e.name];return new e({geometry:!0===this._removeGeometry?null:t.geometry,attributes:r})}async _getFilteredSet(e,t,r,i,a){if(await this.databaseType(),this.isTable()&&t&&null!==e&&""!==e){return new s([],[],!0,null)}const n=this._layer.nativeCapabilities();if(!1===n.canQueryRelated){return new s([],[],!0,null)}if(n.capabilities?.queryRelated.supportsPagination)return this._getFilteredSetUsingPaging(e,t,r,i,a);let d="",o=!1;null!==i&&!0===n.capabilities?.queryRelated.supportsOrderBy&&(d=i.constructClause(),o=!0);const u=new l;u.objectIds=[this._findObjectId];const h=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map((e=>e.name)):["*"]);u.outFields=h,u.relationshipId=this.relationship.id,u.where="1=1";let c=!0;!0===this._removeGeometry&&(c=!1),u.returnGeometry=c,this._requestStandardised&&(u.sqlFormat="standard"),u.outSpatialReference=this.spatialReference,u.orderByFields=""!==d?d.split(","):null;const y=await n.source.queryRelatedFeatures(u);this._checkCancelled(a);const p=y[this._findObjectId]?y[this._findObjectId].features:[],_=[];for(let s=0;s<p.length;s++)this._featureCache[p[s].attributes[this._relatedLayer.objectIdField]]=p[s],_.push(p[s].attributes[this._relatedLayer.objectIdField]);const f=t&&null!==e&&""!==e,g=null!=r;return new s(f||g?_:[],f||g?[]:_,o,null)}_fieldsIncludingObjectId(e){if(null===e)return[this.objectIdField];const t=e.slice();if(t.includes("*"))return t;let r=!1;for(const i of t)if(i.toUpperCase()===this.objectIdField.toUpperCase()){r=!0;break}return!1===r&&t.push(this.objectIdField),t}async _getFilteredSetUsingPaging(e,t,r,i,a){let n="",l=!1;const d=this._layer.nativeCapabilities();null!==i&&!0===d.capabilities?.queryRelated.supportsOrderBy&&(n=i.constructClause(),l=!0),await this.databaseType();const o="1=1";let u=this._maxQueryRate();const h=d.capabilities?.query.maxRecordCount;null!=h&&h<u&&(u=h);const c=t&&null!==e&&""!==e,y=null!=r;let p=null,_=!0;!0===this._removeGeometry&&(_=!1);const f=null!==this._overrideFields?this._overrideFields:this._fieldsIncludingObjectId(this._relatedLayer.fields?this._relatedLayer.fields.map((e=>e.name)):["*"]);return p=new s(c||y?["GETPAGES"]:[],c||y?[]:["GETPAGES"],l,{outFields:f.join(","),resultRecordCount:u,resultOffset:0,objectIds:[this._findObjectId],where:o,orderByFields:n,returnGeometry:_,returnIdsOnly:"false",internal:{set:[],lastRetrieved:0,lastPage:0,fullyResolved:!1}}),await this._expandPagedSet(p,u,0,0,a),p}_expandPagedSet(e,t,r,i,s){return this._expandPagedSetFeatureSet(e,t,r,i,s)}_clonePageDefinition(e){return null===e?null:!0!==e.groupbypage?{groupbypage:!1,outFields:e.outFields,resultRecordCount:e.resultRecordCount,resultOffset:e.resultOffset,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}:{groupbypage:!0,outFields:e.outFields,resultRecordCount:e.resultRecordCount,useOIDpagination:e.useOIDpagination,generatedOid:e.generatedOid,groupByFieldsForStatistics:e.groupByFieldsForStatistics,resultOffset:e.resultOffset,outStatistics:e.outStatistics,geometry:e.geometry,where:e.where,objectIds:e.objectIds,orderByFields:e.orderByFields,returnGeometry:e.returnGeometry,returnIdsOnly:e.returnIdsOnly,internal:e.internal}}async _getPhysicalPage(e,t,r){const i=e.pagesDefinition.internal.lastRetrieved,s=i,a=e.pagesDefinition.internal.lastPage,n=this._layer.nativeCapabilities(),d=new l;!0===this._requestStandardised&&(d.sqlFormat="standard"),d.relationshipId=this.relationship.id,d.objectIds=e.pagesDefinition.objectIds,d.resultOffset=e.pagesDefinition.internal.lastPage,d.resultRecordCount=e.pagesDefinition.resultRecordCount,d.outFields=e.pagesDefinition.outFields.split(","),d.where=e.pagesDefinition.where,d.orderByFields=""!==e.pagesDefinition.orderByFields?e.pagesDefinition.orderByFields.split(","):null,d.returnGeometry=e.pagesDefinition.returnGeometry,d.outSpatialReference=this.spatialReference;const o=await n.source.queryRelatedFeatures(d);if(this._checkCancelled(r),e.pagesDefinition.internal.lastPage!==a)return 0;const u=o[this._findObjectId]?o[this._findObjectId].features:[];for(let l=0;l<u.length;l++)e.pagesDefinition.internal.set[s+l]=u[l].attributes[this._relatedLayer.objectIdField];for(let l=0;l<u.length;l++)this._featureCache[u[l].attributes[this._relatedLayer.objectIdField]]=u[l];const h=!o[this._findObjectId]||!1===o[this._findObjectId].exceededTransferLimit;return u.length!==e.pagesDefinition.resultRecordCount&&h&&(e.pagesDefinition.internal.fullyResolved=!0),e.pagesDefinition.internal.lastRetrieved=i+u.length,e.pagesDefinition.internal.lastPage+=e.pagesDefinition.resultRecordCount,u.length}async _getFeatures(e,i,s,a){const n=[];-1!==i&&void 0===this._featureCache[i]&&n.push(i);const l=this._maxQueryRate();if(!0===this._checkIfNeedToExpandKnownPage(e,l))return await this._expandPagedSet(e,l,0,0,a),this._getFeatures(e,i,s,a);let d=0;for(let t=e._lastFetchedIndex;t<e._known.length&&(d++,d<=s&&(e._lastFetchedIndex+=1),!("GETPAGES"!==e._known[t]&&void 0===this._featureCache[e._known[t]]&&(e._known[t]!==i&&n.push(e._known[t]),n.length>s)))&&!(d>=s&&0===n.length);t++);if(0===n.length)return"success";throw new t(r.MissingFeatures)}async _refineSetBlock(e,t,r){return e}async _stat(e,t,r,i,s,a,n){return{calculated:!1}}get gdbVersion(){return this._relatedLayer.gdbVersion}async _canDoAggregates(e,t,r,i,s){return!1}relationshipMetaData(){return this._relatedLayer.relationshipMetaData()}serviceUrl(){return this._relatedLayer.serviceUrl()}queryAttachments(e,t,r,i,s){return this._relatedLayer.queryAttachments(e,t,r,i,s)}getFeatureByObjectId(e,t){return this._relatedLayer.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._relatedLayer.getOwningSystemUrl()}getIdentityUser(){return this._relatedLayer.getIdentityUser()}getDataSourceFeatureSet(){return this._relatedLayer}get preferredTimeZone(){return this._relatedLayer?.preferredTimeZone??null}get dateFieldsTimeZone(){return this._relatedLayer?.dateFieldsTimeZone??null}get datesInUnknownTimezone(){return this._relatedLayer?.datesInUnknownTimezone}get editFieldsInfo(){return this._relatedLayer?.editFieldsInfo??null}get timeInfo(){return this._relatedLayer?.timeInfo??null}async getFeatureSetInfo(){return this.fsetInfo??this._layer.featureSetInfo}}export{d as default};
