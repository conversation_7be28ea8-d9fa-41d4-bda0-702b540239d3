/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"./cache.js";import{FeatureSetError as t,FeatureSetErrorCodes as n}from"./errorsupport.js";import s from"./FeatureSetIterator.js";import i from"./IdSet.js";import{FeatureServiceDatabaseType as a,layerGeometryEsriConstants as r,IdState as l,esri<PERSON>ieldTo<PERSON>son as u,layerGeometryEsriRestConstants as h}from"./shared.js";import{max as o,min as c,sum as d,variance as f,stdev as _,mean as p,distinct as m,count as g}from"./stats.js";import{invokeRemoteGeometryOp as y}from"../../geometry/operatorsWorkerConnection.js";import{isPromiseLike as F}from"../../../core/promiseUtils.js";import I from"../../../core/sql/WhereClause.js";import T from"../../../geometry/SpatialReference.js";import{fromJSON as b}from"../../../geometry/support/jsonUtils.js";import w from"../../../layers/support/FieldsIndex.js";class S{constructor(e){this.recentlyUsedQueries=null,this.featureSetQueryInterceptor=null,this._idstates=[],this._parent=null,this._wset=null,this._mainSetInUse=null,this._maxProcessing=200,this._maxQuery=500,this._totalCount=-1,this._databaseType=a.NotEvaluated,this._databaseTypeProbed=null,this.declaredRootClass="esri.arcade.featureset.support.FeatureSet",this._featureCache=Object.create(null),this.typeIdField=null,this.types=null,this.subtypeField=null,this.subtypes=null,this.fields=null,this.geometryType="",this.objectIdField="",this.globalIdField="",this.spatialReference=null,this.hasM=!1,this.hasZ=!1,this._transparent=!1,this.loaded=!1,this._loadPromise=null,this._fieldsIndex=null,this.fsetInfo=null,e?.lrucache&&(this.recentlyUsedQueries=e.lrucache),e?.interceptor&&(this.featureSetQueryInterceptor=e.interceptor)}optimisePagingFeatureQueries(e){this._parent&&this._parent.optimisePagingFeatureQueries(e)}_hasMemorySource(){return!0}prop(e,t){return void 0===t?this[e]:(void 0!==this[e]&&(this[e]=t),this)}end(){return null!==this._parent&&!0===this._parent._transparent?this._parent.end():this._parent}_ensureLoaded(){return this.load()}load(){return null===this._loadPromise&&(this._loadPromise=this.loadImpl()),this._loadPromise}async loadImpl(){return!0===this._parent?.loaded?(this._initialiseFeatureSet(),this):(await(this._parent?.load()),this._initialiseFeatureSet(),this)}_initialiseFeatureSet(){null!==this._parent?(this.fields=this._parent.fields.slice(),this.geometryType=this._parent.geometryType,this.objectIdField=this._parent.objectIdField,this.globalIdField=this._parent.globalIdField,this.spatialReference=this._parent.spatialReference,this.hasM=this._parent.hasM,this.hasZ=this._parent.hasZ,this.typeIdField=this._parent.typeIdField,this.types=this._parent.types,this.subtypeField=this._parent.subtypeField,this.subtypes=this._parent.subtypes):(this.fields=[],this.typeIdField="",this.subtypeField="",this.objectIdField="",this.globalIdField="",this.spatialReference=new T({wkid:4326}),this.geometryType=r.point)}getField(e,t){let n;return(t=t||this.fields)&&(e=e.toLowerCase(),t.some((t=>(t&&t.name.toLowerCase()===e&&(n=t),!!n)))),n}getFieldsIndex(){return null===this._fieldsIndex&&(this._fieldsIndex=w.fromLayer({timeInfo:this.timeInfo,editFieldsInfo:this.editFieldsInfo,dateFieldsTimeZone:this.dateFieldsTimeZone,datesInUnknownTimezone:this.datesInUnknownTimezone,fields:this.fields})),this._fieldsIndex}_maxProcessingRate(){return null!==this._parent?Math.min(this._maxProcessing,this._parent._maxProcessingRate()):Math.min(this._maxProcessing,this._maxQueryRate())}_maxQueryRate(){return null!==this._parent?Math.max(this._maxQuery,this._parent._maxQueryRate()):this._maxQuery}_checkCancelled(e){if(null!=e&&e.aborted)throw new t(n.Cancelled)}nativeCapabilities(){return this._parent.nativeCapabilities()}async _canDoAggregates(e,t,n,s,i){return null!==this._parent&&this._parent._canDoAggregates(e,t,n,s,i)}async _getAggregatePagesDataSourceDefinition(e,s,i,a,r,l,u){if(null===this._parent)throw new t(n.NeverReach);return this._parent._getAggregatePagesDataSourceDefinition(e,s,i,a,r,l,u)}async _getAgregagtePhysicalPage(e,s,i){if(null===this._parent)throw new t(n.NeverReach);return this._parent._getAgregagtePhysicalPage(e,s,i)}async databaseType(){if(this._databaseType===a.NotEvaluated){if(null!==e.applicationCache){const t=e.applicationCache.getDatabaseType(this._cacheableFeatureSetSourceKey());if(null!==t)return t}if(null!==this._databaseTypeProbed)return this._databaseTypeProbed;try{this._databaseTypeProbed=this._getDatabaseTypeImpl(),null!==e.applicationCache&&e.applicationCache.setDatabaseType(this._cacheableFeatureSetSourceKey(),this._databaseTypeProbed)}catch(t){throw null!==e.applicationCache&&e.applicationCache.clearDatabaseType(this._cacheableFeatureSetSourceKey()),t}return this._databaseTypeProbed}return this._databaseType}async _getDatabaseTypeImpl(){const e=[{thetype:a.SqlServer,testwhere:"(CAST( '2015-01-01' as DATETIME) = CAST( '2015-01-01' as DATETIME)) AND OBJECTID<0"},{thetype:a.Oracle,testwhere:"(TO_DATE('2003-11-18','YYYY-MM-DD') = TO_DATE('2003-11-18','YYYY-MM-DD')) AND OBJECTID<0"},{thetype:a.StandardisedNoInterval,testwhere:"(date '2015-01-01 10:10:10' = date '2015-01-01 10:10:10') AND OBJECTID<0"}];for(const t of e){if(!0===await this._runDatabaseProbe(t.testwhere))return t.thetype}return a.StandardisedNoInterval}_cacheableFeatureSetSourceKey(){return"MUSTBESET"}async _runDatabaseProbe(e){if(null!==this._parent)return this._parent._runDatabaseProbe(e);throw new t(n.NotImplemented)}isTable(){return this._parent?.isTable()??!1}_featureFromCache(e){if(void 0!==this._featureCache[e])return this._featureCache[e]}_isInFeatureSet(e){return l.Unknown}_getSet(e){throw new t(n.NotImplemented)}async _getFeature(e,s,i){if(this._checkCancelled(i),void 0!==this._featureFromCache(s))return this._featureFromCache(s);if(await this._getFeatures(e,s,this._maxProcessingRate(),i),this._checkCancelled(i),void 0!==this._featureFromCache(s))return this._featureFromCache(s);throw new t(n.MissingFeatures)}async _getFeatureBatch(e,t){this._checkCancelled(t);const n=new i([],e,!1,null),s=[];await this._getFeatures(n,-1,e.length,t),this._checkCancelled(t);for(const i of e)void 0!==this._featureFromCache(i)&&s.push(this._featureFromCache(i));return s}async _getFeatures(e,t,n,s){return"success"}_getFilteredSet(e,s,i,a,r){throw new t(n.NotImplemented)}async _refineSetBlock(e,t,n){if(!0===this._checkIfNeedToExpandCandidatePage(e,this._maxQueryRate()))return await this._expandPagedSet(e,this._maxQueryRate(),0,0,n),this._refineSetBlock(e,t,n);this._checkCancelled(n);const s=e._candidates.length;this._refineKnowns(e,t);let i=s-e._candidates.length;if(0===e._candidates.length)return e;if(i>=t)return e;if(await this._refineIfParentKnown(e,t-i,n),this._checkCancelled(n),this._refineKnowns(e,t-i),i=s-e._candidates.length,i<t&&e._candidates.length>0){const s=t-i,a=this._prepareFetchAndRefineSet(e._candidates);return await this._fetchAndRefineFeatures(a,a.length>s?s:e._candidates.length,n),this._checkCancelled(n),this._refineKnowns(e,t-i),e}return e}_fetchAndRefineFeatures(e,t,n){return null}_prepareFetchAndRefineSet(e){const t=[];for(let n=0;n<e.length;n++)this._isPhysicalFeature(e[n])&&t.push(e[n]);return t}_isPhysicalFeature(e){return null===this._parent||this._parent._isPhysicalFeature(e)}_refineKnowns(e,t){let n=0,s=null;const i=[];t=this._maxQueryRate();for(let a=0;a<e._candidates.length&&"GETPAGES"!==e._candidates[a];a++){let r=!1;const u=this._candidateIdTransform(e._candidates[a]);u!==e._candidates[a]&&(r=!0);const h=this._isInFeatureSet(u);if(h===l.InFeatureSet)!0===r?e._known.includes(u)||(e._known.push(u),n+=1):(e._known.push(e._candidates[a]),n+=1),null===s?s={start:a,end:a}:s.end===a-1?s.end=a:(i.push(s),s={start:a,end:a});else if(h===l.NotInFeatureSet)null===s?s={start:a,end:a}:s.end===a-1?s.end=a:(i.push(s),s={start:a,end:a}),n+=1;else if(h===l.Unknown&&(n+=1,!0===e._ordered))break;if(n>=t)break}null!==s&&i.push(s);for(let a=i.length-1;a>=0;a--)e._candidates.splice(i[a].start,i[a].end-i[a].start+1)}_refineIfParentKnown(e,t,n){const s=new i([],[],e._ordered,null);return s._candidates=e._candidates.slice(),this._parent._refineSetBlock(s,t,n)}_candidateIdTransform(e){return this._parent._candidateIdTransform(e)}_checkIfNeedToExpandKnownPage(e,t){if(null===e.pagesDefinition)return!1;let n=0;for(let s=e._lastFetchedIndex;s<e._known.length;s++){if("GETPAGES"===e._known[s])return!0;if(void 0===this._featureCache[e._known[s]]&&(n+=1,n>=t))break}return!1}_checkIfNeedToExpandCandidatePage(e,t){if(null===e.pagesDefinition)return!1;let n=0;for(let s=0;s<e._candidates.length;s++){if("GETPAGES"===e._candidates[s])return!0;if(n+=1,n>=t)break}return!1}async _expandPagedSet(e,s,i,a,r){if(null===this._parent)throw new t(n.NotImplemented);return this._parent._expandPagedSet(e,s,i,a,r)}async _expandPagedSetFeatureSet(e,t,n,s,i){if(e._known.length>0&&"GETPAGES"===e._known[e._known.length-1]&&(s=1),0===s&&e._candidates.length>0&&"GETPAGES"===e._candidates[e._candidates.length-1]&&(s=2),0===s)return"finished";const a=await this._getPage(e,s,i);return n+a<t?this._expandPagedSet(e,t,n+a,0,i):"success"}async _getPage(e,t,n){const s=1===t?e._known:e._candidates;if(e.pagesDefinition.internal.set.length>e.pagesDefinition.resultOffset||!0===e.pagesDefinition.internal.fullyResolved){s.length=s.length-1;let t=0;for(let i=0;i<e.pagesDefinition.resultRecordCount&&!(e.pagesDefinition.resultOffset+i>=e.pagesDefinition.internal.set.length);i++)s[s.length]=e.pagesDefinition.internal.set[e.pagesDefinition.resultOffset+i],t++;e.pagesDefinition.resultOffset+=t;let n=!1;return!0===e.pagesDefinition.internal.fullyResolved&&e.pagesDefinition.internal.set.length<=e.pagesDefinition.resultOffset&&(n=!0),!1===n&&s.push("GETPAGES"),t}return await this._getPhysicalPage(e,t,n),this._getPage(e,t,n)}_getPhysicalPage(e,t,n){return null}_clonePageDefinition(e){return null===this._parent?null:this._parent._clonePageDefinition(e)}_first(e){return this.iterator(e).next()}first(e){return this._first(e)}async calculateStatistic(e,t,n,s){await this._ensureLoaded();let i=await this._stat(e,t,"",null,null,n,s);return!1===i.calculated&&(i=await this._manualStat(e,t,n,s)),i.result}async _manualStat(e,t,n,s){let i=null;switch(e.toLowerCase()){case"count":return i=await g(this,s),{calculated:!0,result:i};case"distinct":return i=await m(this,t,n,s),{calculated:!0,result:i};case"avg":case"mean":return i=await p(this,t,s),{calculated:!0,result:i};case"stdev":return i=await _(this,t,s),{calculated:!0,result:i};case"variance":return i=await f(this,t,s),{calculated:!0,result:i};case"sum":return i=await d(this,t,s),{calculated:!0,result:i};case"min":return i=await c(this,t,s),{calculated:!0,result:i};case"max":return i=await o(this,t,s),{calculated:!0,result:i};default:return{calculated:!0,result:0}}}async _stat(e,t,n,s,i,a,r){const l=await this._parent._stat(e,t,n,s,i,a,r);return!1===l.calculated?null===i&&""===n&&null===s?this._manualStat(e,t,a,r):{calculated:!1}:l}_unionAllGeomSelf(e){const t=this.iterator(this._defaultTracker(e)),n=[];return new Promise(((e,s)=>{this._unionShapeInBatches(n,t,e,s)}))}_unionAllGeom(e){return new Promise(((t,n)=>{const s=this.iterator(this._defaultTracker(e)),i=[];this._unionShapeInBatches(i,s,t,n)}))}_unionShapeInBatches(e,t,n,s){t.next().then((i=>{try{null!==i&&null!==i.geometry&&e.push(i.geometry),e.length>30||null===i&&e.length>1?y("union",[e.map((e=>e.toJSON()))]).then((a=>{try{null===i?n(a):(e=[b(a)],this._unionShapeInBatches(e,t,n,s))}catch(r){s(r)}}),s):null===i?1===e.length?n(e[0]):n(null):this._unionShapeInBatches(e,t,n,s)}catch(a){s(a)}}),s)}iterator(e){return new s(this,e)}intersection(e,t=!1){return S._featuresetFunctions.intersection.bind(this)(e,t)}difference(e,t=!1,n=!0){return S._featuresetFunctions.difference.bind(this)(e,t,n)}symmetricDifference(e,t=!1,n=!0){return S._featuresetFunctions.symmetricDifference.bind(this)(e,t,n)}morphShape(e,t,n="unknown",s=null){return S._featuresetFunctions.morphShape.bind(this)(e,t,n,s)}morphShapeAndAttributes(e,t,n="unknown"){return S._featuresetFunctions.morphShapeAndAttributes.bind(this)(e,t,n)}union(e,t=!1){return S._featuresetFunctions.union.bind(this)(e,t)}intersects(e){return S._featuresetFunctions.intersects.bind(this)(e)}envelopeIntersects(e){return S._featuresetFunctions.envelopeIntersects.bind(this)(e)}contains(e){return S._featuresetFunctions.contains.bind(this)(e)}overlaps(e){return S._featuresetFunctions.overlaps.bind(this)(e)}relate(e,t){return S._featuresetFunctions.relate.bind(this)(e,t)}within(e){return S._featuresetFunctions.within.bind(this)(e)}touches(e){return S._featuresetFunctions.touches.bind(this)(e)}top(e){return S._featuresetFunctions.top.bind(this)(e)}crosses(e){return S._featuresetFunctions.crosses.bind(this)(e)}buffer(e,t,n,s=!0){return S._featuresetFunctions.buffer.bind(this)(e,t,n,s)}filter(e,t=null){return S._featuresetFunctions.filter.bind(this)(e,t)}orderBy(e){return S._featuresetFunctions.orderBy.bind(this)(e)}dissolve(e,t){return S._featuresetFunctions.dissolve.bind(this)(e,t)}groupby(e,t){return S._featuresetFunctions.groupby.bind(this)(e,t)}reduce(e,t=null,n){return new Promise(((s,i)=>{this._reduceImpl(this.iterator(this._defaultTracker(n)),e,t,0,s,i,0)}))}_reduceImpl(e,t,n,s,i,a,r){try{if(++r>1e3)return void setTimeout((()=>{r=0,this._reduceImpl(e,t,n,s,i,a,r)}));e.next().then((l=>{try{if(null===l)i(n);else{const u=t(n,l,s,this);F(u)?u.then((n=>{this._reduceImpl(e,t,n,s+1,i,a,r)}),a):this._reduceImpl(e,t,u,s+1,i,a,r)}}catch(u){a(u)}}),a)}catch(l){a(l)}}removeField(e){return S._featuresetFunctions.removeField.bind(this)(e)}addField(e,t,n=null){return S._featuresetFunctions.addField.bind(this)(e,t,n)}async sumArea(e,t=!1,n){const s=this.iterator(n);let i,a=0;for(;null!=(i=await s.next());)null!=i.geometry&&(a+=t?await y("geodeticArea",[i.geometry.toJSON(),e]):await y("area",[i.geometry.toJSON(),e]));return a}async sumLength(e,t=!1,n){const s=this.iterator(n);let i,a=0;for(;null!=(i=await s.next());)null!=i.geometry&&(a+=t?await y("geodeticLength",[i.geometry.toJSON(),e]):await y("length",[i.geometry.toJSON(),e]));return a}async distinct(e,t=1e3,n=null,s){await this.load();const i=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(i,n),this.calculateStatistic("distinct",i,t,this._defaultTracker(s))}async min(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("min",s,-1,this._defaultTracker(n))}async max(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("max",s,-1,this._defaultTracker(n))}async avg(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("avg",s,-1,this._defaultTracker(n))}async sum(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("sum",s,-1,this._defaultTracker(n))}async stdev(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("stdev",s,-1,this._defaultTracker(n))}async variance(e,t=null,n){await this.load();const s=I.create(e,{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC});return D(s,t),this.calculateStatistic("variance",s,-1,this._defaultTracker(n))}async count(e){return await this.load(),this.calculateStatistic("count",I.create("1",{fieldsIndex:this.getFieldsIndex(),timeZone:this.dateFieldsTimeZoneDefaultUTC}),-1,this._defaultTracker(e))}_defaultTracker(e){return e??{aborted:!1}}forEach(e,t){return new Promise(((n,s)=>{this._forEachImpl(this.iterator(this._defaultTracker(t)),e,this,n,s,0)}))}_forEachImpl(e,t,n,s,i,a){try{if(++a>1e3)return void setTimeout((()=>{a=0,this._forEachImpl(e,t,n,s,i,a)}),0);e.next().then((r=>{try{if(null===r)s(n);else{const l=t(r);null==l?this._forEachImpl(e,t,n,s,i,a):F(l)?l.then((()=>{try{this._forEachImpl(e,t,n,s,i,a)}catch(r){i(r)}}),i):this._forEachImpl(e,t,n,s,i,a)}}catch(l){i(l)}}),i)}catch(r){i(r)}}convertToJSON(e){const t={layerDefinition:{geometryType:this.geometryType,fields:[]},featureSet:{features:[],geometryType:this.geometryType}};for(let n=0;n<this.fields.length;n++)t.layerDefinition.fields.push(u(this.fields[n]));return this.reduce(((e,n)=>{const s={geometry:n.geometry?.toJSON(),attributes:{}};for(const t in n.attributes)s.attributes[t]=n.attributes[t];return t.featureSet.features.push(s),1}),0,e).then((()=>t))}castToText(e=!1){return"object, FeatureSet"}queryAttachments(e,t,n,s,i){return this._parent.queryAttachments(e,t,n,s,i)}serviceUrl(){return this._parent.serviceUrl()}subtypeMetaData(){return this.subtypeField&&this.subtypes?{subtypeField:this.subtypeField,subtypes:this.subtypes?this.subtypes.map((e=>({name:e.name,code:e.code}))):[]}:this.typeIdField?{subtypeField:this.typeIdField,subtypes:this.types?this.types.map((e=>({name:e.name,code:e.id}))):[]}:null}relationshipMetaData(){return this._parent.relationshipMetaData()}get gdbVersion(){return this._parent?this._parent.gdbVersion:""}schema(){const e=[];for(const t of this.fields)e.push(u(t));return{objectIdField:this.objectIdField,globalIdField:this.globalIdField,geometryType:void 0===h[this.geometryType]?"esriGeometryNull":h[this.geometryType],fields:e}}async convertToText(e,t){if("schema"===e)return await this._ensureLoaded(),JSON.stringify(this.schema());if("featureset"===e){await this._ensureLoaded();const e=[];await this.reduce(((t,n)=>{const s={geometry:n.geometry?n.geometry.toJSON():null,attributes:n.attributes};return null!==s.geometry&&s.geometry.spatialReference&&delete s.geometry.spatialReference,e.push(s),1}),0,t);const n=this.schema();return n.features=e,n.spatialReference=this.spatialReference.toJSON(),JSON.stringify(n)}return this.castToText()}getFeatureByObjectId(e,t){return this._parent.getFeatureByObjectId(e,t)}getOwningSystemUrl(){return this._parent.getOwningSystemUrl()}getIdentityUser(){return this._parent.getIdentityUser()}getRootFeatureSet(){return null!==this._parent?this._parent.getRootFeatureSet():this}getDataSourceFeatureSet(){return null!==this._parent?this._parent.getDataSourceFeatureSet():this}castAsJson(e=null){return"keeptype"===e?.featureset?this:"none"===e?.featureset?null:{type:"FeatureSet"}}async castAsJsonAsync(e=null,t=null){if("keeptype"===t?.featureset)return this;if("schema"===t?.featureset)return await this._ensureLoaded(),JSON.parse(JSON.stringify(this.schema()));if("none"===t?.featureset)return null;await this._ensureLoaded();const n=[];await this.reduce(((e,s)=>{const i={geometry:s.geometry?!0===t?.keepGeometryType?s.geometry:s.geometry.toJSON():null,attributes:s.attributes};return null!==i.geometry&&i.geometry.spatialReference&&!0!==t?.keepGeometryType&&delete i.geometry.spatialReference,n.push(i),1}),0,e);const s=this.schema();return s.features=n,s.spatialReference=!0===t?.keepGeometryType?this.spatialReference:this.spatialReference?.toJSON(),s}fieldTimeZone(e){return this.getFieldsIndex().getTimeZone(e)}get preferredTimeZone(){return this._parent?.preferredTimeZone??null}get dateFieldsTimeZone(){return this._parent?.dateFieldsTimeZone??null}get dateFieldsTimeZoneDefaultUTC(){if(this.datesInUnknownTimezone)return"unknown";const e=this.dateFieldsTimeZone??"UTC";return""===e?"UTC":e}get datesInUnknownTimezone(){return this._parent.datesInUnknownTimezone}get editFieldsInfo(){return this._parent?.editFieldsInfo??null}get timeInfo(){return this._parent?.timeInfo??null}set featureSetInfo(e){this.fsetInfo=e}async getFeatureSetInfo(){return this.fsetInfo??await(this._parent?.getFeatureSetInfo())??null}}function D(e,t){if(null!==t){const n={};for(const e in t)n[e.toLowerCase()]=t[e];e.parameters=n}}S._featuresetFunctions={};export{S as default};
