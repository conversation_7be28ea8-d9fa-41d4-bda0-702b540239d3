/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import t from"../../Feature.js";class e{constructor(t,e){this._lastId=-1,this._progress=e,this._parent=t}reset(){this._lastId=-1}async nextBatchAsArcadeFeatures(e,s){const n=await this.nextBatch(e);return null===n?n:n.map((e=>t.createFromGraphicLikeObject(e.geometry,e.attributes,this._parent,s)))}nextBatch(t){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((e=>this.nextBatch(t)),(e=>this.nextBatch(t)));const e={returnpromise:null,hasset:!1},s=[];return e.returnpromise=new Promise(((n,a)=>{this._parent._getSet(this._progress).then((r=>{const i=r._known;let h=i.length-1;if("GETPAGES"===i[i.length-1]&&(h-=1),this._lastId+t>h&&i.length>0&&"GETPAGES"===i[i.length-1])return void this._parent._expandPagedSet(r,this._parent._maxQueryRate(),0,0,this._progress).then((s=>{e.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(t).then(n,a)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,a(t)}));const _=r._candidates;if(h>=this._lastId+t||0===_.length){for(let e=0;e<t;e++){const t=e+this._lastId+1;if(t>=i.length)break;s[e]=i[t]}return this._lastId+=s.length,0===s.length&&(e.hasset=!0,this._parent._mainSetInUse=null,n([])),void this._parent._getFeatureBatch(s,this._progress).then((t=>{e.hasset=!0,this._parent._mainSetInUse=null,n(t)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,a(t)}))}this._parent._refineSetBlock(r,this._parent._maxProcessingRate(),this._progress).then((()=>{e.hasset=!0,this._parent._mainSetInUse=null,this.nextBatch(t).then(n,a)}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,a(t)}))}),(t=>{e.hasset=!0,this._parent._mainSetInUse=null,a(t)}))})),!1===e.hasset&&(this._parent._mainSetInUse=e.returnpromise,e.hasset=!0),e.returnpromise}next(){if(null!==this._parent._mainSetInUse)return this._parent._mainSetInUse.then((t=>this.next()),(t=>this.next()));const t={returnpromise:null,hasset:!1};return t.returnpromise=new Promise(((e,s)=>{this._parent._getSet(this._progress).then((n=>{const a=n._known;if(this._lastId<a.length-1)"GETPAGES"===a[this._lastId+1]?this._parent._expandPagedSet(n,this._parent._maxQueryRate(),0,0,this._progress).then((e=>(t.hasset=!0,this._parent._mainSetInUse=null,this.next()))).then(e,s):(this._lastId+=1,this._parent._getFeature(n,a[this._lastId],this._progress).then((s=>{t.hasset=!0,this._parent._mainSetInUse=null,e(s)}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,s(e)})));else{n._candidates.length>0?this._parent._refineSetBlock(n,this._parent._maxProcessingRate(),this._progress).then((()=>{t.hasset=!0,this._parent._mainSetInUse=null,this.next().then(e,s)}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,s(e)})):(t.hasset=!0,this._parent._mainSetInUse=null,e(null))}}),(e=>{t.hasset=!0,this._parent._mainSetInUse=null,s(e)}))})),!1===t.hasset&&(this._parent._mainSetInUse=t.returnpromise,t.hasset=!0),t.returnpromise}async count(){if(-1!==this._parent._totalCount)return this._parent._totalCount;const t=await this._parent._getSet(this._progress),e=await this._refineAllSets(t);return this._parent._totalCount=e._known.length,this._parent._totalCount}async _refineAllSets(t){if(t._known.length>0&&"GETPAGES"===t._known[t._known.length-1])return await this._parent._expandPagedSet(t,this._parent._maxQueryRate(),0,1,this._progress),this._refineAllSets(t);if(t._candidates.length>0){if("GETPAGES"===t._known[t._candidates.length-1])return await this._parent._expandPagedSet(t,this._parent._maxQueryRate(),0,2,this._progress),this._refineAllSets(t);const e=await this._parent._refineSetBlock(t,this._parent._maxProcessingRate(),this._progress);return e._candidates.length>0?this._refineAllSets(e):e}return t}}export{e as default};
