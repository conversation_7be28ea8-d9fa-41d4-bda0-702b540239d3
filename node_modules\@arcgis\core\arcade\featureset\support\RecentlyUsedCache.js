/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
class e{constructor(e){const t=this;t._keys=[],t._values=[],t.length=0,e&&e.forEach((e=>{t.set(e[0],e[1])}))}entries(){return[].slice.call(this.keys().map(((e,t)=>[e,this._values[t]])))}keys(){return[].slice.call(this._keys)}values(){return[].slice.call(this._values)}has(e){return this._keys.includes(e)}get(e){const t=this._keys.indexOf(e);return t>-1?this._values[t]:null}deepGet(t){if(!t?.length)return null;const s=(t,a)=>null==t?null:a.length?s(t instanceof e?t.get(a[0]):t[a[0]],a.slice(1)):t;return s(this.get(t[0]),t.slice(1))}set(e,t){const s=this,a=this._keys.indexOf(e);return a>-1?s._values[a]=t:(s._keys.push(e),s._values.push(t),s.length=s._values.length),this}sortedSet(e,t,s,a){const i=this,h=this._keys.length,r=s||0,l=void 0!==a?a:h-1;if(0===h)return i._keys.push(e),i._values.push(t),i;if(e===this._keys[r])return this._values.splice(r,0,t),this;if(e===this._keys[l])return this._values.splice(l,0,t),this;if(e>this._keys[l])return this._keys.splice(l+1,0,e),this._values.splice(l+1,0,t),this;if(e<this._keys[r])return this._values.splice(r,0,t),this._keys.splice(r,0,e),this;if(r>=l)return this;const n=r+Math.floor((l-r)/2);return e<this._keys[n]?this.sortedSet(e,t,r,n-1):e>this._keys[n]?this.sortedSet(e,t,n+1,l):this}size(){return this.length}clear(){const e=this;return e._keys.length=e.length=e._values.length=0,this}delete(e){const t=this,s=t._keys.indexOf(e);return s>-1&&(t._keys.splice(s,1),t._values.splice(s,1),t.length=t._keys.length,!0)}forEach(e){this._keys.forEach(((t,s)=>{e(this._values[s],t,s)}))}map(e){return this.keys().map(((t,s)=>e(this._values[s],t,s)))}filter(e){const t=this;return t._keys.forEach(((s,a)=>{!1===e(t._values[a],s,a)&&t.delete(s)})),this}clone(){return new e(this.entries())}}class t{constructor(t=20){this._maxEntries=t,this._values=new e}delete(e){this._values.has(e)&&this._values.delete(e)}get(e){let t=null;return this._values.has(e)&&(t=this._values.get(e),this._values.delete(e),this._values.set(e,t)),t}put(e,t){if(this._values.size()>=this._maxEntries){const e=this._values.keys()[0];this._values.delete(e)}this._values.set(e,t)}}class s{constructor(e=20){this._maxEntries=e,this._cache=new t(this._maxEntries),this._layerMetadata=new t(this._maxEntries),this._serviceMetadata=new t(this._maxEntries),this._portalItems=new t(this._maxEntries),this._portalItemLayers=new t(this._maxEntries)}clear(){this._cache=new t(this._maxEntries),this._layerMetadata=new t(this._maxEntries),this._serviceMetadata=new t(this._maxEntries),this._portalItems=new t(this._maxEntries),this._portalItemLayers=new t(this._maxEntries)}addToCache(e,t){this._cache.put(e,t)}removeFromCache(e){this._cache.delete(e)}getFromCache(e){return this._cache.get(e)}getCachedLayerMetadata(e){return this._layerMetadata.get(e)}setCachedLayerMetadata(e,t){this._layerMetadata.put(e,t)}removeCachedLayerMetadata(e,t){this._layerMetadata.get(e)===t&&this._layerMetadata.delete(e)}getCachedServiceMetadata(e){return this._serviceMetadata.get(e)}setCachedServiceMetadata(e,t){this._serviceMetadata.put(e,t)}removeCachedServiceMetadata(e,t){this._serviceMetadata.get(e)===t&&this._serviceMetadata.delete(e)}getCachedPortalItem(e,t){return this._portalItems.get(`${t}:${e}`)}setCachedPortalItem(e,t,s){this._portalItems.put(`${t}:${e}`,s)}removeCachedPortalItem(e,t,s){const a=`${t}:${e}`;this._portalItems.get(a)===s&&this._portalItems.delete(a)}getCachedPortalItemLayer(e,t){return this._portalItemLayers.get(`${t}:${e}`)}setCachedPortalItemLayer(e,t,s){this._portalItemLayers.put(`${t}:${e}`,s)}removeCachedPortalItemLayer(e,t,s){const a=`${t}:${e}`;this._portalItemLayers.get(a)===s&&this._portalItemLayers.delete(a)}}export{s as default};
