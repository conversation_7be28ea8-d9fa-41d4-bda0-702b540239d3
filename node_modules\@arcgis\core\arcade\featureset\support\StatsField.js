/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{FeatureServiceDatabaseType as e}from"./shared.js";import{toWhereClauseFromTree as t}from"./sqlUtils.js";import{SqlError as r,SqlErrorCodes as n}from"../../../core/sql/errorSupport.js";import i from"../../../core/sql/WhereClause.js";function s(s){if("function"===s.parseTree.type){if(0===s.parseTree.args.value.length)return{name:s.parseTree.name,expr:null};if(s.parseTree.args.value.length>1)throw new r(n.MissingStatisticParameters);const a=i.create(t(s.parseTree.args.value[0],e.Standardised,s.parameters),{fieldsIndex:s.fieldsIndex,timeZone:s.timeZone,currentUser:s.currentUser});return{name:s.parseTree.name,expr:a}}return null}class a{constructor(){this.field="",this.tofieldname="",this.typeofstat="MIN",this.workingexpr=null}clone(){const e=new a;return e.field=this.field,e.tofieldname=this.tofieldname,e.typeofstat=this.typeofstat,e.workingexpr=this.workingexpr,e}static parseStatField(e,t,o,l){const f=new a;f.field=e;const u=i.create(t,{fieldsIndex:o,timeZone:l}),p=s(u);if(null===p)throw new r(n.UnsupportedSqlFunction,{function:""});const c=p.name.toUpperCase().trim();if("MIN"===c){if(f.typeofstat="MIN",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"min"})}else if("MAX"===c){if(f.typeofstat="MAX",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"max"})}else if("COUNT"===c)f.typeofstat="COUNT",f.workingexpr=p.expr;else if("STDEV"===c){if(f.typeofstat="STDDEV",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"stdev"})}else if("SUM"===c){if(f.typeofstat="SUM",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"sum"})}else if("MEAN"===c){if(f.typeofstat="AVG",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:c})}else if("AVG"===c){if(f.typeofstat="AVG",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"avg"})}else{if("VAR"!==c)throw new r(n.UnsupportedSqlFunction,{function:c});if(f.typeofstat="VAR",f.workingexpr=p.expr,null===u)throw new r(n.InvalidFunctionParameters,{function:"var"})}return f}toStatisticsName(){switch(this.typeofstat.toUpperCase()){case"MIN":return"min";case"MAX":return"max";case"SUM":return"sum";case"COUNT":default:return"count";case"VAR":return"var";case"STDDEV":return"stddev";case"AVG":return"avg"}}}export{a as default};
