/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{replace as e}from"../../../core/string.js";var t;!function(e){e.NeverReach="NeverReach",e.NotImplemented="NotImplemented",e.Cancelled="Cancelled",e.InvalidStatResponse="InvalidStatResponse",e.InvalidRequest="InvalidRequest",e.RequestFailed="RequestFailed",e.MissingFeatures="MissingFeatures",e.AggregationFieldNotFound="AggregationFieldNotFound",e.DataElementsNotFound="DataElementsNotFound"}(t||(t={}));const a={[t.Cancelled]:"Cancelled",[t.InvalidStatResponse]:"Invalid statistics response from service",[t.InvalidRequest]:"Invalid request",[t.RequestFailed]:"Request failed - {reason}",[t.MissingFeatures]:"Missing features",[t.AggregationFieldNotFound]:"Aggregation field not found",[t.DataElementsNotFound]:"Data elements not found on service",[t.NeverReach]:"Encountered unreachable logic",[t.NotImplemented]:"Not implemented"};class s extends Error{constructor(t,n){super(e(a[t],n)),this.declaredRootClass="esri.arcade.featureset.support.featureseterror",Error.captureStackTrace&&Error.captureStackTrace(this,s)}}export{s as FeatureSetError,t as FeatureSetErrorCodes,a as featureSetErrorMessages};
