/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as e}from"../../ArcadeDate.js";import{DateOnly as i}from"../../../core/sql/DateOnly.js";import{TimeOnly as r}from"../../../core/sql/TimeOnly.js";import t from"../../../layers/support/Field.js";import{DateTime as n}from"luxon";var o,l;function s(e){return t.fromJSON(e.toJSON())}function y(e){return e.toJSON?e.toJSON():e}function p(e){return"string"==typeof e||e instanceof String}function u(e){return"boolean"==typeof e}function a(e){return"number"==typeof e}function c(e){return Array.isArray(e)}function d(e){return e instanceof Date}function m(e){return e instanceof n}function f(i){return i instanceof e}function g(e){return e instanceof i}function T(e){return e instanceof r}function F(e,i){return e===i||!(!d(e)&&!f(e)||!d(i)&&!f(i))&&e.getTime()===i.getTime()}function G(e){const i={};for(const r in e)i[r]=e[r];return i}function S(e,i){return e===i||("point"===e&&"esriGeometryPoint"===i||("polyline"===e&&"esriGeometryPolyline"===i||("polygon"===e&&"esriGeometryPolygon"===i||("extent"===e&&"esriGeometryEnvelope"===i||("multipoint"===e&&"esriGeometryMultipoint"===i||("point"===i&&"esriGeometryPoint"===e||("polyline"===i&&"esriGeometryPolyline"===e||("polygon"===i&&"esriGeometryPolygon"===e||("extent"===i&&"esriGeometryEnvelope"===e||"multipoint"===i&&"esriGeometryMultipoint"===e)))))))))}!function(e){e[e.Standardised=0]="Standardised",e[e.StandardisedNoInterval=1]="StandardisedNoInterval",e[e.SqlServer=2]="SqlServer",e[e.Oracle=3]="Oracle",e[e.Postgres=4]="Postgres",e[e.PGDB=5]="PGDB",e[e.FILEGDB=6]="FILEGDB",e[e.NotEvaluated=7]="NotEvaluated"}(o||(o={})),function(e){e[e.InFeatureSet=0]="InFeatureSet",e[e.NotInFeatureSet=1]="NotInFeatureSet",e[e.Unknown=2]="Unknown"}(l||(l={}));const O=1e3;function v(e){return function(i){e.reject(i)}}function I(e,i){return function(){try{e.apply(null,arguments)}catch(r){i.reject(r)}}}const D={point:"point",polygon:"polygon",polyline:"polyline",multipoint:"multipoint",extent:"extent",esriGeometryPoint:"point",esriGeometryPolygon:"polygon",esriGeometryPolyline:"polyline",esriGeometryMultipoint:"multipoint",esriGeometryEnvelope:"extent",envelope:"extent"},P={point:"esriGeometryPoint",polygon:"esriGeometryPolygon",polyline:"esriGeometryPolyline",multipoint:"esriGeometryMultipoint",extent:"esriGeometryEnvelope",esriGeometryPoint:"esriGeometryPoint",esriGeometryPolygon:"esriGeometryPolygon",esriGeometryPolyline:"esriGeometryPolyline",esriGeometryMultipoint:"esriGeometryMultipoint",esriGeometryEnvelope:"esriGeometryEnvelope",envelope:"esriGeometryEnvelope"},b={"small-integer":"esriFieldTypeSmallInteger",integer:"esriFieldTypeInteger",long:"esriFieldTypeLong",single:"esriFieldTypeSingle",double:"esriFieldTypeDouble",string:"esriFieldTypeString",date:"esriFieldTypeDate","date-only":"esriFieldTypeDateOnly","time-only":"esriFieldTypeTimeOnly","timestamp-offset":"esriFieldTypeTimestampOffset",oid:"esriFieldTypeOID",geometry:"esriFieldTypeGeometry",blob:"esriFieldTypeBlob",raster:"esriFieldTypeRaster",guid:"esriFieldTypeGUID","global-id":"esriFieldTypeGlobalID",xml:"esriFieldTypeXML","big-integer":"esriFieldTypeBigInteger",esriFieldTypeSmallInteger:"esriFieldTypeSmallInteger",esriFieldTypeInteger:"esriFieldTypeInteger",esriFieldTypeLong:"esriFieldTypeLong",esriFieldTypeSingle:"esriFieldTypeSingle",esriFieldTypeDouble:"esriFieldTypeDouble",esriFieldTypeString:"esriFieldTypeString",esriFieldTypeDate:"esriFieldTypeDate",esriFieldTypeDateOnly:"esriFieldTypeDateOnly",esriFieldTypeTimeOnly:"esriFieldTypeTimeOnly",esriFieldTypeTimestampOffset:"esriFieldTypeTimestampOffset",esriFieldTypeOID:"esriFieldTypeOID",esriFieldTypeGeometry:"esriFieldTypeGeometry",esriFieldTypeBlob:"esriFieldTypeBlob",esriFieldTypeRaster:"esriFieldTypeRaster",esriFieldTypeGUID:"esriFieldTypeGUID",esriFieldTypeGlobalID:"esriFieldTypeGlobalID",esriFieldTypeXML:"esriFieldTypeXML",esriFieldTypeBigInteger:"esriFieldTypeBigInteger"};function N(e){switch(e){case"point":default:return"esriGeometryPoint";case"polygon":return"esriGeometryPolygon";case"multipoint":return"esriGeometryMultipoint";case"polyline":return"esriGeometryPolyline"}}function E(e){return void 0===e?"":e=(e=(e=e.replace(/\/featureserver\/[0-9]*/i,"/FeatureServer")).replace(/\/mapserver\/[0-9]*/i,"/MapServer")).split("?")[0]}function J(e,i){i||(i={}),"function"==typeof i&&(i={cmp:i});const r="boolean"==typeof i.cycles&&i.cycles,t=i.cmp&&(n=i.cmp,function(e){return function(i,r){const t={key:i,value:e[i]},o={key:r,value:e[r]};return n(t,o)}});var n;const o=[];return function e(i){if(i?.toJSON&&"function"==typeof i.toJSON&&(i=i.toJSON()),void 0===i)return;if("number"==typeof i)return isFinite(i)?""+i:"null";if("object"!=typeof i)return JSON.stringify(i);let n,l;if(Array.isArray(i)){for(l="[",n=0;n<i.length;n++)n&&(l+=","),l+=e(i[n])||"null";return l+"]"}if(null===i)return"null";if(o.includes(i)){if(r)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}const s=o.push(i)-1,y=Object.keys(i).sort(t?.(i));for(l="",n=0;n<y.length;n++){const r=y[n],t=e(i[r]);t&&(l&&(l+=","),l+=JSON.stringify(r)+":"+t)}return o.splice(s,1),"{"+l+"}"}(e)}function M(e){switch(e.type){case"catalog":case"csv":case"feature":case"geojson":case"knowledge-graph-sublayer":case"oriented-imagery":case"subtype-group":case"wfs":return!0;default:return!1}}function x(e){switch(e.type){case"catalog-footprint":case"subtype-sublayer":return!0;default:return M(e)}}export{o as FeatureServiceDatabaseType,l as IdState,I as callback,G as cloneAttributes,s as cloneField,O as defaultMaxRecords,F as equalityTest,v as errback,y as esriFieldToJson,E as extractServiceUrl,f as isArcadeDate,g as isArcadeDateOnly,T as isArcadeTime,c as isArray,u as isBoolean,d as isDate,m as isLuxonDate,a as isNumber,p as isString,x as isSupportedLayer,M as isSupportedSourceLayer,b as layerFieldEsriConstants,D as layerGeometryEsriConstants,P as layerGeometryEsriRestConstants,S as sameGeomType,J as stableStringify,N as toEsriGeometryType};
