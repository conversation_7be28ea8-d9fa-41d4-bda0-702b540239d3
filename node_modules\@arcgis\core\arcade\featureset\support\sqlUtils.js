/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as e}from"../../ArcadeDate.js";import{FeatureServiceDatabaseType as r,isDate as t,isLuxonDate as a,isArcadeTime as s,isArcadeDate as n,isArcadeDateOnly as c}from"./shared.js";import{SqlError as i,SqlErrorCodes as o}from"../../../core/sql/errorSupport.js";import{TimeOnly as u}from"../../../core/sql/TimeOnly.js";import l from"../../../core/sql/WhereClause.js";import{DateTime as d}from"luxon";function f(e,r){return y(e?.parseTree,r,e?.parameters)}function m(e,r,t){return y(e,r,t)}function p(e,t,a,s){const n=y(e.parseTree,r.Standardised,e.parameters,t,a);return l.create(n,{fieldsIndex:s,timeZone:e.timeZone,currentUser:e.currentUser})}function g(e,t,a="AND"){return l.create("(("+f(e,r.Standardised)+")"+a+"("+f(t,r.Standardised)+"))",{fieldsIndex:e.fieldsIndex,timeZone:e.timeZone,currentUser:e.currentUser})}function h(e){return!0===e.delimited?`"${e.column.split('"').join('""')}"`:e.column}function y(e,u,l,d=null,f=null){let m,p,g,w;switch(e.type){case"interval":return F(y(e.value,u,l,d,f),e.qualifier,e.op);case"case-expression":{let r=" CASE ";"simple"===e.format&&(r+=y(e.operand,u,l,d,f));for(let t=0;t<e.clauses.length;t++)r+=" WHEN "+y(e.clauses[t].operand,u,l,d,f)+" THEN "+y(e.clauses[t].value,u,l,d,f);return null!==e.else&&(r+=" ELSE "+y(e.else,u,l,d,f)),r+=" END ",r}case"parameter":{const r=l[e.value.toLowerCase()];if("string"==typeof r){return"'"+l[e.value.toLowerCase()].toString().replaceAll("'","''")+"'"}if(t(r))return I(r,u);if(a(r))return I(r,u);if(s(r))return A(r,u);if(n(r))return S(r,u);if(c(r))return E(r,u);if(Array.isArray(r)){const e=[];for(let i=0;i<r.length;i++)"string"==typeof r[i]?e.push("'"+r[i].toString().replaceAll("'","''")+"'"):t(r[i])||a(r[i])?e.push(I(r[i],u)):s(r[i])?e.push(A(r[i],u)):n(r[i])?e.push(S(r[i],u)):c(r[i])?e.push(E(r[i],u)):e.push(r[i].toString());return e}return r.toString()}case"expression-list":p=[];for(const r of e.value)p.push(y(r,u,l,d,f));return p;case"unary-expression":return" ( NOT "+y(e.expr,u,l,d,f)+" ) ";case"binary-expression":switch(e.operator){case"AND":return" ("+y(e.left,u,l,d,f)+" AND "+y(e.right,u,l,d,f)+") ";case"OR":return" ("+y(e.left,u,l,d,f)+" OR "+y(e.right,u,l,d,f)+") ";case"IS":if("null"!==e.right.type)throw new i(o.UnsupportedIsRhs);return" ("+y(e.left,u,l,d,f)+" IS NULL )";case"ISNOT":if("null"!==e.right.type)throw new i(o.UnsupportedIsRhs);return" ("+y(e.left,u,l,d,f)+" IS NOT NULL )";case"IN":return m=[],"expression-list"===e.right.type?(m=y(e.right,u,l,d,f)," ("+y(e.left,u,l,d,f)+" IN ("+m.join(",")+")) "):(w=y(e.right,u,l,d,f),Array.isArray(w)?" ("+y(e.left,u,l,d,f)+" IN ("+w.join(",")+")) ":" ("+y(e.left,u,l,d,f)+" IN ("+w+")) ");case"NOT IN":return m=[],"expression-list"===e.right.type?(m=y(e.right,u,l,d,f)," ("+y(e.left,u,l,d,f)+" NOT IN ("+m.join(",")+")) "):(w=y(e.right,u,l,d,f),Array.isArray(w)?" ("+y(e.left,u,l,d,f)+" NOT IN ("+w.join(",")+")) ":" ("+y(e.left,u,l,d,f)+" NOT IN ("+w+")) ");case"BETWEEN":return g=y(e.right,u,l,d,f)," ("+y(e.left,u,l,d,f)+" BETWEEN "+g[0]+" AND "+g[1]+" ) ";case"NOTBETWEEN":return g=y(e.right,u,l,d,f)," ("+y(e.left,u,l,d,f)+" NOT BETWEEN "+g[0]+" AND "+g[1]+" ) ";case"LIKE":return""!==e.escape?" ("+y(e.left,u,l,d,f)+" LIKE "+y(e.right,u,l,d,f)+" ESCAPE '"+e.escape+"') ":" ("+y(e.left,u,l,d,f)+" LIKE "+y(e.right,u,l,d,f)+") ";case"NOT LIKE":return""!==e.escape?" ("+y(e.left,u,l,d,f)+" NOT LIKE "+y(e.right,u,l,d,f)+" ESCAPE '"+e.escape+"') ":" ("+y(e.left,u,l,d,f)+" NOT LIKE "+y(e.right,u,l,d,f)+") ";case"<>":case"<":case">":case">=":case"<=":case"=":case"*":case"-":case"+":case"/":return" ("+y(e.left,u,l,d,f)+" "+e.operator+" "+y(e.right,u,l,d,f)+") ";case"||":return" ("+y(e.left,u,l,d,f)+" "+(u===r.SqlServer?"+":e.operator)+" "+y(e.right,u,l,d,f)+") "}throw new i(o.UnsupportedOperator,{operator:e.operator});case"null":return"null";case"boolean":return!0===e.value?"1":"0";case"string":return"'"+e.value.toString().replaceAll("'","''")+"'";case"timestamp":return`timestamp '${e.value}'`;case"date":return`date '${e.value}'`;case"time":return`time '${e.value}'`;case"number":return e.value.toString();case"current-time":return L(e.mode,u);case"current-user":return"CURRENT_USER";case"column-reference":return d&&d.toLowerCase()===e.column.toLowerCase()?"("+f+")":h(e);case"data-type":return e.value;case"function":{const r=y(e.args,u,l,d,f);return T(e.name,r,u)}}throw new i(o.UnsupportedSyntax,{node:e.type})}function T(e,t,a){switch(e.toLowerCase().trim()){case"cos":case"sin":case"tan":case"cosh":case"tanh":case"sinh":case"acos":case"asin":case"atan":case"floor":case"log10":case"log":case"abs":if(1!==t.length)throw new i(o.InvalidFunctionParameters,{function:e.toLowerCase().trim()});return`${e.toUpperCase().trim()}(${t[0]})`;case"ceiling":case"ceil":if(1!==t.length)throw new i(o.InvalidFunctionParameters,{function:"ceiling"});switch(a){case r.Standardised:case r.StandardisedNoInterval:}return"CEILING("+t[0]+")";case"mod":case"power":case"nullif":if(2!==t.length)throw new i(o.InvalidFunctionParameters,{function:e.toLowerCase().trim()});return`${e.toUpperCase().trim()}(${t[0]},${t[1]})`;case"round":if(2===t.length)return"ROUND("+t[0]+","+t[1]+")";if(1===t.length)return"ROUND("+t[0]+")";throw new i(o.InvalidFunctionParameters,{function:"round"});case"truncate":if(t.length<1||t.length>2)throw new i(o.InvalidFunctionParameters,{function:"truncate"});return a===r.SqlServer?"ROUND("+t[0]+(1===t.length?"0":","+t[1])+",1)":"TRUNCATE("+t[0]+(1===t.length?")":","+t[1]+")");case"char_length":case"len":if(1!==t.length)throw new i(o.InvalidFunctionParameters,{function:"char_length"});switch(a){case r.SqlServer:return"LEN("+t[0]+")";case r.Oracle:return"LENGTH("+t[0]+")";default:return"CHAR_LENGTH("+t[0]+")"}case"coalesce":case"concat":{if(t.length<1)throw new i(o.InvalidFunctionParameters,{function:e.toLowerCase()});let r=e.toUpperCase().trim()+"(";for(let e=0;e<t.length;e++)0!==e&&(r+=","),r+=t[e];return r+=")",r}case"lower":case"lcase":if(1!==t.length)throw new i(o.InvalidFunctionParameters,{function:"lower"});return"LOWER("+t[0]+")";case"upper":case"ucase":if(1!==t.length)throw new i(o.InvalidFunctionParameters,{function:"upper"});return"UPPER("+t[0]+")";case"substring":{let e="";switch(a){case r.Oracle:return e="SUBSTR("+t[0]+","+t[1],3===t.length&&(e+=","+t[2]),e+=")",e;case r.SqlServer:return e=3===t.length?"SUBSTRING("+t[0]+","+t[1]+","+t[2]+")":"SUBSTRING("+t[0]+",  "+t[1]+", LEN("+t[0]+") - "+t[1]+")",e;default:return e="SUBSTRING("+t[0]+" FROM "+t[1],3===t.length&&(e+=" FOR "+t[2]),e+=")",e}}case"extract":return"EXTRACT("+t[0].replaceAll("'","")+" FROM "+t[1]+")";case"cast":{let e="";switch(a){case r.Oracle:switch(t[1].type){case"date":e="DATE";break;case"float":e="DOUBLE";break;case"integer":e="INTEGER";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;case r.Postgres:switch(t[1].type){case"date":e="DATE";break;case"float":e="DOUBLE PRECISION";break;case"integer":e="INT";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;case r.SqlServer:switch(t[1].type){case"date":e="DATE";break;case"float":e="FLOAT";break;case"integer":e="INT";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="DATETIME";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`;default:switch(t[1].type){case"date":e="DATE";break;case"float":e="FLOAT";break;case"integer":e="INTEGER";break;case"real":e="REAL";break;case"smallint":e="SMALLINT";break;case"timestamp":e="TIMESTAMP";break;case"varchar":e="VARCHAR("+t[1].size.toString()+")"}return`CAST(${t[0]} AS ${e})`}}}throw new i(o.InvalidFunctionParameters,{function:e})}function S(e,t){const a=e.toDateTime(),s=0===a.hour&&0===a.minute&&0===a.second&&0===a.millisecond;switch(t){case r.FILEGDB:case r.Standardised:case r.StandardisedNoInterval:return s?`date '${a.toFormat("yyyy-LL-dd")}'`:`timestamp '${a.toFormat("yyyy-LL-dd HH:mm:ss")}'`;case r.Oracle:return s?`TO_DATE('${a.toFormat("yyyy-LL-dd")}','YYYY-MM-DD')`:`TO_DATE('${a.toFormat("yyyy-LL-dd HH:mm:ss")}','YYYY-MM-DD HH24:MI:SS')`;case r.SqlServer:return`'${a.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;case r.PGDB:return`#${a.toFormat(s?"LL-dd-yyyy":"LL-dd-yyyy HH:mm:ss")}#`;case r.Postgres:return`TIMESTAMP '${a.toFormat(s?"yyyy-LL-dd":"yyyy-LL-dd HH:mm:ss")}'`;default:return`timestamp '${a.toFormat("yyyy-LL-dd HH:mm:ss")}'`}}function E(e,t){switch(t){case r.FILEGDB:case r.Standardised:case r.StandardisedNoInterval:return e.toSQLWithKeyword();case r.Oracle:return`TO_DATE('${e.toFormat("Y-MM-DD")}'`;case r.SqlServer:return`'${e.toFormat("Y-MM-DD")}'`;case r.PGDB:return`#${e.toFormat("Y-MM-DD")}#`;case r.Postgres:return`TIMESTAMP '${e.toFormat("Y-MM-DD")}'`;default:return e.toSQLWithKeyword()}}function A(e,t){switch(e instanceof u&&(e=e.toStorageString()),t){case r.Oracle:return`TO_DATE('${e}', 'HH24:MI:SS')`;case r.SqlServer:return`'${e}'`;case r.FILEGDB:case r.Standardised:case r.StandardisedNoInterval:case r.Postgres:default:return`time '${e}'`}}function I(r,t){return S(e.dateTimeToArcadeDate(a(r)?r:d.fromJSDate(r)),t)}function L(e,t){switch(t){case r.FILEGDB:case r.Standardised:case r.StandardisedNoInterval:case r.Oracle:case r.PGDB:default:return"date"===e?"CURRENT_DATE":"time"===e?"CURRENT_TIME":"CURRENT_TIMESTAMP";case r.SqlServer:return"date"===e?"CAST(GETDATE() AS DATE)":"time"===e?"CAST(GETDATE() AS TIME)":"GETDATE()";case r.Postgres:return"date"===e?"CURRENT_DATE":"time"===e?"LOCALTIME":"CURRENT_TIMESTAMP"}}function w(e,r,t={}){const a={},s={},n={esriFieldTypeSmallInteger:"integer",esriFieldTypeInteger:"integer",esriFieldTypeBigInteger:"integer",esriFieldTypeSingle:"double",esriFieldTypeDouble:"double",esriFieldTypeString:"string",esriFieldTypeTimeOnly:"time-only",esriFieldTypeDateOnly:"date-only",esriFieldTypeTimestampOffset:"timestamp-offset",esriFieldTypeDate:"date",esriFieldTypeOID:"integer",esriFieldTypeGUID:"guid",esriFieldTypeGlobalID:"guid",oid:"integer",long:"integer","small-integer":"integer",integer:"integer","big-integer":"integer",single:"double","time-only":"time-only","date-only":"date-only","timestamp-offset":"timestemp-offset",double:"double",date:"date",guid:"guid","global-id":"guid",string:"string"};for(const c of r){const e=c.type?n[c.type]:void 0;a[c.name.toLowerCase()]=void 0===e?"":e}for(const c in t){const e=n[t[c]];s[c.toLowerCase()]=void 0===e?"":e}switch(N(a,e.parseTree,e.parameters,s)){case"double":return"double";case"integer":return"integer";case"date":return"date";case"date-only":return"date-only";case"time-only":return"time-only";case"timestamp-offset":return"timestamp-offset";case"string":return"string";case"global-id":case"guid":return"guid"}return""}function N(e,r,a,u){let l;switch(r.type){case"interval":return"integer";case"case-expression":{const t=[];if("simple"===r.format){for(let s=0;s<r.clauses.length;s++)t.push(N(e,r.clauses[s].value,a,u));null!==r.else&&t.push(N(e,r.else,a,u))}else{for(let s=0;s<r.clauses.length;s++)t.push(N(e,r.clauses[s].value,a,u));null!==r.else&&t.push(N(e,r.else,a,u))}return v(t)}case"parameter":{const e=u[r.value.toLowerCase()];if(void 0===e&&a){const e=a[r.value.toLowerCase()];if(void 0===e)return"";if(null===e)return"";if("string"==typeof e||e instanceof String)return"string";if("boolean"==typeof e)return"boolean";if(t(e))return"date";if(n(e))return"date";if(c(e))return"date-only";if(s(e))return"time-only";if("number"==typeof e)return e%1==0?"integer":"double"}return void 0===e?"":e}case"expression-list":{const t=[];for(const s of r.value)t.push(N(e,s,a,u));return t}case"unary-expression":return"boolean";case"binary-expression":switch(r.operator){case"AND":case"OR":case"IN":case"NOT IN":case"BETWEEN":case"NOTBETWEEN":case"LIKE":case"NOT LIKE":case"<>":case"<":case">":case">=":case"<=":case"=":return"boolean";case"IS":case"ISNOT":if("null"!==r.right.type)throw new i(o.UnsupportedIsRhs);return"boolean";case"*":case"-":case"+":case"/":return v([N(e,r.left,a,u),N(e,r.right,a,u)]);case"||":return"string";default:throw new i(o.UnsupportedOperator,{operator:r.operator})}case"null":return"";case"boolean":return"boolean";case"string":return"string";case"number":return null===r.value?"":r.value%1==0?"integer":"double";case"date":return"date";case"timestamp":return r.withtimezone?"timestamp-offset":"date";case"time":return"time-only";case"current-time":return"time"===r.mode?"time-only":"date";case"current-user":return"string";case"column-reference":{const t=e[r.column.toLowerCase()];return void 0===t?"":t}case"function":switch(r.name.toLowerCase()){case"cast":switch(r.args?.value[1]?.value.type??""){case"integer":case"smallint":return"integer";case"real":case"float":return"double";case"date":case"timestamp":return!0===r.args?.value[1]?.value?.withtimezone?"timestamp-offset":"date";case"time":return"time-only";case"varchar":return"string";default:return""}case"position":case"extract":case"char_length":case"mod":return"integer";case"round":if(l=N(e,r.args,a,u),Array.isArray(l)){if(l.length<=0)return"double";l=l[0]}return l;case"sign":return"integer";case"ceiling":case"floor":case"abs":return l=N(e,r.args,a,u),Array.isArray(l)&&(l=v(l)),"integer"===l||"double"===l?l:"double";case"area":case"length":case"log":case"log10":case"sin":case"cos":case"tan":case"asin":case"acos":case"atan":case"cosh":case"sinh":case"tanh":case"power":return"double";case"substring":case"trim":case"concat":case"lower":case"upper":return"string";case"truncate":return"double";case"nullif":case"coalesce":return l=N(e,r.args,a,u),Array.isArray(l)?l.length>0?l[0]:"":l}return""}throw new i(o.UnsupportedSyntax,{node:r.type})}const b={boolean:1,string:2,integer:3,double:4,date:5};function v(e){if(e){let r="";for(const t of e)""!==t&&(r=""===r||b[r]<b[t]?t:r);return r}return""}function D(e,r){return R(e.parseTree,r)}function O(e){return"column-reference"===e?.parseTree.type}function R(e,r){if(null==e)return!1;switch(e.type){case"when-clause":return R(e.operand,r)||R(e.value,r);case"case-expression":for(const t of e.clauses)if(R(t,r))return!0;return!("simple"!==e.format||!R(e.operand,r))||!(null===e.else||!R(e.else,r));case"parameter":case"null":case"boolean":case"date":case"timestamp":case"time":case"string":case"number":return!1;case"expression-list":for(const t of e.value)if(R(t,r))return!0;return!1;case"unary-expression":return R(e.expr,r);case"binary-expression":return R(e.left,r)||R(e.right,r);case"column-reference":return r.toLowerCase()===e.column.toLowerCase();case"function":return R(e.args,r)}return!1}function C(e){let r="";return r+=e.period.toUpperCase(),r}function F(e,r,t){let a="";return a="interval-period"===r.type?C(r):C(r.start)+" TO "+C(r.end),"INTERVAL "+t+" "+e+" "+a}export{E as arcadeDateOnlyToSqlString,S as arcadeDateToSqlString,g as combine,h as convertColumnReferenceToSql,F as convertIntervalToSql,O as isSingleField,I as makeSqlFromDateTimeParameter,A as makeTimeString,L as makeToday,w as predictType,p as reformulateWithoutField,D as scanForField,f as toWhereClause,m as toWhereClauseFromTree,T as translateFunctionToDatabaseSpecific};
