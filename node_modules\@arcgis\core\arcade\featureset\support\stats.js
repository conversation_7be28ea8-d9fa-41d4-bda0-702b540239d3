/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{FeatureSetError as t,FeatureSetErrorCodes as n}from"./errorsupport.js";import{isNumber as e,isString as r,equalityTest as a}from"./shared.js";import{isSingleField as c,predictType as s}from"./sqlUtils.js";import{DateOnly as i}from"../../../core/sql/DateOnly.js";import{SqlTimeStampOffset as o}from"../../../core/sql/SqlTimestampOffset.js";import{TimeOnly as l}from"../../../core/sql/TimeOnly.js";function u(t){return t=+t,isFinite(t)?t-t%1||(t<0?-0:0===t?t:0):t}function f(t){let n=0;for(let e=0;e<t.length;e++)n+=t[e];return n/t.length}function h(t){const n=f(t);let e=0;for(let r=0;r<t.length;r++)e+=(n-t[r])**2;return e/t.length}function m(t){const n=f(t);let e=0;for(let r=0;r<t.length;r++)e+=(n-t[r])**2;return e/(t.length-1)}function g(t){let n=0;for(let e=0;e<t.length;e++)n+=t[e];return n}function p(t,n){const c=[],s={},i=[];for(let o=0;o<t.length;o++){if(void 0!==t[o]&&null!==t[o]){const n=t[o];if(e(n)||r(n))void 0===s[n]&&(c.push(n),s[n]=1);else{let t=!1;for(let e=0;e<i.length;e++)!0===a(i[e],n)&&(t=!0);!1===t&&(i.push(n),c.push(n))}}if(c.length>=n&&-1!==n)return c}return c}function w(t){switch(t.toLowerCase()){case"distinct":return"distinct";case"avg":case"mean":return"avg";case"min":return"min";case"sum":return"sum";case"max":return"max";case"stdev":case"stddev":return"stddev";case"var":case"variance":return"var";case"count":return"count"}return""}function d(t,n,e=1e3){switch(t.toLowerCase()){case"distinct":return p(n,e);case"avg":case"mean":return f(n);case"min":return Math.min.apply(Math,n);case"sum":return g(n);case"max":return Math.max.apply(Math,n);case"stdev":case"stddev":return Math.sqrt(h(n));case"var":case"variance":return h(n);case"count":return n.length}return 0}async function v(t,n,e){const r=await O(t,n,e);return 0===r.length?null:Math.min.apply(Math,r)}async function y(t,n,e){const r=await O(t,n,e);return 0===r.length?null:Math.max.apply(Math,r)}async function M(t,n,e){let r="";n&&!c(n)&&(r=s(n,t.fields));const a=await O(t,n,e);if(0===a.length)return null;const i=f(a);return null===i?i:"integer"===r?u(i):i}async function x(t,n,e){const r=await O(t,n,e);return 0===r.length?null:m(r)}async function T(t,n,e){const r=await O(t,n,e);return 0===r.length?null:Math.sqrt(m(r))}async function k(t,n,e){const r=await O(t,n,e);return 0===r.length?null:g(r)}async function q(t,n){return t.iterator(n).count()}async function O(e,r,a){const c=e.iterator(a),s=[],u={ticker:0};let f=await c.next();for(;null!==f;){if(u.ticker++,a.aborted)throw new t(n.Cancelled);u.ticker%100==0&&(u.ticker=0,await new Promise((t=>{setTimeout(t,0)})));const e=r?.calculateValue(f);null===e||(s[s.length]=e instanceof i||e instanceof l?e.toNumber():e instanceof o?e.toMilliseconds():e),f=await c.next()}return s}async function S(e,r,a=1e3,c=null){const s=e.iterator(c),u=[],f={},h={ticker:0};let m=await s.next();for(;null!==m;){if(h.ticker++,c?.aborted)throw new t(n.Cancelled);h.ticker%100==0&&(h.ticker=0,await new Promise((t=>{setTimeout(t,0)})));const e=r?.calculateValue(m);let g=e;if(e instanceof i?g="!!DATEONLY!!-"+e.toString():e instanceof o?g="!!TSOFFSETONLY!!-"+e.toString():e instanceof l?g="!!TIMEONLY!!-"+e.toString():e instanceof Date&&(g="!!DATE!!-"+e.toString()),null!=e&&void 0===f[g]&&(u.push(e),f[g]=1),u.length>=a&&-1!==a)return u;m=await s.next()}return u}export{d as calculateStat,q as count,w as decodeStatType,S as distinct,y as max,M as mean,v as min,T as stdev,k as sum,x as variance};
