/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{rad2deg as t}from"../../core/mathUtils.js";import n from"../../geometry/Point.js";import{segmentIntersects as e}from"../../geometry/support/intersectsBase.js";function r(t,n,e){return Math.sqrt((t[0]-n[0])**2+(t[1]-n[1])**2+(void 0!==t[2]&&void 0!==n[2]?(t[2]*e-n[2]*e)**2:0))}function o(t,n,e){return(t[0]-n[0])**2+(t[1]-n[1])**2+(void 0!==t[2]&&void 0!==n[2]?(t[2]*e-n[2]*e)**2:0)}const s=[];for(const N of[[9002,56146130,6131,6132,8050,8051,8228],[9003,5702,6358,6359,6360,8052,8053],[9095,5754]]){const t=N[0];for(let n=1;n<N.length;n++)s[N[n]]=t}const i=[];function c(t){return t.vcsWkid&&void 0!==s[t.vcsWkid]?i[s[t.vcsWkid]]:t.latestVcsWkid&&void 0!==s[t.latestVcsWkid]?i[s[t.latestVcsWkid]]:1}function f(t,n,e){const r={x:0,y:0};n&&(r.z=0),e&&(r.m=0);let o=0,s=t[0];for(let i=0;i<t.length;i++){const c=t[i];if(!1===x(c,s)){const t=h(s,c,n),i=u(s,c,n,e);i.x*=t,i.y*=t,r.x+=i.x,r.y+=i.y,n&&(i.z*=t,r.z+=i.z),e&&(i.m*=t,r.m+=i.m),o+=t,s=c}}return o>0?(r.x/=o,r.y/=o,n&&(r.z/=o),e&&(r.m/=o)):(r.x=t[0][0],r.y=t[0][1],n&&(r.z=t[0][2]),e&&n?r.m=t[0][3]:e&&(r.m=t[0][2])),r}function u(t,n,e,r){const o={x:(t[0]+n[0])/2,y:(t[1]+n[1])/2};return e&&(o.z=(t[2]+n[2])/2),e&&r?o.m=(t[3]+n[3])/2:r&&(o.m=(t[2]+n[2])/2),o}function a(t,n){if(t.length<=1)return 0;let e=0;for(let r=1;r<t.length;r++)e+=h(t[r-1],t[r],n);return e}function h(t,n,e){const r=n[0]-t[0],o=n[1]-t[1];if(e){const t=n[2]-n[2];return Math.sqrt(r*r+o*o+t*t)}return Math.sqrt(r*r+o*o)}function l(t,n,e){const r=n[0]-t[0],o=n[1]-t[1];if(e){const t=n[2]-n[2];return r*r+o*o+t*t}return r*r+o*o}function x(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==n[e])return!1;return!0}function y(t){const e={x:0,y:0,spatialReference:t.spatialReference.toJSON()},r={x:0,y:0,spatialReference:t.spatialReference.toJSON()};let o=0,s=0;for(let n=0;n<t.paths.length;n++){if(0===t.paths[n].length)continue;const i=a(t.paths[n],!0===t.hasZ);if(0===i){const r=f(t.paths[n],!0===t.hasZ,!0===t.hasM);e.x+=r.x,e.y+=r.y,!0===t.hasZ&&(e.z+=r.z),!0===t.hasM&&(e.m+=r.m),++o}else{const e=f(t.paths[n],!0===t.hasZ,!0===t.hasM);r.x+=e.x*i,r.y+=e.y*i,!0===t.hasZ&&(r.z+=e.z*i),!0===t.hasM&&(r.m+=e.m*i),s+=i}}return s>0?(r.x/=s,r.y/=s,!0===t.hasZ&&(r.z/=s),!0===t.hasM&&(r.m/=s),new n(r)):o>0?(e.x/=o,e.y/=o,!0===t.hasZ&&(r.z/=o),!0===t.hasM&&(e.m/=o),new n(e)):null}function m(t){if(0===t.points.length)return null;let e=0,r=0,o=0,s=0;for(let n=0;n<t.points.length;n++){const i=t.getPoint(n);!0===i.hasZ&&(o+=i.z),!0===i.hasM&&(s+=i.m),e+=i.x,r+=i.y,s+=i.m}const i={x:e/t.points.length,y:r/t.points.length,spatialReference:null};return i.spatialReference=t.spatialReference.toJSON(),!0===t.hasZ&&(i.z=o/t.points.length),!0===t.hasM&&(i.m=s/t.points.length),new n(i)}function p(t,n){return t.x*n.x+t.y*n.y}function g(t,n){return t.x*n.y-n.x*t.y}function M(t,n,e=0){for(;t<e;)t+=n;const r=e+n;for(;t>=r;)t-=n;return t}function z(t,n){return Math.atan2(n.y-t.y,n.x-t.x)}function d(t,n){return M(z(t,n),2*Math.PI)*(180/Math.PI)}function v(t,n){return M(Math.PI/2-z(t,n),2*Math.PI)*(180/Math.PI)}function P(t,n,e){const r={x:t.x-n.x,y:t.y-n.y},o={x:e.x-n.x,y:e.y-n.y};return Math.atan2(g(r,o),p(r,o))}function Z(n,e,r){return t(M(P(n,e,r),2*Math.PI))}function I(n,e,r){return t(M(-1*P(n,e,r),2*Math.PI))}i[9002]=.3048,i[9003]=.3048006096012192,i[9095]=.3048007491;const R=[0,0];function k(t){for(let n=0;n<t.length;n++){const r=t[n];for(let s=0;s<r.length-1;s++){const o=r[s],i=r[s+1];for(let r=n+1;r<t.length;r++)for(let n=0;n<t[r].length-1;n++){const s=t[r][n],c=t[r][n+1];if(e(o,i,s,c,R)&&!(R[0]===o[0]&&R[1]===o[1]||R[0]===s[0]&&R[1]===s[1]||R[0]===i[0]&&R[1]===i[1]||R[0]===c[0]&&R[1]===c[1]))return!0}}const o=r.length;if(!(o<3))for(let t=0;t<=o-2;t++){const n=r[t],s=r[t+1];for(let i=t+2;i<=o-2;i++){const t=r[i],o=r[i+1];if(e(n,s,t,o,R)&&!(R[0]===n[0]&&R[1]===n[1]||R[0]===t[0]&&R[1]===t[1]||R[0]===s[0]&&R[1]===s[1]||R[0]===o[0]&&R[1]===o[1]))return!0}}}return!1}function W(t,n,e){return Math.max(n,Math.min(e,t))}function j(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function q(t){return t[0]*t[0]+t[1]*t[1]+t[2]*t[2]}function w(t,n,e){const r=[e[0]-n[0],e[1]-n[1],e[2]-n[2]],o=W(j(r,[t[0]-n[0],t[1]-n[1],t[2]-n[2]])/q(r),0,1);return[n[0]+(e[0]-n[0])*o,n[1]+(e[1]-n[1])*o,n[2]+(e[2]-n[2])*o]}function J(t,n,e){let r=0;const o=e[0]-n[0],s=e[1]-n[1],i=o*o+s*s;if(0===i)r=.5;else{r=((t[0]-n[0])*o+(t[1]-n[1])*s)/i,r<0?r=0:r>1&&(r=1)}return r<=.5?[n[0]+(e[0]-n[0])*r,n[1]+(e[1]-n[1])*r]:[e[0]-(e[0]-n[0])*(1-r),e[1]-(e[1]-n[1])*(1-r)]}export{d as angle2D,Z as angleBetween2D,P as angleBetweenRad,z as angleRad,v as bearing2D,I as bearingBetween2D,m as centroidMultiPoint,y as centroidPolyline,J as closestPointOnLineSegment,w as closestPointOnLineSegmentWithZ,c as getMetersPerVerticalUnitForSR,k as pathsSelfIntersecting,h as segmentLength,r as segmentLength3d,o as segmentLength3dSqr,l as segmentLengthSqr};
