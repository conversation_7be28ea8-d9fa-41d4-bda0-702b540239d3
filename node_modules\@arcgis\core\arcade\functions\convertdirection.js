/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as t}from"../executionError.js";import{f as r,t as s,m as a}from"../../chunks/languageUtils.js";import{assertIsSome as i}from"../../core/maybe.js";import{isString as o,isNumber as c,isArray as u}from"../../support/guards.js";const l=e=>(n,t,r=14)=>+e(n,t).toFixed(r),d=(e,n)=>e+n,h=(e,n)=>e-n,m=(e,n)=>e*n,g=(e,n)=>e/n,_=(e,n,t)=>l(d)(e,n,t),f=(e,n,t)=>l(h)(e,n,t),w=(e,n,t)=>l(m)(e,n,t),A=(e,n,t)=>l(g)(e,n,t),D=360,p=400,E=2*Math.PI,T=3600,S=3240,R=60,M=60,F=180*T/Math.PI,v=D*R*M,L=90*T,U=180*T,N=270*T,x=String.fromCharCode(7501),z="°";function O(e){if(!1===o(e))throw new n(null,t.InvalidParameter,null);return e}function G(e,n){const t=10**n;return Math.round(e*t)/t}function I(e,n){return e%n}function b(e){const n=parseFloat(e.toString().replace(Math.trunc(e).toString(),"0"))*Math.sign(e);if(e<0){return{fraction:n,integer:Math.ceil(e)}}return{fraction:n,integer:Math.floor(e)}}var H,y,q,k;function C(e,n){switch(e){case H.north:return"SHORT"===n?"N":"North";case H.east:return"SHORT"===n?"E":"East";case H.south:return"SHORT"===n?"S":"South";case H.west:return"SHORT"===n?"W":"West"}}function P(e,n){return e-Math.floor(e/n)*n}function Q(e){switch(e){case y.truncated_degrees:case y.decimal_degrees:return D;case y.radians:return E;case y.gradians:return p;case y.seconds:return v;case y.fractional_degree_minutes:return R;case y.fractional_minute_seconds:return M;default:throw new n(null,t.LogicError,null,{reason:"unsupported evaluations"})}}function j(e){switch(e.toUpperCase().trim()){case"NORTH":case"NORTHAZIMUTH":case"NORTH AZIMUTH":return q.north_azimuth;case"POLAR":return q.polar;case"QUADRANT":return q.quadrant;case"SOUTH":case"SOUTHAZIMUTH":case"SOUTH AZIMUTH":return q.south_azimuth}throw new n(null,t.LogicError,null,{reason:"unsupported directionType"})}function W(e){switch(e.toUpperCase().trim()){case"D":case"DD":case"DECIMALDEGREE":case"DECIMAL DEGREE":case"DEGREE":case"DECIMALDEGREES":case"DECIMAL DEGREES":case"DEGREES":return y.decimal_degrees;case"DMS":case"DEGREESMINUTESSECONDS":case"DEGREES MINUTES SECONDS":return y.degrees_minutes_seconds;case"R":case"RAD":case"RADS":case"RADIAN":case"RADIANS":return y.radians;case"G":case"GON":case"GONS":case"GRAD":case"GRADS":case"GRADIAN":case"GRADIANS":return y.gradians}throw new n(null,t.LogicError,null,{reason:"unsupported units"})}!function(e){e[e.north=0]="north",e[e.east=1]="east",e[e.south=2]="south",e[e.west=3]="west"}(H||(H={})),function(e){e[e.decimal_degrees=1]="decimal_degrees",e[e.seconds=2]="seconds",e[e.degrees_minutes_seconds=3]="degrees_minutes_seconds",e[e.radians=4]="radians",e[e.gradians=5]="gradians",e[e.truncated_degrees=6]="truncated_degrees",e[e.fractional_degree_minutes=7]="fractional_degree_minutes",e[e.fractional_minute_seconds=8]="fractional_minute_seconds"}(y||(y={})),function(e){e[e.north_azimuth=1]="north_azimuth",e[e.polar=2]="polar",e[e.quadrant=3]="quadrant",e[e.south_azimuth=4]="south_azimuth"}(q||(q={})),function(e){e[e.meridian=0]="meridian",e[e.direction=1]="direction"}(k||(k={}));class Z{constructor(e,n,t){this.m_degrees=e,this.m_minutes=n,this.m_seconds=t}getField(e){switch(e){case y.decimal_degrees:case y.truncated_degrees:return this.m_degrees;case y.fractional_degree_minutes:return this.m_minutes;case y.seconds:case y.fractional_minute_seconds:return this.m_seconds;default:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"})}}static secondsToDMS(e){const n=b(e).fraction;let t=b(e).integer;const r=Math.floor(t/T);t-=r*T;const s=Math.floor(t/M);return t-=s*M,new Z(r,s,t+n)}static numberToDms(e){const n=b(e).fraction,t=b(e).integer,r=w(b(100*n).fraction,100),s=b(100*n).integer;return new Z(t,s,r)}format(e,n){let t=G(this.m_seconds,n),r=this.m_minutes,s=this.m_degrees;if(e===y.seconds||e===y.fractional_minute_seconds)M<=t&&(t-=M,++r),R<=r&&(r=0,++s),D<=s&&(s=0);else if(e===y.fractional_degree_minutes)t=0,r=30<=this.m_seconds?this.m_minutes+1:this.m_minutes,s=this.m_degrees,R<=r&&(r=0,++s),D<=s&&(s=0);else if(e===y.decimal_degrees||e===y.truncated_degrees){const e=A(this.m_seconds,T),n=A(this.m_minutes,R);s=Math.round(this.m_degrees+n+e),r=0,t=0}return new Z(s,r,t)}static dmsToSeconds(e,n,t){return e*T+n*M+t}}class B{constructor(e,n,t){this.meridian=e,this.angle=n,this.direction=t}fetchAzimuth(e){return e===k.meridian?this.meridian:this.direction}}class V{constructor(e){this._angle=e}static createFromAngleAndDirection(e,n){return new V(new J(V._convertDirectionFormat(e.extractAngularUnits(y.seconds),n,q.north_azimuth)))}getAngle(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case q.north_azimuth:case q.south_azimuth:case q.polar:return new J(V._convertDirectionFormat(n,q.north_azimuth,e));case q.quadrant:{const e=V.secondsNorthAzimuthToQuadrant(n);return new J(e.angle)}}}getMeridian(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case q.north_azimuth:return H.north;case q.south_azimuth:return H.south;case q.polar:return H.east;case q.quadrant:return V.secondsNorthAzimuthToQuadrant(n).meridian}}getDirection(e){const n=this._angle.extractAngularUnits(y.seconds);switch(e){case q.north_azimuth:return H.east;case q.south_azimuth:return H.west;case q.polar:return H.north;case q.quadrant:return V.secondsNorthAzimuthToQuadrant(n).direction}}static secondsNorthAzimuthToQuadrant(e){const n=e<=L||e>=N?H.north:H.south,t=n===H.north?Math.min(v-e,e):Math.abs(e-U),r=e>U?H.west:H.east;return new B(n,t,r)}static createFromAngleMeridianAndDirection(e,n,t){return new V(new J(V.secondsQuadrantToNorthAzimuth(e.extractAngularUnits(y.seconds),n,t)))}static secondsQuadrantToNorthAzimuth(e,n,t){return n===H.north?t===H.east?e:v-e:t===H.east?U-e:U+e}static _convertDirectionFormat(e,r,s){let a=0;switch(r){case q.north_azimuth:a=e;break;case q.polar:a=L-e;break;case q.quadrant:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"});case q.south_azimuth:a=e+U}let i=0;switch(s){case q.north_azimuth:i=a;break;case q.polar:i=L-a;break;case q.quadrant:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"});case q.south_azimuth:i=a-U}return i=I(i,v),i<0?v+i:i}}function X(e,r,s){let a=null;switch(r){case y.decimal_degrees:a=w(e,T);break;case y.seconds:a=e;break;case y.gradians:a=w(e,S);break;case y.radians:a=w(e,F);break;default:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"})}switch(s){case y.decimal_degrees:return A(a,T);case y.seconds:return a;case y.gradians:return A(a,S);case y.radians:return a/F;default:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"})}}class J{constructor(e){this._seconds=e}static createFromAngleAndUnits(e,n){return new J(X(e,n,y.seconds))}extractAngularUnits(e){return X(this._seconds,y.seconds,K(e))}static createFromDegreesMinutesSeconds(e,n,t){return new J(_(_(w(e,T),w(n,M)),t))}}function K(e){switch(i(e),e){case y.decimal_degrees:case y.truncated_degrees:case y.degrees_minutes_seconds:return y.decimal_degrees;case y.gradians:return y.gradians;case y.fractional_degree_minutes:return y.fractional_degree_minutes;case y.radians:return y.radians;case y.seconds:case y.fractional_minute_seconds:return y.seconds}}class Y{constructor(e,n,t,r){this.view=e,this.angle=n,this.merdian=t,this.direction=r,this._dms=null,this._formattedDms=null}static createFromStringAndBearing(e,n,t){return new Y(e,n.getAngle(t),n.getMeridian(t),n.getDirection(t))}fetchAngle(){return this.angle}fetchMeridian(){return this.merdian}fetchDirection(){return this.direction}fetchView(){return this.view}fetchDms(){return null===this._dms&&this._calculateDms(),this._dms}fetchFormattedDms(){return null===this._formattedDms&&this._calculateDms(),this._formattedDms}_calculateDms(){let e=null,n=y.truncated_degrees,t=0;for(let r=0;r<this.view.length;r++){const s=this.view[r];switch(s){case"m":e=le(this.view,r,s),n=n===y.truncated_degrees?y.fractional_degree_minutes:n,r=e.newpos;continue;case"s":e=le(this.view,r,s),n=y.fractional_minute_seconds,t=t<e.rounding?e.rounding:t,r=e.newpos;continue;default:continue}}this._dms=Z.secondsToDMS(this.angle.extractAngularUnits(y.seconds)),this._formattedDms=Z.secondsToDMS(this.angle.extractAngularUnits(y.seconds)).format(n,t)}}function $(e,r,s,a,i){let o=null;switch(r){case y.decimal_degrees:case y.radians:case y.gradians:return o=P(G(e.extractAngularUnits(r),a),Q(r)),o.toFixed(a).padStart(s+a+(a>0?1:0),"0");case y.truncated_degrees:case y.fractional_degree_minutes:return o=P(i.fetchFormattedDms().getField(r),Q(r)),o.toFixed(a).padStart(s+a+(a>0?1:0),"0");case y.fractional_minute_seconds:return o=P(G(i.fetchDms().getField(r),a),Q(r)),o.toFixed(a).padStart(s+a+(a>0?1:0),"0");default:throw new n(null,t.LogicError,null,{reason:"unexpected evaluation"})}}function ee(e,r,s){if(s===q.quadrant)throw new n(null,t.LogicError,null,{reason:"conversion error"});if(r===y.degrees_minutes_seconds){const n=Z.numberToDms(e);return V.createFromAngleAndDirection(J.createFromDegreesMinutesSeconds(n.m_degrees,n.m_minutes,n.m_seconds),s)}return V.createFromAngleAndDirection(J.createFromAngleAndUnits(e,K(r)),s)}function ne(e){switch(s(e)){case 1:return{first:H.north,second:H.east};case 2:return{first:H.south,second:H.east};case 3:return{first:H.south,second:H.west};case 4:return{first:H.north,second:H.west}}return null}function te(e){switch(e.toUpperCase().trim()){case"N":case"NORTH":return H.north;case"E":case"EAST":return H.east;case"S":case"SOUTH":return H.south;case"W":case"WEST":return H.west}return null}function re(e){const r=parseFloat(e);if(c(r)){if(isNaN(r))throw new n(null,t.LogicError,null,{reason:"invalid conversion"});return r}throw new n(null,t.LogicError,null,{reason:"invalid conversion"})}function se(e,s,a){const i=a===q.quadrant;let o=null,c=null,u=0,l=0,d=0;if(i){if(e.length<2)throw new n(null,t.LogicError,null,{reason:"conversion error"});d=1;const s=ne(r(e[e.length-1]));if(s?(o=s.first,c=s.second):(u=1,o=te(r(e[0])),c=te(r(e[e.length-1]))),null===o||null===c)throw new n(null,t.LogicError,null,{reason:"invalid conversion"})}switch(s){case y.decimal_degrees:case y.radians:case y.gradians:if(0===e.length)throw new n(null,t.LogicError,null,{reason:"invalid conversion"});return i?V.createFromAngleMeridianAndDirection(J.createFromAngleAndUnits(re(e[u]),K(s)),o,c):V.createFromAngleAndDirection(J.createFromAngleAndUnits(re(e[u]),K(s)),a);case y.degrees_minutes_seconds:if(l=e.length-d-u,3===l){const n=J.createFromDegreesMinutesSeconds(re(e[u]),re(e[u+1]),re(e[u+2]));return i?V.createFromAngleMeridianAndDirection(n,o,c):V.createFromAngleAndDirection(n,a)}if(1===l){const n=re(e[u]),t=Z.numberToDms(n),r=J.createFromDegreesMinutesSeconds(t.m_degrees,t.m_minutes,t.m_seconds);return i?V.createFromAngleMeridianAndDirection(r,o,c):V.createFromAngleAndDirection(r,a)}}throw new n(null,t.LogicError,null,{reason:"invalid conversion"})}function ae(e){const n=new Set([" ","-","/","'",'"',"\\","^",z,x,"\t","\r","\n","*"]);let t="";for(let r=0;r<e.length;r++){const s=e.charAt(r);n.has(s)?t+="RRSPLITRRSPLITRR":t+=s}return t.split("RRSPLITRRSPLITRR").filter((e=>""!==e))}function ie(e,r,i){if(c(e))return ee(s(e),r,i);if(o(e))return se(ae(e),r,i);if(u(e))return se(e,r,i);if(a(e))return se(e.toArray(),r,i);throw new n(null,t.LogicError,null,{reason:"conversion error"})}function oe(e,r,s){const a=K(s);if(a&&s!==y.degrees_minutes_seconds){return e.getAngle(r).extractAngularUnits(a)}throw new n(null,t.LogicError,null,{reason:"conversion error"})}function ce(e,n,t){const r=e.getAngle(n);if(n===q.quadrant&&t===y.degrees_minutes_seconds){const t=Z.secondsToDMS(r.extractAngularUnits(y.seconds));return[C(e.getMeridian(n),"SHORT"),t.m_degrees,t.m_minutes,t.m_seconds,C(e.getDirection(n),"SHORT")]}if(t===y.degrees_minutes_seconds){const e=Z.secondsToDMS(r.extractAngularUnits(y.seconds));return[e.m_degrees,e.m_minutes,e.m_seconds]}return n===q.quadrant?[C(e.getMeridian(n),"SHORT"),r.extractAngularUnits(t),C(e.getDirection(n),"SHORT")]:[r.extractAngularUnits(t)]}function ue(e,r){let s="";switch(e){case y.decimal_degrees:s=r===q.quadrant?"DD.DD"+z:"DDD.DD"+z;break;case y.degrees_minutes_seconds:s=r===q.quadrant?"dd"+z+" mm' ss\"":"ddd"+z+" mm' ss.ss\"";break;case y.radians:s="R.RR";break;case y.gradians:s="GGG.GG"+x;break;default:throw new n(null,t.LogicError,null,{reason:"conversion error"})}return r===q.quadrant&&(s="p "+s+" b"),s}function le(e,n,t){const r={padding:0,rounding:0,newpos:n};let s=!1;for(;n<e.length;){const a=e[n];if(a===t)s?r.rounding++:r.padding++,n++;else{if("."!==a)break;s=!0,n++}}return r.newpos=n-1,r}function de(e,n,t){const r={escaped:"",newpos:n};for(n++;n<e.length;){const t=e[n];if(n++,"]"===t)break;r.escaped+=t}return r.newpos=n-1,r}function he(e,n,t){let r="",s=null,a=null;const i=Y.createFromStringAndBearing(n,e,t),o={D:y.decimal_degrees,d:y.truncated_degrees,m:y.fractional_degree_minutes,s:y.fractional_minute_seconds,R:y.radians,G:y.gradians};for(let c=0;c<n.length;c++){const u=n[c];switch(u){case"[":s=de(n,c),r+=s.escaped,c=s.newpos;continue;case"D":case"d":case"m":case"s":case"R":case"G":s=le(n,c,u),a=e.getAngle(t),r+=$(a,o[u],s.padding,s.rounding,i),c=s.newpos;continue;case"P":case"p":r+=C(i.fetchMeridian(),"p"===u?"SHORT":"LONG");continue;case"B":case"b":r+=C(i.fetchDirection(),"b"===u?"SHORT":"LONG");continue;default:r+=u}}return r}function me(s,a,i){if(!(a instanceof e))throw new n(null,t.InvalidParameter,null);if(!1===a.hasField("directionType"))throw new n(null,t.LogicError,null,{reason:"missing directionType"});if(!1===a.hasField("angleType"))throw new n(null,t.LogicError,null,{reason:"missing angleType"});const o=j(O(a.field("directiontype"))),c=ie(s,W(O(a.field("angletype"))),o);if(!(i instanceof e))throw new n(null,t.InvalidParameter,null);if(!1===i.hasField("directionType"))throw new n(null,t.LogicError,null,{reason:"missing directionType"});if(!1===i.hasField("outputType"))throw new n(null,t.LogicError,null,{reason:"missing angleType"});const u=j(O(i.field("directiontype"))),l=i.hasField("angleType")?W(O(i.field("angletype"))):null,d=O(i.field("outputType")).toUpperCase().trim();if(!u||!d)throw new n(null,t.LogicError,null,{reason:"conversion error"});if(!(l||"TEXT"===d&&i.hasField("format")))throw new n(null,t.LogicError,null,{reason:"invalid unit"});switch(d){case"VALUE":return u===q.quadrant||l===y.degrees_minutes_seconds?ce(c,u,l):oe(c,u,l);case"TEXT":{let e="";return i.hasField("format")&&(e=r(i.field("format"))),null!==e&&""!==e||(e=ue(l,u)),he(c,e,u)}default:throw new n(null,t.InvalidParameter,null)}}export{me as convertDirection,_ as preciseAdd,A as preciseDivide,f as preciseMinus,w as preciseMultiply};
