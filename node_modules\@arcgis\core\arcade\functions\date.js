/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as n,createDateTimeZone as e}from"../ArcadeDate.js";import{ArcadeExecutionError as r,ExecutionErrorCodes as t}from"../executionError.js";import{D as o,K as u,t as a,k as s,g as i,j as l,f as c,h as f,J as m,M as d,N as h}from"../../chunks/languageUtils.js";import{DateOnly as y}from"../../core/sql/DateOnly.js";import{TimeOnly as N}from"../../core/sql/TimeOnly.js";import{getLocale as w}from"../../intl/locale.js";import{DateTime as g}from"luxon";import{isString as A}from"../../support/guards.js";function k(n,e,r){return n+(T(r)?P:D)[e]}function T(n){return n%4==0&&(n%100!=0||n%400==0)}const D=[0,31,59,90,120,151,181,212,243,273,304,334],P=[0,31,60,91,121,152,182,213,244,274,305,335];function S(n){return null===n?n:!1===n.isValid?null:n}function Z(n,e){return""===n||"default"===n.toLowerCase().trim()?u(e):"z"===n||"Z"===n?"UTC":n}function p(n,e){return l(n)?n.toArcadeDate():f(n,u(e))}function C(T,D){T.today=function(e,r){return D(e,r,((t,a,s)=>{o(s,0,0,e,r);const i=new Date;return i.setHours(0,0,0,0),n.dateJSAndZoneToArcadeDate(i,u(e))}))},T.time=function(e,c){return D(e,c,((f,m,d)=>{switch(o(d,0,4,e,c),d.length){case 0:{const r=n.nowToArcadeDate(u(e));return new N(r.hour,r.minute,r.second,r.millisecond)}case 1:{if(s(d[0]))return d[0].clone();if(i(d[0]))return new N(d[0].hour,d[0].minute,d[0].second,d[0].millisecond);if(l(d[0]))return new N(0,0,0,0);if(A(d[0]))return N.fromString(d[0]);const n=a(d[0]);return!1===isNaN(n)?N.fromMilliseconds(n):null}case 2:return A(d[0])&&A(d[1])?N.fromString(d[0],d[1]):N.fromParts(a(d[0]),a(d[1]),0,0);case 3:return N.fromParts(a(d[0]),a(d[1]),a(d[2]),0);case 4:return N.fromParts(a(d[0]),a(d[1]),a(d[2]),a(d[3]))}throw new r(e,t.InvalidParameter,c)}))},T.dateonly=function(e,r){return D(e,r,((t,s,f)=>{if(o(f,0,3,e,r),3===f.length)return y.fromParts(a(f[0]),a(f[1])+1,a(f[2]));if(2===f.length){const n=c(f[1]);return""===n?null:"X"===n?y.fromSeconds(a(f[0])):"x"===n?y.fromMilliseconds(a(f[0])):y.fromString(c(f[0]),n)}if(1===f.length){if(A(f[0])){if(""===f[0].replaceAll(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""))return null;if(!0===/^[0-9][0-9][0-9][0-9]$/.test(f[0]))return y.fromString(f[0]+"-01-01")}if(l(f[0]))return f[0].clone();if(i(f[0]))return y.fromParts(f[0].year,f[0].monthJS+1,f[0].day);const n=a(f[0]);return!1===isNaN(n)?y.fromMilliseconds(n):A(f[0])?y.fromString(f[0]):null}if(0===f.length){const r=n.nowToArcadeDate(u(e));return!1===r.isValid?null:y.fromParts(r.year,r.monthJS+1,r.day)}return null}))},T.changetimezone=function(a,s){return D(a,s,((i,m,d)=>{if(o(d,2,2,a,s),null===d[0])return null;if(l(d[0]))throw new r(a,t.CannotChangeTimeZoneDateOnly,s);if(l(d[0]))throw new r(a,t.CannotChangeTimeZoneTime,s);const h=f(d[0],u(a));if(null===h)throw new r(a,t.InvalidParameter,s);const y=e(Z(c(d[1]),a),!1);if(null===y)return null;const N=n.arcadeDateAndZoneToArcadeDate(h,y);return!1===N.isValid?null:N}))},T.timezone=function(e,r){return D(e,r,((t,a,i)=>{if(o(i,1,2,e,r),s(i[0]))return"Unknown";if(l(i[0]))return"Unknown";const c=f(i[0],u(e));if(null===c)return null;const m=c.timeZone;return"system"===m?n.systemTimeZoneCanonicalName:"utc"===m.toLowerCase()?"UTC":"unknown"===m.toLowerCase()?"Unknown":m}))},T.timezoneoffset=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=f(a[0],u(n));return null===s?null:60*s.timeZoneOffset*1e3}))},T.now=function(e,r){return D(e,r,((t,a,s)=>{o(s,0,0,e,r);const i=n.nowToArcadeDate(u(e));return!1===i.isValid?null:i}))},T.timestamp=function(e,r){return D(e,r,((t,u,a)=>{o(a,0,0,e,r);const s=n.nowUTCToArcadeDate();return!1===s.isValid?null:s}))},T.toutc=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=f(a[0],u(n));return null===s?null:s.toUTC()}))},T.tolocal=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=f(a[0],u(n));return null===s?null:s.toLocal()}))},T.day=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.day}))},T.month=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.monthJS}))},T.year=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.year}))},T.hour=function(n,e){return D(n,e,((r,t,a)=>{if(o(a,1,1,n,e),s(a[0]))return a[0].hour;const i=f(a[0],u(n));return null===i?NaN:i.hour}))},T.second=function(n,e){return D(n,e,((r,t,a)=>{if(o(a,1,1,n,e),s(a[0]))return a[0].second;const i=f(a[0],u(n));return null===i?NaN:i.second}))},T.millisecond=function(n,e){return D(n,e,((r,t,a)=>{if(o(a,1,1,n,e),s(a[0]))return a[0].millisecond;const i=f(a[0],u(n));return null===i?NaN:i.millisecond}))},T.minute=function(n,e){return D(n,e,((r,t,a)=>{if(o(a,1,1,n,e),s(a[0]))return a[0].minute;const i=f(a[0],u(n));return null===i?NaN:i.minute}))},T.week=function(n,e){return D(n,e,((s,i,l)=>{o(l,1,2,n,e);const c=p(l[0],u(n));if(null===c)return NaN;const f=a(m(l[1],0));if(f<0||f>6)throw new r(n,t.InvalidParameter,e);const d=c.day,h=c.monthJS,y=c.year,N=c.dayOfWeekJS,w=k(d,h,y)-1,g=Math.floor(w/7);return N-f+(N-f<0?7:0)<w-7*g?g+1:g}))},T.weekday=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.dayOfWeekJS}))},T.isoweekday=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.dayOfWeekISO}))},T.isomonth=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.monthISO}))},T.isoweek=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.weekISO}))},T.isoyear=function(n,e){return D(n,e,((r,t,a)=>{o(a,1,1,n,e);const s=p(a[0],u(n));return null===s?NaN:s.yearISO}))},T.date=function(r,t){return D(r,t,((i,m,h)=>{if(o(h,0,8,r,t),3===h.length){if(l(h[0])&&s(h[1])&&A(h[2])){const t=e(Z(c(h[2])??"unknown",r),!1);return null===t?null:S(n.fromParts(h[0].year,h[0].month,h[0].day,h[1].hour,h[1].minute,h[1].second,h[1].millisecond,t))}return S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),0,0,0,0,u(r)))}if(4===h.length)return S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),a(h[3]),0,0,0,u(r)));if(5===h.length)return S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),a(h[3]),a(h[4]),0,0,u(r)));if(6===h.length)return S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),a(h[3]),a(h[4]),a(h[5]),0,u(r)));if(7===h.length)return S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),a(h[3]),a(h[4]),a(h[5]),a(h[6]),u(r)));if(8===h.length){const t=e(Z(c(h[7])??"unknown",r),!1);return null===t?null:S(n.fromParts(a(h[0]),a(h[1])+1,a(h[2]),a(h[3]),a(h[4]),a(h[5]),a(h[6]),t))}if(2===h.length){if(l(h[0])&&A(h[1])){const t=e(Z(c(h[1])??"unknown",r),!1);return null===t?null:S(n.fromParts(h[0].year,h[0].month,h[0].day,0,0,0,0,t))}if(l(h[0])&&s(h[1]))return S(n.fromParts(h[0].year,h[0].month,h[0].day,h[1].hour,h[1].minute,h[1].second,h[1].millisecond,"unknown"));let t,o=c(h[1]);return""===o?null:(o=d(o,!0),t="X"===o?g.fromSeconds(a(h[0])):"x"===o?g.fromMillis(a(h[0])):g.fromFormat(c(h[0]),o,{locale:w(),numberingSystem:"latn"}),t.isValid?n.dateTimeToArcadeDate(t):null)}if(1===h.length){if(l(h[0]))return S(n.fromParts(h[0].year,h[0].month,h[0].day,0,0,0,0,"unknown"));if(A(h[0])){if(""===h[0].replaceAll(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""))return null;if(!0===/^[0-9][0-9][0-9][0-9]$/.test(h[0]))return f(h[0]+"-01-01",u(r))}const e=a(h[0]);if(!1===isNaN(e)){const t=g.fromMillis(e);return t.isValid?n.dateTimeAndZoneToArcadeDate(t,u(r)):null}return f(h[0],u(r))}return 0===h.length?n.nowToArcadeDate(u(r)):null}))},T.datediff=function(e,r){return D(e,r,((t,a,i)=>{if(o(i,2,4,e,r),s(i[0]))return s(i[1])?i[0].difference(i[1],c(i[2])):NaN;if(s(i[1]))return NaN;if(l(i[0]))return l(i[1])?i[0].difference(i[1],c(i[2])):NaN;if(l(i[1]))return NaN;let d=f(i[0],u(e)),h=f(i[1],u(e));if(null===d||null===h)return NaN;let y=m(i[3],"");switch(""!==y&&null!==y?(y=Z(c(y),e),d=n.arcadeDateAndZoneToArcadeDate(d,y),h=n.arcadeDateAndZoneToArcadeDate(h,y)):d.timeZone!==h.timeZone&&(d.isUnknownTimeZone?d=n.arcadeDateAndZoneToArcadeDate(d,h.timeZone):h=(h.isUnknownTimeZone,n.arcadeDateAndZoneToArcadeDate(h,d.timeZone))),c(i[2]).toLowerCase()){case"days":case"day":case"d":return d.diff(h,"days");case"months":case"month":return d.diff(h,"months");case"minutes":case"minute":case"m":return"M"===i[2]?d.diff(h,"months"):d.diff(h,"minutes");case"seconds":case"second":case"s":return d.diff(h,"seconds");case"milliseconds":case"millisecond":case"ms":default:return d.diff(h);case"hours":case"hour":case"h":return d.diff(h,"hours");case"years":case"year":case"y":return d.diff(h,"years")}}))},T.dateadd=function(n,e){return D(n,e,((r,t,i)=>{o(i,2,3,n,e);let m=a(i[1]);if(isNaN(m)||m===1/0||m===-1/0)return s(i[0])||l(i[0])?i[0].clone():f(i[0],u(n));let d="milliseconds";switch(c(i[2]).toLowerCase()){case"days":case"day":case"d":d="days",m=l(i[0])?m:h(m);break;case"months":case"month":d="months",m=l(i[0])?m:h(m);break;case"minutes":case"minute":case"m":d="M"===i[2]?"months":"minutes";break;case"seconds":case"second":case"s":d="seconds";break;case"milliseconds":case"millisecond":case"ms":d="milliseconds";break;case"hours":case"hour":case"h":d="hours";break;case"years":case"year":case"y":d="years"}if(s(i[0]))return i[0].plus(d,m);if(l(i[0]))return i[0].plus(d,m);const y=f(i[0],u(n));return null===y?null:y.plus({[d]:m})}))}}export{C as registerFunctions};
