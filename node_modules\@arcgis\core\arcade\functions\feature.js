/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as t}from"../executionError.js";import r from"../Feature.js";import{D as o,n as i,O as a,f as u,K as s,P as d,Q as f,w as c,S as m,T as l,g as p}from"../../chunks/languageUtils.js";import{layerFieldEsriConstants as y}from"../featureset/support/shared.js";import{SqlTimeStampOffset as w}from"../../core/sql/SqlTimestampOffset.js";import v from"../../core/sql/WhereClause.js";import{isString as b}from"../../support/guards.js";function h(e){const n=e?.fullSchema();return n?.datesInUnknownTimezone?"unknown":n?.dateFieldsTimeZone||"UTC"}function T(e){const n=e.fullSchema()?.fieldsIndex;return null==n&&e instanceof r?e.fieldsIndex:n}const I={getAttributeSQL(e,n){const t=e.field(n);if(null==t)return t;if(p(t)){const r=t.toDateTime(),o=T(e)?.get(n)?.type;return"esriFieldTypeTimestampOffset"===o||"timestamp-offset"===o?w.fromDateTime(r):r}return t}};function g(r,p,w){r.domain=function(r,d){return p(r,d,((f,c,m)=>{if(o(m,2,3,r,d),i(m[0])){const n=a(m[0],u(m[1]),void 0===m[2]?void 0:m[2]);return n&&n.domain?"coded-value"===n.domain.type||"codedValue"===n.domain.type?e.convertObjectToArcadeDictionary({type:"codedValue",name:n.domain.name,dataType:y[n.field.type],codedValues:n.domain.codedValues.map((e=>({name:e.name,code:e.code})))},s(r)):e.convertObjectToArcadeDictionary({type:"range",name:n.domain.name,dataType:y[n.field.type],min:n.domain.minValue,max:n.domain.maxValue},s(r)):null}throw new n(r,t.InvalidParameter,d)}))},r.domaincode=function(e,r){return p(e,r,((a,s,f)=>{if(o(f,2,4,e,r),i(f[0]))return d(f[0],u(f[1]),f[2],void 0===f[3]?void 0:f[3]);throw new n(e,t.InvalidParameter,r)}))},r.domainname=function(e,r){return p(e,r,((a,s,d)=>{if(o(d,2,4,e,r),i(d[0]))return f(d[0],u(d[1]),d[2],void 0===d[3]?void 0:d[3]);throw new n(e,t.InvalidParameter,r)}))},r.expects=function(e,r){return p(e,r,((o,i,a)=>{if(a.length<1)throw new n(e,t.WrongNumberOfParameters,r);return c}))},r.featureinfilter=function(e,r){return p(e,r,((e,r,a)=>{o(a,2,2,e,r);const[u,s]=a;if(null==u)return!1;if(""===s||null==s)return!0;if(!i(u)||!b(s))throw new n(e,t.InvalidParameter,r);const d=v.create(s,{fieldsIndex:T(u),timeZone:h(u)}),f=d.getVariables();for(const n of f)d.parameters[n]=w(e,{name:n});return d.testFeature(u,I)}))},r.gdbversion=function(e,r){return p(e,r,((a,u,s)=>{if(o(s,1,1,e,r),i(s[0]))return s[0].gdbVersion();throw new n(e,t.InvalidParameter,r)}))},r.schema=function(r,o){return p(r,o,((a,u,d)=>{if(i(d[0])){const n=m(d[0]);return n?e.convertObjectToArcadeDictionary(n,s(r)):null}throw new n(r,t.InvalidParameter,o)}))},r.subtypecode=function(e,r){return p(e,r,((a,u,s)=>{if(o(s,1,1,e,r),i(s[0])){const e=l(s[0]);if(!e)return null;if(e.subtypeField&&s[0].hasField(e.subtypeField)){const n=s[0].field(e.subtypeField);for(const t of e.subtypes)if(t.code===n)return t.code;return null}return null}throw new n(e,t.InvalidParameter,r)}))},r.subtypename=function(e,r){return p(e,r,((a,u,s)=>{if(o(s,1,1,e,r),i(s[0])){const e=l(s[0]);if(!e)return"";if(e.subtypeField&&s[0].hasField(e.subtypeField)){const n=s[0].field(e.subtypeField);for(const t of e.subtypes)if(t.code===n)return t.name;return""}return""}throw new n(e,t.InvalidParameter,r)}))},r.subtypes=function(r,a){return p(r,a,((u,d,f)=>{if(o(f,1,1,r,a),i(f[0])){const n=l(f[0]);return n?e.convertObjectToArcadeDictionary(n,s(r)):null}throw new n(r,t.InvalidParameter,a)}))}}export{g as registerFunctions};
