/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../Graphic.js";import{ArcadeDate as t}from"../ArcadeDate.js";import n from"../ArcadePortal.js";import i from"../Dictionary.js";import{ArcadeExecutionError as a,ExecutionErrorCodes as r}from"../executionError.js";import o from"../Feature.js";import{convertToFeatureSet as s,constructFeatureSetFromPortalItem as l,constructFeatureSet as f,constructFeatureSetFromRelationship as d,constructFeatureSetFromUrl as u,constructAssociationMetaDataFeatureSetFromUrl as c}from"../featureSetUtils.js";import m from"../ImmutableArray.js";import{D as p,k as y,j as w,p as h,f as I,h as g,K as F,g as b,o as T,J as D,e as x,n as E,m as N,i as A,t as v,$ as S,W as L}from"../../chunks/languageUtils.js";import{getPortal as j}from"../portalUtils.js";import{SqlExpressionAdapted as C,StringToCodeAdapted as Z,FieldRename as k,AdaptedFeatureSet as $,OriginalField as P}from"../featureset/actions/Adapted.js";import U from"../featureset/actions/AttributeFilter.js";import M from"../featureset/actions/OrderBy.js";import R from"../featureset/actions/Top.js";import O from"../featureset/sources/Empty.js";import z from"../featureset/sources/FeatureLayerMemory.js";import W from"../featureset/support/OrderbyClause.js";import{isSupportedLayer as H,cloneField as G}from"../featureset/support/shared.js";import{isSingleField as V}from"../featureset/support/sqlUtils.js";import{calculateStat as _}from"./fieldStats.js";import{isPromiseLike as K}from"../../core/promiseUtils.js";import q from"../../core/sql/WhereClause.js";import B from"../../layers/FeatureLayer.js";import Q from"../../layers/support/Field.js";import J from"../../portal/Portal.js";import{queryAssociations as Y}from"../../rest/networks/queryAssociations.js";import X from"../../rest/networks/support/NetworkElement.js";import ee from"../../rest/networks/support/QueryAssociationsParameters.js";import{isString as te,isArray as ne,isInteger as ie}from"../../support/guards.js";function ae(e){if(1===e.length){if(ne(e[0]))return _("distinct",e[0],-1);if(N(e[0]))return _("distinct",e[0].toArray(),-1)}return _("distinct",e,-1)}function re(e,t,n){const i=e.getVariables();if(i.length>0){const a={};for(const e of i)a[e]=t.evaluateIdentifier(n,{name:e});e.parameters=a}return e}function oe(e,t,n=null){for(const i in e)if(i.toLowerCase()===t.toLowerCase())return e[i];return n}function se(e){if(null===e)return null;const t={type:oe(e,"type",""),name:oe(e,"name","")};if("range"===t.type)t.range=oe(e,"range",[]);else{t.codedValues=[];for(const n of oe(e,"codedValues",[]))t.codedValues.push({name:oe(n,"name",""),code:oe(n,"code",null)})}return t}function le(e){if(null===e)return null;const t={},n=oe(e,"wkt");null!==n&&(t.wkt=n);const i=oe(e,"wkid");return null!==i&&(t.wkid=i),t}function fe(e){if(null===e)return null;const t={hasZ:oe(e,"hasz",!1),hasM:oe(e,"hasm",!1)},n=oe(e,"spatialreference");null!=n&&(t.spatialReference=le(n));const i=oe(e,"x",null);if(null!==i)return t.x=i,t.y=oe(e,"y",null),t.hasZ&&(t.z=oe(e,"z",null)),t.hasM&&(t.m=oe(e,"m",null)),t;const a=oe(e,"rings",null);if(null!==a)return t.rings=a,t;const r=oe(e,"paths",null);if(null!==r)return t.paths=r,t;const o=oe(e,"points",null);if(null!==o)return t.points=o,t;for(const s of["xmin","xmax","ymin","ymax","zmin","zmax","mmin","mmax"]){const n=oe(e,s,null);null!==n&&(t[s]=n)}return t}function de(e,t){for(const n of t)if(n===e)return!0;return!1}function ue(e){return!!e.layerDefinition&&(!!e.featureSet&&(!1!==de(e.layerDefinition.geometryType,["",null,"esriGeometryNull","esriGeometryPoint","esriGeometryPolyline","esriGeometryPolygon","esriGeometryMultipoint","esriGeometryEnvelope"])&&(!1!==ne(e.layerDefinition.fields)&&!1!==ne(e.featureSet.features))))}function ce(e){return"utc"===e?.toLowerCase()?"UTC":"unknown"===e?.toLowerCase()?"Unknown":e}async function me(t,n,i,o,s,l,d){const u=await t.getFeatureSetInfo();if(null===(u?.layerId??null))return null;if(!s.layerIdLookup.get(u.layerId))return null;const c=t.serviceUrl().replace(/\/FeatureServer/i,"/UtilityNetworkServer"),m=[];switch(i){case"connected":m.push("connectivity"),m.push("junction-edge-from-connectivity"),m.push("junction-edge-to-connectivity"),m.push("junction-edge-midspan-connectivity"),m.push("junction-junction-connectivity");break;case"container":case"content":m.push("containment");break;case"structure":case"attached":m.push("attachment");break;case"junctionedge":m.push("junction-edge-from-connectivity"),m.push("junction-edge-to-connectivity");break;case"midspan":m.push("junction-edge-midspan-connectivity");break;default:throw new a(l,r.InvalidParameter,d)}let p=null,y=!1;if(null!==o&&""!==o&&void 0!==o){for(const e of s.terminals)e.terminalName===o&&(p=e.terminalId);null===p&&(y=!0)}const w=[];if(!y){const a=new X({globalId:n.field(t.globalIdField),networkSourceId:s.layerIdLookup.get(u.layerId).sourceId,...p?{terminalId:p}:""}),r=await Y(c,new ee({types:m,elements:[a]}));let o=0;for(const t of r.associations){let n=null,r="",l="";if(t.fromNetworkElement?.globalId===a.globalId?(n=t.toNetworkElement,l="to"):t.toNetworkElement?.globalId===a.globalId&&(n=t.fromNetworkElement,l="from"),!n)continue;switch(i){case"attached":if("attachment"!==t.associationType)continue;if("to"!==l)continue;break;case"structure":if("attachment"!==t.associationType)continue;if("from"!==l)continue;break;case"container":if("containment"!==t.associationType)continue;if("from"!==l)continue;break;case"content":if("containment"!==t.associationType)continue;if("to"!==l)continue;break;case"connected":break;case"junctionedge":"junction-edge-to-connectivity"===t.associationType?r="to":"junction-edge-from-connectivity"===t.associationType&&(r="from");break;case"midspan":if("junction-edge-midspan-connectivity"!==t.associationType)continue}const f=s.sourceIdLookup.get(n.networkSourceId)?.className??"";w.push(new e({geometry:null,attributes:{objectId:o++,globalId:n.globalId,percentAlong:t.percentAlong??0,isContentVisible:t.isContentVisible?0:1,className:f,side:r}}))}}const h=new B({source:w,geometryType:null,objectIdField:"objectId",globalIdField:"globalId",fields:[new Q({name:"objectId",alias:"objectId",type:"oid"}),new Q({name:"globalId",alias:"globalId",type:"global-id"}),new Q({name:"percentAlong",alias:"percentAlong",type:"double"}),new Q({name:"side",alias:"side",type:"string"}),new Q({name:"isContentVisible",alias:"isContentVisible",type:"integer"}),new Q({name:"className",alias:"className",type:"string"})]});return f(h)}function pe(e){"async"===e.mode&&(e.functions.timezone=function(n,o){return e.standardFunctionAsync(n,o,(async(e,s,l)=>{if(p(l,1,2,n,o),y(l[0]))return"Unknown";if(w(l[0]))return"Unknown";if(h(l[0])){if(await l[0].load(),1===l.length||null===l[1])return l[0].datesInUnknownTimezone?ce("unknown"):ce(l[0].dateFieldsTimeZone);if(!(l[1]instanceof i)||!1===l[1].hasField("type"))throw new a(n,r.InvalidParameter,o);const e=l[1].field("type");if(!1===te(e))throw new a(n,r.InvalidParameter,o);switch(I(e).toLowerCase()){case"preferredtimezone":return ce(l[0].preferredTimeZone);case"editfieldsinfo":return ce(l[0].editFieldsInfo?.timeZone??null);case"timeinfo":return ce(l[0].timeInfo?.timeZone??null);case"field":if(l[1].hasField("fieldname")&&te(l[1].field("fieldname")))return ce(l[0].fieldTimeZone(I(l[1].field("fieldname"))))}throw new a(n,r.InvalidParameter,o)}const f=g(l[0],F(n));if(null===f)return null;const d=f.timeZone;return"system"===d?t.systemTimeZoneCanonicalName:"utc"===d.toLowerCase()?"UTC":"unknown"===d.toLowerCase()?"Unknown":d}))},e.functions.sqltimestamp=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{p(o,1,3,t,n);const s=o[0];if(b(s)){if(1===o.length)return s.toSQLWithKeyword();if(2===o.length)return s.changeTimeZone(I(o[1])).toSQLWithKeyword();throw new a(t,r.InvalidParameter,n)}if(w(s))return s.toSQLWithKeyword();if(h(s)){if(3!==o.length)throw new a(t,r.InvalidParameter,n);await s.load();const e=I(o[1]);if(w(o[2]))return o[2].toSQLWithKeyword();if(!1===b(o[2]))throw new a(t,r.InvalidParameter,n);const i=s.fieldTimeZone(e);return null==i?o[2].toSQLWithKeyword():o[2].changeTimeZone(i).toSQLWithKeyword()}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"sqltimestamp",min:2,max:4}),e.functions.featuresetbyid=function(t,n){return e.standardFunctionAsync(t,n,((e,i,o)=>{if(p(o,2,4,t,n),T(o[0])){const e=I(o[1]);let i=D(o[2],null);const s=x(D(o[3],!0));if(null===i&&(i=["*"]),!1===ne(i))throw new a(t,r.InvalidParameter,n);return o[0].featureSetById(e,s,i)}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"featuresetbyid",min:2,max:4}),e.functions.getfeatureset=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{if(p(o,1,2,t,n),E(o[0])){let e=D(o[1],"datasource");return null===e&&(e="datasource"),e=I(e).toLowerCase(),s(o[0].fullSchema(),e,t.lrucache,t.interceptor,t.spatialReference??null)}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"getfeatureset",min:1,max:2}),e.functions.featuresetbyportalitem=function(t,i){return e.standardFunctionAsync(t,i,((e,o,s)=>{if(p(s,2,5,t,i),null===s[0])throw new a(t,r.PortalRequired,i);if(s[0]instanceof n){const e=I(s[1]),n=I(s[2]);let o=D(s[3],null);const f=x(D(s[4],!0));if(null===o&&(o=["*"]),!1===ne(o))throw new a(t,r.InvalidParameter,i);let d;return d=t.services?.portal?t.services.portal:J.getDefault(),d=j(s[0],d),l(e,n,t.spatialReference??null,o,f,d,t.lrucache,t.interceptor)}if(!1===te(s[0]))throw new a(t,r.PortalRequired,i);const f=I(s[0]),d=I(s[1]);let u=D(s[2],null);const c=x(D(s[3],!0));if(null===u&&(u=["*"]),!1===ne(u))throw new a(t,r.InvalidParameter,i);return l(f,d,t.spatialReference??null,u,c,t.services?.portal??J.getDefault(),t.lrucache,t.interceptor)}))},e.signatures.push({name:"featuresetbyportalitem",min:2,max:5}),e.functions.featuresetbyname=function(t,n){return e.standardFunctionAsync(t,n,((e,i,o)=>{if(p(o,2,4,t,n),T(o[0])){const e=I(o[1]);let i=D(o[2],null);const s=x(D(o[3],!0));if(null===i&&(i=["*"]),!1===ne(i))throw new a(t,r.InvalidParameter,n);return o[0].featureSetByName(e,s,i)}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"featuresetbyname",min:2,max:4}),e.functions.featureset=function(t,n){return e.standardFunction(t,n,((e,o,s)=>{p(s,1,1,t,n);const l={layerDefinition:{geometryType:"",objectIdField:"",globalIdField:"",typeIdField:"",hasM:!1,hasZ:!1,fields:[]},featureSet:{geometryType:"",features:[]}};if(te(s[0])){const e=JSON.parse(s[0]);void 0!==e.layerDefinition?(l.layerDefinition=e.layerDefinition,l.featureSet=e.featureSet,e.layerDefinition.spatialReference&&(l.layerDefinition.spatialReference=e.layerDefinition.spatialReference)):(l.featureSet.features=e.features,l.featureSet.geometryType=e.geometryType,l.layerDefinition.geometryType=l.featureSet.geometryType,l.layerDefinition.objectIdField=e.objectIdFieldName??"",l.layerDefinition.typeIdField=e.typeIdFieldName,l.layerDefinition.globalIdField=e.globalIdFieldName,l.layerDefinition.fields=e.fields,e.spatialReference&&(l.layerDefinition.spatialReference=e.spatialReference))}else{if(!(s[0]instanceof i))throw new a(t,r.InvalidParameter,n);{const e=JSON.parse(s[0].castToText(!0)),i=oe(e,"layerdefinition");if(null!==i){l.layerDefinition.geometryType=oe(i,"geometrytype",""),l.featureSet.geometryType=l.layerDefinition.geometryType,l.layerDefinition.globalIdField=oe(i,"globalidfield",""),l.layerDefinition.objectIdField=oe(i,"objectidfield",""),l.layerDefinition.typeIdField=oe(i,"typeidfield",""),l.layerDefinition.hasZ=!0===oe(i,"hasz",!1),l.layerDefinition.hasM=!0===oe(i,"hasm",!1);const t=oe(i,"spatialreference");t&&(l.layerDefinition.spatialReference=le(t));const n=[];for(const e of oe(i,"fields",[])){const t={name:oe(e,"name",""),alias:oe(e,"alias",""),type:oe(e,"type",""),nullable:oe(e,"nullable",!0),editable:oe(e,"editable",!0),length:oe(e,"length",null),domain:se(oe(e,"domain"))};n.push(t)}l.layerDefinition.fields=n;const a=oe(e,"featureset");if(a){const e={};for(const t of n)e[t.name.toLowerCase()]=t.name;for(const t of oe(a,"features",[])){const n={},i=oe(t,"attributes",{});for(const t in i)n[e[t.toLowerCase()]]=i[t];l.featureSet.features.push({attributes:n,geometry:fe(oe(t,"geometry"))})}}}else{l.layerDefinition.hasZ=!0===oe(e,"hasz",!1),l.layerDefinition.hasM=!0===oe(e,"hasm",!1),l.layerDefinition.geometryType=oe(e,"geometrytype",""),l.featureSet.geometryType=l.layerDefinition.geometryType,l.layerDefinition.objectIdField=oe(e,"objectidfieldname",""),l.layerDefinition.typeIdField=oe(e,"typeidfieldname","");const i=oe(e,"spatialreference");i&&(l.layerDefinition.spatialReference=le(i));const o=[],s=oe(e,"fields",null);if(!ne(s))throw new a(t,r.InvalidParameter,n);for(const e of s){const t={name:oe(e,"name",""),alias:oe(e,"alias",""),type:oe(e,"type",""),nullable:oe(e,"nullable",!0),editable:oe(e,"editable",!0),length:oe(e,"length",null),domain:se(oe(e,"domain"))};o.push(t)}l.layerDefinition.fields=o;const f={};for(const e of o)f[e.name.toLowerCase()]=e.name;let d=oe(e,"features",null);if(ne(d))for(const e of d){const t={},n=oe(e,"attributes",{});for(const e in n)t[f[e.toLowerCase()]]=n[e];l.featureSet.features.push({attributes:t,geometry:fe(oe(e,"geometry",null))})}else d=null,l.featureSet.features=d}}}if(!1===ue(l))throw new a(t,r.InvalidParameter,n);return l.layerDefinition.geometryType||(l.layerDefinition.geometryType="esriGeometryNull"),z.create(l,t.spatialReference)}))},e.signatures.push({name:"featureset",min:1,max:1}),e.functions.filter=function(t,n){return e.standardFunctionAsync(t,n,(async(i,o,s)=>{if(p(s,2,2,t,n),ne(s[0])||N(s[0])){const e=[];let i,o=s[0];if(o instanceof m&&(o=o.toArray()),!A(s[1]))throw new a(t,r.InvalidParameter,n);i=s[1].createFunction(t);for(const t of o){const n=i(t);K(n)?!0===await n&&e.push(t):!0===n&&e.push(t)}return e}if(h(s[0])){const n=await s[0].load(),i=q.create(s[1],{fieldsIndex:n.getFieldsIndex(),timeZone:n.dateFieldsTimeZoneDefaultUTC}),a=i.getVariables();if(a.length>0){const n={};for(const i of a)n[i]=e.evaluateIdentifier(t,{name:i});i.parameters=n}return new U({parentfeatureset:s[0],whereclause:i})}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"filter",min:2,max:2}),e.functions.orderby=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{if(p(o,2,2,t,n),h(o[0])){const e=new W(o[1]);return new M({parentfeatureset:o[0],orderbyclause:e})}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"orderby",min:2,max:2}),e.functions.top=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{if(p(o,2,2,t,n),h(o[0]))return new R({parentfeatureset:o[0],topnum:o[1]});if(ne(o[0]))return v(o[1])>=o[0].length?o[0].slice():o[0].slice(0,v(o[1]));if(N(o[0]))return v(o[1])>=o[0].length()?o[0].slice():o[0].slice(0,v(o[1]));throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"top",min:2,max:2}),e.functions.first=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,a)=>{if(p(a,1,1,t,n),h(a[0])){const n=await a[0].first(e.abortSignal);if(null!==n){const e=o.createFromGraphicLikeObject(n.geometry,n.attributes,a[0],t.timeZone);return e._underlyingGraphic=n,e}return n}return ne(a[0])?0===a[0].length?null:a[0][0]:N(a[0])?0===a[0].length()?null:a[0].get(0):null}))},e.signatures.push({name:"first",min:1,max:1}),e.functions.attachments=function(t,n){return e.standardFunctionAsync(t,n,(async(e,o,s)=>{p(s,1,2,t,n);const l={minsize:-1,maxsize:-1,types:null,returnMetadata:!1};if(s.length>1)if(s[1]instanceof i){if(s[1].hasField("minsize")&&(l.minsize=v(s[1].field("minsize"))),s[1].hasField("metadata")&&(l.returnMetadata=x(s[1].field("metadata"))),s[1].hasField("maxsize")&&(l.maxsize=v(s[1].field("maxsize"))),s[1].hasField("types")){const e=S(s[1].field("types"),!1);e.length>0&&(l.types=e)}}else if(null!==s[1])throw new a(t,r.InvalidParameter,n);if(E(s[0])){const e=s[0]._layer;let n;if(h(e))n=e;else{if(null==e||!H(e))return[];n=f(e,t.spatialReference,["*"],!0,t.lrucache,t.interceptor)}return await n.load(),n.queryAttachments(s[0].field(n.objectIdField),l.minsize,l.maxsize,l.types,l.returnMetadata)}if(null===s[0])return[];throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"attachments",min:1,max:2}),e.functions.featuresetbyrelationshipname=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{p(o,2,4,t,n);const s=o[0],l=I(o[1]);let c=D(o[2],null);const m=x(D(o[3],!0));if(null===c&&(c=["*"]),!1===ne(c))throw new a(t,r.InvalidParameter,n);if(null===o[0])return null;if(!E(o[0]))throw new a(t,r.InvalidParameter,n);const y=s._layer;let w;if(h(y))w=y;else{if(null==y||!H(y))return null;w=f(y,t.spatialReference,["*"],!0,t.lrucache,t.interceptor)}w=await w.load();const g=w.relationshipMetaData().filter((e=>e.name===l));if(0===g.length)return null;if(void 0!==g[0].relationshipTableId&&null!==g[0].relationshipTableId&&g[0].relationshipTableId>-1)return d(w,g[0],s.field(w.objectIdField),w.spatialReference,c,m,t.lrucache,t.interceptor);let F=w.serviceUrl();if(!F)return null;F="/"===F.charAt(F.length-1)?F+g[0].relatedTableId.toString():F+"/"+g[0].relatedTableId.toString();const b=await u(F,w.spatialReference,c,m,t.lrucache,t.interceptor);await b.load();let T=b.relationshipMetaData();if(T=T.filter((e=>e.id===g[0].id)),!1===s.hasField(g[0].keyField)||null===s.field(g[0].keyField)){const e=await w.getFeatureByObjectId(s.field(w.objectIdField),[g[0].keyField]);if(e){const t=q.create(T[0].keyField+"= @id",{fieldsIndex:b.getFieldsIndex(),timeZone:b.dateFieldsTimeZoneDefaultUTC});return t.parameters={id:e.attributes[g[0].keyField]},b.filter(t)}return new O({parentfeatureset:b})}const N=q.create(T[0].keyField+"= @id",{fieldsIndex:b.getFieldsIndex(),timeZone:b.dateFieldsTimeZoneDefaultUTC});return N.parameters={id:s.field(g[0].keyField)},b.filter(N)}))},e.signatures.push({name:"featuresetbyrelationshipname",min:2,max:4}),e.functions.featuresetbyassociation=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{p(o,2,3,t,n);const s=o[0],l=I(D(o[1],"")).toLowerCase(),d=te(o[2])?I(o[2]):null;if(null===o[0])return null;if(!E(o[0]))throw new a(t,r.InvalidParameter,n);let u=s._layer;if(u instanceof B&&(u=f(u,t.spatialReference,["*"],!0,t.lrucache,t.interceptor)),null===u)return null;if(!1===h(u))return null;await u.load();const m=u.serviceUrl(),y=await c(m,t.spatialReference,!0);if(y.unVersion>=8)return await me(u,s,l,d,y,t,n);const w=y.associations;let g=null,F=null,b=!1;if(null!==d&&""!==d&&void 0!==d){for(const e of y.terminals)e.terminalName===d&&(F=e.terminalId);null===F&&(b=!0)}const T=w.getFieldsIndex(),x=T.get("TOGLOBALID").name,N=T.get("FROMGLOBALID").name,A=T.get("TOTERMINALID").name,v=T.get("FROMTERMINALID").name,S=T.get("FROMNETWORKSOURCEID").name,j=T.get("TONETWORKSOURCEID").name,U=T.get("ASSOCIATIONTYPE").name,M=T.get("ISCONTENTVISIBLE").name,R=T.get("OBJECTID").name;for(const t of u.fields)if("global-id"===t.type){g=s.field(t.name);break}let O=null,z=new C(new Q({name:"percentalong",alias:"percentalong",type:"double"}),q.create("0",{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC})),W=new C(new Q({name:"side",alias:"side",type:"string"}),q.create("''",{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC}));const H="globalid",V="globalId",_={};for(const t in y.lkp)_[t]=y.lkp[t].sourceId;const K=new Z(new Q({name:"classname",alias:"classname",type:"string"}),null,_);let J="";switch(l){case"midspan":{J=`((${x}='${g}') OR ( ${N}='${g}')) AND (${U} IN (5))`,K.codefield=q.create(`CASE WHEN (${x}='${g}') THEN ${S} ELSE ${j} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC});const e=G($.findField(w.fields,N));e.name=H,e.alias=H,O=new C(e,q.create(`CASE WHEN (${N}='${g}') THEN ${x} ELSE ${N} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC})),z=y.unVersion>=4?new P($.findField(w.fields,T.get("PERCENTALONG").name)):new C(new Q({name:"percentalong",alias:"percentalong",type:"double"}),q.create("0",{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC}));break}case"junctionedge":{J=`((${x}='${g}') OR ( ${N}='${g}')) AND (${U} IN (4,6))`,K.codefield=q.create(`CASE WHEN (${x}='${g}') THEN ${S} ELSE ${j} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC});const e=G($.findField(w.fields,N));e.name=H,e.alias=H,O=new C(e,q.create(`CASE WHEN (${N}='${g}') THEN ${x} ELSE ${N} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC})),W=new C(new Q({name:"side",alias:"side",type:"string"}),q.create(`CASE WHEN (${U}=4) THEN 'from' ELSE 'to' END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC}));break}case"connected":{let e=`${x}='@T'`,t=`${N}='@T'`;null!==F&&(e+=` AND ${A}=@A`,t+=` AND ${v}=@A`),J="(("+e+") OR ("+t+"))",J=L(J,"@T",g??""),e=L(e,"@T",g??""),null!==F&&(e=L(e,"@A",F.toString()),J=L(J,"@A",F.toString())),K.codefield=q.create("CASE WHEN "+e+` THEN ${S} ELSE ${j} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC});const n=G($.findField(w.fields,N));n.name=H,n.alias=H,O=new C(n,q.create("CASE WHEN "+e+` THEN ${N} ELSE ${x} END`,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC}));break}case"container":J=`${x}='${g}' AND ${U} = 2`,null!==F&&(J+=` AND ${A} = `+F.toString()),K.codefield=S,J="( "+J+" )",O=new k($.findField(w.fields,N),H,H);break;case"content":J=`(${N}='${g}' AND ${U} = 2)`,null!==F&&(J+=` AND ${v} = `+F.toString()),K.codefield=j,J="( "+J+" )",O=new k($.findField(w.fields,x),H,H);break;case"structure":J=`(${x}='${g}' AND ${U} = 3)`,null!==F&&(J+=` AND ${A} = `+F.toString()),K.codefield=S,J="( "+J+" )",O=new k($.findField(w.fields,N),H,V);break;case"attached":J=`(${N}='${g}' AND ${U} = 3)`,null!==F&&(J+=` AND ${v} = `+F.toString()),K.codefield=j,J="( "+J+" )",O=new k($.findField(w.fields,x),H,V);break;default:throw new a(t,r.InvalidParameter,n)}b&&(J="1 <> 1");return new $({parentfeatureset:w,adaptedFields:[new P($.findField(w.fields,R)),new P($.findField(w.fields,M)),O,W,K,z],extraFilter:J?q.create(J,{fieldsIndex:w.getFieldsIndex(),timeZone:w.dateFieldsTimeZoneDefaultUTC}):null})}))},e.signatures.push({name:"featuresetbyassociation",min:2,max:6}),e.functions.groupby=function(t,n){return e.standardFunctionAsync(t,n,(async(o,s,l)=>{if(p(l,3,3,t,n),!h(l[0]))throw new a(t,r.InvalidParameter,n);const f=await l[0].load(),d=[],u=[];let c=!1,m=[];if(te(l[1]))m.push(l[1]);else if(l[1]instanceof i)m.push(l[1]);else if(ne(l[1]))m=l[1];else{if(!N(l[1]))throw new a(t,r.InvalidParameter,n);m=l[1].toArray()}for(const e of m)if(te(e)){const t=q.create(I(e),{fieldsIndex:f.getFieldsIndex(),timeZone:f.dateFieldsTimeZoneDefaultUTC}),n=!0===V(t)?I(e):"%%%%FIELDNAME";d.push({name:n,expression:t}),"%%%%FIELDNAME"===n&&(c=!0)}else{if(!(e instanceof i))throw new a(t,r.InvalidParameter,n);{const i=e.hasField("name")?e.field("name"):"%%%%FIELDNAME",o=e.hasField("expression")?e.field("expression"):"";if("%%%%FIELDNAME"===i&&(c=!0),!i)throw new a(t,r.InvalidParameter,n);d.push({name:i,expression:q.create(o||i,{fieldsIndex:f.getFieldsIndex(),timeZone:f.dateFieldsTimeZoneDefaultUTC})})}}if(m=[],te(l[2]))m.push(l[2]);else if(ne(l[2]))m=l[2];else if(N(l[2]))m=l[2].toArray();else{if(!(l[2]instanceof i))throw new a(t,r.InvalidParameter,n);m.push(l[2])}for(const e of m){if(!(e instanceof i))throw new a(t,r.InvalidParameter,n);{const i=e.hasField("name")?e.field("name"):"",o=e.hasField("statistic")?e.field("statistic"):"",s=e.hasField("expression")?e.field("expression"):"";if(!i||!o||!s)throw new a(t,r.InvalidParameter,n);u.push({name:i,statistic:o.toLowerCase(),expression:q.create(s,{fieldsIndex:f.getFieldsIndex(),timeZone:f.dateFieldsTimeZoneDefaultUTC})})}}if(c){const e={};for(const n of f.fields)e[n.name.toLowerCase()]=1;for(const n of d)"%%%%FIELDNAME"!==n.name&&(e[n.name.toLowerCase()]=1);for(const n of u)"%%%%FIELDNAME"!==n.name&&(e[n.name.toLowerCase()]=1);let t=0;for(const n of d)if("%%%%FIELDNAME"===n.name){for(;1===e["field_"+t.toString()];)t++;e["field_"+t.toString()]=1,n.name="FIELD_"+t.toString()}}for(const n of d)re(n.expression,e,t);for(const n of u)re(n.expression,e,t);return l[0].groupby(d,u)}))},e.signatures.push({name:"groupby",min:3,max:3}),e.functions.distinct=function(t,n){return e.standardFunctionAsync(t,n,(async(o,s,l)=>{if(h(l[0])){p(l,2,2,t,n);const o=await l[0].load(),s=[];let f=[];if(te(l[1]))f.push(l[1]);else if(l[1]instanceof i)f.push(l[1]);else if(ne(l[1]))f=l[1];else{if(!N(l[1]))throw new a(t,r.InvalidParameter,n);f=l[1].toArray()}let d=!1;for(const e of f)if(te(e)){const t=q.create(I(e),{fieldsIndex:o.getFieldsIndex(),timeZone:o.dateFieldsTimeZoneDefaultUTC}),n=!0===V(t)?I(e):"%%%%FIELDNAME";s.push({name:n,expression:t}),"%%%%FIELDNAME"===n&&(d=!0)}else{if(!(e instanceof i))throw new a(t,r.InvalidParameter,n);{const i=e.hasField("name")?e.field("name"):"%%%%FIELDNAME",l=e.hasField("expression")?e.field("expression"):"";if("%%%%FIELDNAME"===i&&(d=!0),!i)throw new a(t,r.InvalidParameter,n);s.push({name:i,expression:q.create(l||i,{fieldsIndex:o.getFieldsIndex(),timeZone:o.dateFieldsTimeZoneDefaultUTC})})}}if(d){const e={};for(const n of o.fields)e[n.name.toLowerCase()]=1;for(const n of s)"%%%%FIELDNAME"!==n.name&&(e[n.name.toLowerCase()]=1);let t=0;for(const n of s)if("%%%%FIELDNAME"===n.name){for(;1===e["field_"+t.toString()];)t++;e["field_"+t.toString()]=1,n.name="FIELD_"+t.toString()}}for(const n of s)re(n.expression,e,t);return l[0].groupby(s,[])}return ae(l)}))},e.functions.getfeaturesetinfo=function(t,n){return e.standardFunctionAsync(t,n,(async(e,a,r)=>{if(p(r,1,1,t,n),!h(r[0]))return null;const o=await r[0].getFeatureSetInfo();return o?i.convertObjectToArcadeDictionary({layerId:o.layerId,layerName:o.layerName,itemId:o.itemId,serviceLayerUrl:o.serviceLayerUrl,webMapLayerId:o.webMapLayerId??null,webMapLayerTitle:o.webMapLayerTitle??null,className:null,objectClassId:null},F(t),!1,!1):null}))},e.signatures.push({name:"getfeaturesetinfo",min:1,max:1}),e.functions.filterbysubtypecode=function(t,n){return e.standardFunctionAsync(t,n,(async(e,i,o)=>{if(p(o,2,2,t,n),h(o[0])){const e=await o[0].load(),i=o[1];if(!ie(i))throw new a(t,r.InvalidParameter,n);if(e.subtypeField){const t=q.create(`${e.subtypeField}= ${o[1]}`,{fieldsIndex:e.getFieldsIndex(),timeZone:e.dateFieldsTimeZoneDefaultUTC});return new U({parentfeatureset:o[0],whereclause:t})}if(null===e.typeIdField||""===e.typeIdField)throw new a(t,r.FeatureSetDoesNotHaveSubtypes,n);const s=q.create(`${e.typeIdField}= ${o[1]}`,{fieldsIndex:e.getFieldsIndex(),timeZone:e.dateFieldsTimeZoneDefaultUTC});return new U({parentfeatureset:o[0],whereclause:s})}throw new a(t,r.InvalidParameter,n)}))},e.signatures.push({name:"filterbysubtypecode",min:2,max:2}))}export{pe as registerFunctions};
