/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeExecutionError as e,ExecutionErrorCodes as t}from"../executionError.js";import{shapeExtent as n}from"../kernel.js";import{E as r,D as s,l as i,f as a,p as o}from"../../chunks/languageUtils.js";import u from"../featureset/actions/SpatialFilter.js";import l from"../featureset/sources/Empty.js";import{invokeRemoteGeometryOp as c}from"../geometry/operatorsWorkerConnection.js";function f(a){return async(f,p,S)=>{if(s(S,2,2,f,p),null===(S=r(S))[0]&&null===S[1])return!1;if(o(S[0])){if(i(S[1]))return new u({parentfeatureset:S[0],relation:a,relationGeom:S[1]});if(null===S[1])return new l({parentfeatureset:S[0]});throw new e(f,t.InvalidParameter,p)}if(i(S[0])){if(i(S[1])){switch(a){case"esriSpatialRelEnvelopeIntersects":{const e=n(S[0]),t=n(S[1]);return null!=e&&null!=t&&c("intersects",[e.toJSON(),t.toJSON()])}case"esriSpatialRelIntersects":return c("intersects",[S[0].toJSON(),S[1].toJSON()]);case"esriSpatialRelContains":return c("contains",[S[0].toJSON(),S[1].toJSON()]);case"esriSpatialRelOverlaps":return c("overlaps",[S[0].toJSON(),S[1].toJSON()]);case"esriSpatialRelWithin":return c("within",[S[0].toJSON(),S[1].toJSON()]);case"esriSpatialRelTouches":return c("touches",[S[0].toJSON(),S[1].toJSON()]);case"esriSpatialRelCrosses":return c("crosses",[S[0].toJSON(),S[1].toJSON()])}throw new e(f,t.InvalidParameter,p)}if(o(S[1]))return new u({parentfeatureset:S[1],relation:a,relationGeom:S[0]});if(null===S[1])return!1;throw new e(f,t.InvalidParameter,p)}if(null===S[0]){if(o(S[1]))return new l({parentfeatureset:S[1]});if(i(S[1])||null===S[1])return!1}throw new e(f,t.InvalidParameter,p)}}function p(n){"async"===n.mode&&(n.functions.intersects=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelIntersects"))},n.functions.envelopeintersects=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelEnvelopeIntersects"))},n.signatures.push({name:"envelopeintersects",min:2,max:2}),n.functions.contains=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelContains"))},n.functions.overlaps=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelOverlaps"))},n.functions.within=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelWithin"))},n.functions.touches=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelTouches"))},n.functions.crosses=function(e,t){return n.standardFunctionAsync(e,t,f("esriSpatialRelCrosses"))},n.functions.relate=function(u,f){return n.standardFunctionAsync(u,f,(async(n,p,S)=>{if(S=r(S),s(S,3,3,u,f),i(S[0])&&i(S[1]))return c("relate",[S[0].toJSON(),S[1].toJSON(),a(S[2])]);if(i(S[0])&&null===S[1])return!1;if(i(S[1])&&null===S[0])return!1;if(o(S[0])&&null===S[1])return new l({parentfeatureset:S[0]});if(o(S[1])&&null===S[0])return new l({parentfeatureset:S[1]});if(o(S[0])&&i(S[1]))return S[0].relate(S[1],a(S[2]));if(o(S[1])&&i(S[0]))return S[1].relate(S[0],a(S[2]));if(null===S[0]&&null===S[1])return!1;throw new e(u,t.InvalidParameter,f)}))})}export{p as registerFunctions};
