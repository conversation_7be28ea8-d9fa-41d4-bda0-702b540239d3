/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeDate as n}from"../ArcadeDate.js";import{ArcadeExecutionError as t,ExecutionErrorCodes as e}from"../executionError.js";import{D as a,p as r,m as i,J as o,K as s}from"../../chunks/languageUtils.js";import{calculateStat as c}from"./fieldStats.js";import{SqlTimeStampOffset as u}from"../../core/sql/SqlTimestampOffset.js";import f from"../../core/sql/WhereClause.js";import{isArray as d,isString as m}from"../../support/guards.js";async function l(n,t,e,a){if(1===e.length){if(d(e[0]))return c(n,e[0],o(e[1],-1));if(i(e[0]))return c(n,e[0].toArray(),o(e[1],-1))}else if(2===e.length){if(d(e[0]))return c(n,e[0],o(e[1],-1));if(i(e[0]))return c(n,e[0].toArray(),o(e[1],-1));if(r(e[0])){const r=await e[0].load(),i=await p(f.create(e[1],{fieldsIndex:r.getFieldsIndex(),timeZone:r.dateFieldsTimeZoneDefaultUTC}),a,t);return g(t,await e[0].calculateStatistic(n,i,o(e[2],1e3),t.abortSignal))}}else if(3===e.length&&r(e[0])){const r=await e[0].load(),i=await p(f.create(e[1],{fieldsIndex:r.getFieldsIndex(),timeZone:r.dateFieldsTimeZoneDefaultUTC}),a,t);return g(t,await e[0].calculateStatistic(n,i,o(e[2],1e3),t.abortSignal))}return c(n,e,-1)}function g(t,e){return e instanceof u?n.fromReaderAsTimeStampOffset(e.toStorageFormat()):e instanceof Date?n.dateJSAndZoneToArcadeDate(e,s(t)):e}async function p(n,t,e){const a=n.getVariables();if(a.length>0){const r={};for(const n of a)r[n]=t.evaluateIdentifier(e,{name:n});n.parameters=r}return n}function y(n){"async"===n.mode&&(n.functions.stdev=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("stdev",t,r,n)))},n.functions.variance=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("variance",t,r,n)))},n.functions.average=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("mean",t,r,n)))},n.functions.mean=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("mean",t,r,n)))},n.functions.sum=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("sum",t,r,n)))},n.functions.min=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("min",t,r,n)))},n.functions.max=function(t,e){return n.standardFunctionAsync(t,e,((e,a,r)=>l("max",t,r,n)))},n.functions.count=function(o,s){return n.standardFunctionAsync(o,s,(async(n,c,u)=>{if(a(u,1,1,o,s),r(u[0]))return u[0].count(n.abortSignal);if(d(u[0])||m(u[0]))return u[0].length;if(i(u[0]))return u[0].length();throw new t(o,e.InvalidParameter,s)}))})}export{y as registerFunctions};
