/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import n from"../Dictionary.js";import{ArcadeExecutionError as a,ExecutionErrorCodes as e}from"../executionError.js";import{D as t,n as r,O as o,f as i,p as c,a0 as s,T as d,K as u,Q as m,a1 as f,P as l,a2 as y,J as v,s as w,S as p}from"../../chunks/languageUtils.js";import{layerFieldEsriConstants as h}from"../featureset/support/shared.js";function T(a,e){return a&&a.domain?"coded-value"===a.domain.type||"codedValue"===a.domain.type?n.convertObjectToArcadeDictionary({type:"codedValue",name:a.domain.name,dataType:h[a.field.type],codedValues:a.domain.codedValues.map((n=>({name:n.name,code:n.code})))},u(e)):n.convertObjectToArcadeDictionary({type:"range",name:a.domain.name,dataType:h[a.field.type],min:a.domain.minValue,max:a.domain.maxValue},u(e)):null}function b(h){"async"===h.mode&&(h.functions.domain=function(n,d){return h.standardFunctionAsync(n,d,(async(u,m,f)=>{if(t(f,2,3,n,d),r(f[0])){return T(o(f[0],i(f[1]),void 0===f[2]?void 0:f[2]),n)}if(c(f[0])){await f[0]._ensureLoaded();return T(s(i(f[1]),f[0],null,void 0===f[2]?void 0:f[2]),n)}throw new a(n,e.InvalidParameter,d)}))},h.functions.subtypes=function(o,i){return h.standardFunctionAsync(o,i,(async(s,m,f)=>{if(t(f,1,1,o,i),r(f[0])){const a=d(f[0]);return a?n.convertObjectToArcadeDictionary(a,u(o)):null}if(c(f[0])){await f[0]._ensureLoaded();const a=f[0].subtypeMetaData();return a?n.convertObjectToArcadeDictionary(a,u(o)):null}throw new a(o,e.InvalidParameter,i)}))},h.functions.domainname=function(n,o){return h.standardFunctionAsync(n,o,(async(d,u,l)=>{if(t(l,2,4,n,o),r(l[0]))return m(l[0],i(l[1]),l[2],void 0===l[3]?void 0:l[3]);if(c(l[0])){await l[0]._ensureLoaded();const n=s(i(l[1]),l[0],null,void 0===l[3]?void 0:l[3]);return f(n,l[2])}throw new a(n,e.InvalidParameter,o)}))},h.signatures.push({name:"domainname",min:2,max:4}),h.functions.domaincode=function(n,o){return h.standardFunctionAsync(n,o,(async(d,u,m)=>{if(t(m,2,4,n,o),r(m[0]))return l(m[0],i(m[1]),m[2],void 0===m[3]?void 0:m[3]);if(c(m[0])){await m[0]._ensureLoaded();const n=s(i(m[1]),m[0],null,void 0===m[3]?void 0:m[3]);return y(n,m[2])}throw new a(n,e.InvalidParameter,o)}))},h.signatures.push({name:"domaincode",min:2,max:4}),h.functions.text=function(n,r){return h.standardFunctionAsync(n,r,(async(o,i,s)=>{if(t(s,1,2,n,r),c(s[0])){const t=v(s[1],"");if(""===t)return s[0].castToText();if("schema"===t.toLowerCase())return s[0].convertToText("schema",o.abortSignal);if("featureset"===t.toLowerCase())return s[0].convertToText("featureset",o.abortSignal);throw new a(n,e.InvalidParameter,r)}return w(s[0],s[1])}))},h.functions.gdbversion=function(n,o){return h.standardFunctionAsync(n,o,(async(i,s,d)=>{if(t(d,1,1,n,o),r(d[0]))return d[0].gdbVersion();if(c(d[0])){return(await d[0].load()).gdbVersion}throw new a(n,e.InvalidParameter,o)}))},h.functions.schema=function(o,i){return h.standardFunctionAsync(o,i,(async(s,d,m)=>{if(t(m,1,1,o,i),c(m[0]))return await m[0].load(),n.convertObjectToArcadeDictionary(m[0].schema(),u(o));if(r(m[0])){const a=p(m[0]);return a?n.convertObjectToArcadeDictionary(a,u(o)):null}throw new a(o,e.InvalidParameter,i)}))})}export{b as registerFunctions};
