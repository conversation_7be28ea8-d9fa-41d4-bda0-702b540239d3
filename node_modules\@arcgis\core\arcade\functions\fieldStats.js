/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{Y as e,w as t,B as r}from"../../chunks/languageUtils.js";import{isNumber as n,isString as s}from"../../support/guards.js";function a(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r];return t/e.length}function u(e){const t=a(e);let r=0;for(let n=0;n<e.length;n++)r+=(t-e[n])**2;return r/e.length}function c(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r];return t}function o(e,a){const u=[],c={},o=[];for(let i=0;i<e.length;i++){if(void 0!==e[i]&&null!==e[i]&&e[i]!==t){const t=e[i];if(n(t)||s(t))void 0===c[t]&&(u.push(t),c[t]=1);else{let e=!1;for(let n=0;n<o.length;n++)!0===r(o[n],t)&&(e=!0);!1===e&&(o.push(t),u.push(t))}}if(u.length>=a&&-1!==a)return u}return u}function i(e){switch(e.toLowerCase()){case"distinct":return"distinct";case"avg":case"mean":return"avg";case"min":return"min";case"sum":return"sum";case"max":return"max";case"stdev":case"stddev":return"stddev";case"var":case"variance":return"var";case"count":return"count"}return""}function l(t,r,n=1e3){switch(t.toLowerCase()){case"distinct":return o(r,n);case"avg":case"mean":return a(e(r));case"min":return Math.min.apply(Math,e(r));case"sum":return c(e(r));case"max":return Math.max.apply(Math,e(r));case"stdev":case"stddev":return Math.sqrt(u(e(r)));case"var":case"variance":return u(e(r));case"count":return r.length}return 0}export{l as calculateStat,i as decodeStatType};
