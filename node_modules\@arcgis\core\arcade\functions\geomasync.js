/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import n from"../ArcadePortal.js";import t from"../Dictionary.js";import{ArcadeExecutionError as e,ExecutionErrorCodes as r}from"../executionError.js";import{cloneGeometry as a}from"../kernel.js";import{E as i,D as o,g as s,k as c,j as l,f as u,m as f,u as d,p as w,F as m,G as y,H as h,t as p,e as g,J as N,K as S}from"../../chunks/languageUtils.js";import{getPortal as O}from"../portalUtils.js";import{commonRelationsCheck as v,planarLength3D as J,measureToCoordinateFunc as P,pointToCoordinateFunc as A,distanceToCoordinateFunc as I}from"../geometry/functions.js";import{invokeRemoteGeometryOp as F}from"../geometry/operatorsWorkerConnection.js";import j from"../../geometry/Extent.js";import b from"../../geometry/Geometry.js";import R from"../../geometry/Multipoint.js";import x from"../../geometry/Point.js";import D from"../../geometry/Polygon.js";import L from"../../geometry/Polyline.js";import{fromJSON as C}from"../../geometry/support/jsonUtils.js";import k from"../../portal/Portal.js";import{lookupUser as E}from"../../portal/support/utils.js";import{isArray as U}from"../../support/guards.js";function M(n){if(null==n)return n;switch(typeof n){case"string":case"number":return n;default:throw new e(null,r.InvalidParameter,null)}}function T(T){"async"===T.mode&&(T.functions.disjoint=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null===a[0]||null===a[1]||F("disjoint",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.intersects=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("intersects",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.touches=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("touches",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.crosses=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("crosses",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.within=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("within",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.contains=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("contains",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.overlaps=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(a=i(a),v(a,n,t),null!==a[0]&&null!==a[1]&&F("overlaps",[a[0].toJSON(),a[1].toJSON()]))))},T.functions.equals=function(n,t){return T.standardFunctionAsync(n,t,((e,r,a)=>(o(a,2,2,n,t),a[0]===a[1]||(a[0]instanceof b&&a[1]instanceof b?F("equals",[a[0].toJSON(),a[1].toJSON()]):(s(a[0])&&s(a[1])||!!(c(a[0])&&c(a[1])||l(a[0])&&l(a[1])))&&a[0].equals(a[1])))))},T.functions.relate=function(n,t){return T.standardFunctionAsync(n,t,((a,s,c)=>{if(c=i(c),o(c,3,3,n,t),c[0]instanceof b&&c[1]instanceof b)return F("relate",[c[0].toJSON(),c[1].toJSON(),u(c[2])]);if(c[0]instanceof b&&null===c[1])return!1;if(c[1]instanceof b&&null===c[0])return!1;if(null===c[0]&&null===c[1])return!1;throw new e(n,r.InvalidParameter,t)}))},T.functions.intersection=function(n,t){return T.standardFunctionAsync(n,t,(async(e,r,a)=>(a=i(a),v(a,n,t),null===a[0]||null===a[1]?null:C(await F("intersection",[a[0].toJSON(),a[1].toJSON()])))))},T.functions.union=function(n,t){return T.standardFunctionAsync(n,t,(async(o,s,c)=>{if(0===(c=i(c)).length)throw new e(n,r.WrongNumberOfParameters,t);const l=[];if(1===c.length)if(U(c[0])){for(const a of i(c[0]))if(null!==a){if(!(a instanceof b))throw new e(n,r.InvalidParameter,t);l.push(a.toJSON())}}else{if(!f(c[0])){if(c[0]instanceof b)return d(a(c[0]),n.spatialReference);if(null===c[0])return null;throw new e(n,r.InvalidParameter,t)}for(const a of i(c[0].toArray()))if(null!==a){if(!(a instanceof b))throw new e(n,r.InvalidParameter,t);l.push(a.toJSON())}}else for(const a of c)if(null!==a){if(!(a instanceof b))throw new e(n,r.InvalidParameter,t);l.push(a.toJSON())}return 0===l.length?null:C(await F("union",[l]))}))},T.functions.difference=function(n,t){return T.standardFunctionAsync(n,t,(async(e,r,o)=>(o=i(o),v(o,n,t),null===o[0]?null:null===o[1]?a(o[0]):C(await F("difference",[o[0].toJSON(),o[1].toJSON()])))))},T.functions.symmetricdifference=function(n,t){return T.standardFunctionAsync(n,t,(async(e,r,o)=>(o=i(o),v(o,n,t),null===o[0]&&null===o[1]?null:null===o[0]?a(o[1]):null===o[1]?a(o[0]):C(await F("symmetricDifference",[o[0].toJSON(),o[1].toJSON()])))))},T.functions.clip=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,2,n,t),!(c[1]instanceof j)&&null!==c[1])throw new e(n,r.InvalidParameter,t);if(null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);return null===c[1]?null:C(await F("clip",[c[0].toJSON(),c[1].toJSON()]))}))},T.functions.cut=function(n,t){return T.standardFunctionAsync(n,t,(async(s,c,l)=>{if(l=i(l),o(l,2,2,n,t),!(l[1]instanceof L)&&null!==l[1])throw new e(n,r.InvalidParameter,t);if(null===l[0])return[];if(!(l[0]instanceof b))throw new e(n,r.InvalidParameter,t);return null===l[1]?[a(l[0])]:(await F("cut",[l[0].toJSON(),l[1].toJSON()])).map((n=>C(n)))}))},T.functions.area=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(o(c,1,2,n,t),c=i(c),w(c[0])){const a=await c[0].sumArea(M(c[1]),!1,n.abortSignal);if(n.abortSignal.aborted)throw new e(n,r.Cancelled,t);return a}let l=c[0];if((U(l)||f(l))&&(l=m(c[0],n.spatialReference)),null===l)return 0;if(!(l instanceof b))throw new e(n,r.InvalidParameter,t);return F("area",[l.toJSON(),M(c[1])])}))},T.functions.areageodetic=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(o(c,1,2,n,t),c=i(c),w(c[0])){const a=await c[0].sumArea(M(c[1]),!0,n.abortSignal);if(n.abortSignal.aborted)throw new e(n,r.Cancelled,t);return a}let l=c[0];if((U(c[0])||f(c[0]))&&(l=m(c[0],n.spatialReference)),null===l)return 0;if(!(l instanceof b))throw new e(n,r.InvalidParameter,t);return F("geodeticArea",[l.toJSON(),M(c[1])])}))},T.functions.length=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(o(c,1,2,n,t),c=i(c),w(c[0])){const a=await c[0].sumLength(M(c[1]),!1,n.abortSignal);if(n.abortSignal.aborted)throw new e(n,r.Cancelled,t);return a}let l=c[0];if((U(c[0])||f(c[0]))&&(l=y(c[0],n.spatialReference)),null===l)return 0;if(!(l instanceof b))throw new e(n,r.InvalidParameter,t);return F("length",[l.toJSON(),M(c[1])])}))},T.functions.length3d=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(o(c,1,2,n,t),null===(c=i(c))[0])return 0;let l=c[0];if((U(c[0])||f(c[0]))&&(l=y(c[0],n.spatialReference)),null===l)return 0;if(!(l instanceof b))throw new e(n,r.InvalidParameter,t);if(!0===l.hasZ){const{convertFromSpatialReferenceUnit:n,toLengthUnit:t}=await import("../geometry/unitConversion.js"),e=J(l);return n(l.spatialReference,t(c[1]),e)}return F("length",[l.toJSON(),M(c[1])])}))},T.functions.lengthgeodetic=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(o(c,1,2,n,t),c=i(c),w(c[0])){const a=await c[0].sumLength(M(c[1]),!0,n.abortSignal);if(n.abortSignal.aborted)throw new e(n,r.Cancelled,t);return a}let l=c[0];if((U(c[0])||f(c[0]))&&(l=y(c[0],n.spatialReference)),null===l)return 0;if(!(l instanceof b))throw new e(n,r.InvalidParameter,t);return F("geodeticLength",[l.toJSON(),M(c[1])])}))},T.functions.distance=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{c=i(c),o(c,2,3,n,t);let l=c[0];if((U(c[0])||f(c[0]))&&(l=h(c[0],n.spatialReference)),!(l instanceof b))throw new e(n,r.InvalidParameter,t);let u=c[1];if((U(c[1])||f(c[1]))&&(u=h(c[1],n.spatialReference)),!(u instanceof b))throw new e(n,r.InvalidParameter,t);return F("distance",[l.toJSON(),u.toJSON(),M(c[2])])}))},T.functions.distancegeodetic=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{c=i(c),o(c,2,3,n,t);const l=c[0];if(!(l instanceof x))throw new e(n,r.InvalidParameter,t);const u=c[1];if(!(u instanceof x))throw new e(n,r.InvalidParameter,t);const f=new L({paths:[],spatialReference:l.spatialReference});return f.addPath([l,u]),F("geodeticLength",[f.toJSON(),M(c[2])])}))},T.functions.densify=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,3,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);const l=p(c[1]);if(isNaN(l))throw new e(n,r.InvalidParameter,t);if(l<=0)throw new e(n,r.InvalidParameter,t);switch(c[0].type){case"polygon":case"polyline":case"extent":return C(await F("densify",[c[0].toJSON(),l,M(c[2])]));default:return c[0]}}))},T.functions.densifygeodetic=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,3,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);const l=p(c[1]);if(isNaN(l))throw new e(n,r.InvalidParameter,t);if(l<=0)throw new e(n,r.InvalidParameter,t);switch(c[0].type){case"polygon":case"polyline":case"extent":return C(await F("geodeticDensify",[c[0].toJSON(),l,M(c[2])]));default:return c[0]}}))},T.functions.generalize=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,4,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);const l=p(c[1]);if(isNaN(l))throw new e(n,r.InvalidParameter,t);const u=g(N(c[2],!0));return C(await F("generalize",[c[0].toJSON(),l,M(c[3]),{removeDegenerateParts:u}]))}))},T.functions.buffer=function(n,t){return T.standardFunctionAsync(n,t,(async(s,c,l)=>{if(l=i(l),o(l,2,3,n,t),null===l[0])return null;if(!(l[0]instanceof b))throw new e(n,r.InvalidParameter,t);const u=p(l[1]);if(isNaN(u))throw new e(n,r.InvalidParameter,t);return 0===u?a(l[0]):C(await F("buffer",[l[0].toJSON(),u,M(l[2])]))}))},T.functions.buffergeodetic=function(n,t){return T.standardFunctionAsync(n,t,(async(s,c,l)=>{if(l=i(l),o(l,2,3,n,t),null===l[0])return null;if(!(l[0]instanceof b))throw new e(n,r.InvalidParameter,t);const u=p(l[1]);if(isNaN(u))throw new e(n,r.InvalidParameter,t);return 0===u?a(l[0]):C(await F("geodesicBuffer",[l[0].toJSON(),u,M(l[2])]))}))},T.functions.offset=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,6,n,t),null===c[0])return null;if(!(c[0]instanceof D||c[0]instanceof L))throw new e(n,r.InvalidParameter,t);const l=p(c[1]);if(isNaN(l))throw new e(n,r.InvalidParameter,t);const f=u(c[3]??"round").toLowerCase();let d;switch(f){case"round":case"bevel":case"miter":case"square":d=f;break;default:d="round"}const w=p(N(c[4],10));if(isNaN(w))throw new e(n,r.InvalidParameter,t);const m=p(N(c[5],0));if(isNaN(m))throw new e(n,r.InvalidParameter,t);return C(await F("offset",[c[0].toJSON(),l,M(c[2]),{joins:d,miterLimit:w,flattenError:m}]))}))},T.functions.rotate=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,2,3,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);const l=c[0]instanceof j?D.fromExtent(c[0]):c[0],u=p(c[1]);if(isNaN(u))throw new e(n,r.InvalidParameter,t);const f=N(c[2],null);if(null===f){const n="point"===l.type?l:l.extent?.center;return C(await F("rotate",[l.toJSON(),u,n?.x,n?.y]))}if(f instanceof x)return C(await F("rotate",[l.toJSON(),u,f.x,f.y]));throw new e(n,r.InvalidParameter,t)}))},T.functions.centroid=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,1,2,n,t),null===c[0])return null;const l=u(c[1]??"geometric").toLowerCase();if("geometric"!==l&&"labelpoint"!==l)throw new e(n,r.InvalidParameter,t);let d=c[0];if((U(c[0])||f(c[0]))&&(d="geometric"===l?h(c[0],n.spatialReference):m(c[0],n.spatialReference),null===d))return null;if(!(d instanceof b))throw new e(n,r.InvalidParameter,t);return C("geometric"===l?await F("centroid",[d.toJSON()]):await F("labelPoint",[d.toJSON()]))}))},T.functions.measuretocoordinate=function(n,t){return T.standardFunctionAsync(n,t,P)},T.functions.pointtocoordinate=function(n,t){return T.standardFunctionAsync(n,t,A)},T.functions.distancetocoordinate=function(n,t){return T.standardFunctionAsync(n,t,I)},T.functions.multiparttosinglepart=function(n,t){return T.standardFunctionAsync(n,t,(async(s,c,l)=>{if(l=i(l),o(l,1,1,n,t),null===l[0])return null;if(!(l[0]instanceof b))throw new e(n,r.InvalidParameter,t);if(l[0]instanceof x)return[d(a(l[0]),n.spatialReference)];if(l[0]instanceof j)return[d(a(l[0]),n.spatialReference)];const u=C(await F("simplify",[l[0].toJSON()]));if(u instanceof D){const n=[],t=[];for(let e=0;e<u.rings.length;e++)if(u.isClockwise(u.rings[e])){const t=C({rings:[u.rings[e]],hasZ:!0===u.hasZ,hasM:!0===u.hasM,spatialReference:u.spatialReference.toJSON()});n.push(t)}else t.push({ring:u.rings[e],pt:u.getPoint(e,0)});for(let e=0;e<t.length;e++)for(let r=0;r<n.length;r++)if(n[r].contains(t[e].pt)){n[r].addRing(t[e].ring);break}return n}if(u instanceof L){const n=[];for(let t=0;t<u.paths.length;t++){const e=C({paths:[u.paths[t]],hasZ:!0===u.hasZ,hasM:!0===u.hasM,spatialReference:u.spatialReference.toJSON()});n.push(e)}return n}if(l[0]instanceof R){const t=[],e=d(a(l[0]),n.spatialReference);for(let n=0;n<e.points.length;n++)t.push(e.getPoint(n));return t}return null}))},T.functions.issimple=function(n,t){return T.standardFunctionAsync(n,t,((a,s,c)=>{if(c=i(c),o(c,1,1,n,t),null===c[0])return!0;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);return F("isSimple",[c[0].toJSON()])}))},T.functions.simplify=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,1,1,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);return C(await F("simplify",[c[0].toJSON()]))}))},T.functions.convexhull=function(n,t){return T.standardFunctionAsync(n,t,(async(a,s,c)=>{if(c=i(c),o(c,1,1,n,t),null===c[0])return null;if(!(c[0]instanceof b))throw new e(n,r.InvalidParameter,t);return C(await F("convexHull",[c[0].toJSON()]))}))},T.functions.getuser=function(a,i){return T.standardFunctionAsync(a,i,(async(s,c,l)=>{o(l,0,2,a,i);let f=N(l[1],""),d=!0===f;if(f=!0===f||!1===f?"":u(f),0===l.length||l[0]instanceof n){let n;n=a.services?.portal?a.services.portal:k.getDefault(),l.length>0&&(n=O(l[0],n));const e=await E(n,f,d);if(e){const n=JSON.parse(JSON.stringify(e));for(const t of["lastLogin","created","modified"])void 0!==n[t]&&null!==n[t]&&(n[t]=new Date(n[t]));return t.convertObjectToArcadeDictionary(n,S(a))}return null}let m=null;if(w(l[0])&&(m=l[0]),m){if(d=!1,f)return null;await m.load();const e=await m.getOwningSystemUrl();if(!e){if(!f){const n=await m.getIdentityUser();return n?t.convertObjectToArcadeDictionary({username:n},S(a)):null}return null}let r;r=a.services?.portal?a.services.portal:k.getDefault(),r=O(new n(e),r);const i=await E(r,f,d);if(i){const n=JSON.parse(JSON.stringify(i));for(const t of["lastLogin","created","modified"])void 0!==n[t]&&null!==n[t]&&(n[t]=new Date(n[t]));return t.convertObjectToArcadeDictionary(n,S(a))}return null}throw new e(a,r.InvalidParameter,i)}))},T.functions.nearestcoordinate=function(n,a){return T.standardFunctionAsync(n,a,(async(s,c,l)=>{if(l=i(l),o(l,2,2,n,a),!(l[0]instanceof b||null===l[0]))throw new e(n,r.InvalidParameter,a);if(!(l[1]instanceof x||null===l[1]))throw new e(n,r.InvalidParameter,a);if(null===l[0]||null===l[1])return null;const u=l[0]instanceof j?D.fromExtent(l[0]):l[0],f=await F("getNearestCoordinate",[u.toJSON(),l[1].toJSON(),{calculateLeftRightSide:!0}]);return null===f?null:t.convertObjectToArcadeDictionary({coordinate:C(f.coordinate),distance:f.distance,sideOfLine:0===f.distance?"straddle":f.rightSide?"right":"left"},S(n),!1,!0)}))},T.functions.nearestvertex=function(n,a){return T.standardFunctionAsync(n,a,(async(s,c,l)=>{if(l=i(l),o(l,2,2,n,a),!(l[0]instanceof b||null===l[0]))throw new e(n,r.InvalidParameter,a);if(!(l[1]instanceof x||null===l[1]))throw new e(n,r.InvalidParameter,a);if(null===l[0]||null===l[1])return null;const u=l[0]instanceof j?D.fromExtent(l[0]):l[0],f=await F("getNearestVertex",[u.toJSON(),l[1].toJSON()]);return null===f?null:t.convertObjectToArcadeDictionary({coordinate:C(f.coordinate),distance:f.distance,sideOfLine:0===f.distance?"straddle":f.rightSide?"right":"left"},S(n),!1,!0)}))})}export{T as registerFunctions};
