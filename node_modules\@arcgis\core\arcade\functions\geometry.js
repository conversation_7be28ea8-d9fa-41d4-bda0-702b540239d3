/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{geometryMember as e,getNestedOptionalValue as t}from"../containerUtils.js";import n from"../Dictionary.js";import{ArcadeExecutionError as r,ExecutionErrorCodes as a}from"../executionError.js";import i from"../Feature.js";import l from"../ImmutablePointArray.js";import{D as o,m as s,u as f,U as c,E as u,n as m,w as p,q as h,l as w,f as g,g as R,k as y,j as d,i as P,d as v,K as I,V as b,z as O,B as j,G as S}from"../../chunks/languageUtils.js";import{angle2D as N,angleBetween2D as x,bearing2D as J,bearingBetween2D as F,pathsSelfIntersecting as k}from"./centroid.js";import{constructGeometryFromDictionary as Z}from"../geometry/constructors.js";import z from"../../geometry/Extent.js";import W from"../../geometry/Geometry.js";import q from"../../geometry/Multipoint.js";import A from"../../geometry/Point.js";import D from"../../geometry/Polygon.js";import G from"../../geometry/Polyline.js";import{isClockwise as M}from"../../geometry/support/coordsUtils.js";import{fromJSON as U}from"../../geometry/support/jsonUtils.js";import{isArray as L,isString as E,isNumber as _,isBoolean as T}from"../../support/guards.js";function B(B,C){B.ringisclockwise=function(e,t){return C(e,t,((n,i,f)=>{o(f,1,1,e,t);let c=[];if(null===f[0])return!1;if(L(f[0]))for(const l of f[0]){if(!(l instanceof A))throw new r(e,a.InvalidParameter,t);c.push(l.hasZ?l.hasM?[l.x,l.y,l.z,l.m]:[l.x,l.y,l.z]:[l.x,l.y])}else if(f[0]instanceof l)c=f[0]._elements;else{if(!s(f[0]))throw new r(e,a.InvalidParameter,t);for(const n of f[0].toArray()){if(!(n instanceof A))throw new r(e,a.InvalidParameter,t);c.push(n.hasZ?n.hasM?[n.x,n.y,n.z,n.m]:[n.x,n.y,n.z]:[n.x,n.y])}}return!(c.length<3)&&M(c)}))},B.polygon=function(e,t){return C(e,t,((i,l,s)=>{let u;if(o(s,1,1,e,t),s[0]instanceof n){const t=f(Z(s[0],e.spatialReference,"polygon"),e.spatialReference);if(null==t)return null;u=t}else if(s[0]instanceof D)u=U(s[0].toJSON());else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),u=f(new D(t),e.spatialReference)}if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(u)}))},B.polyline=function(e,t){return C(e,t,((i,l,s)=>{let u;if(o(s,1,1,e,t),s[0]instanceof n){const t=f(Z(s[0],e.spatialReference,"polyline"),e.spatialReference);if(null==t)return null;u=t}else if(s[0]instanceof G)u=U(s[0].toJSON());else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),u=f(new G(t),e.spatialReference)}if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(u)}))},B.point=function(e,t){return C(e,t,((i,l,s)=>{let u;if(o(s,1,1,e,t),s[0]instanceof n){const t=f(Z(s[0],e.spatialReference,"point"),e.spatialReference);if(null==t)return null;u=t}else if(s[0]instanceof A)u=U(s[0].toJSON());else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),u=f(new A(t),e.spatialReference)}if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(u)}))},B.multipoint=function(e,t){return C(e,t,((i,l,s)=>{let u;if(o(s,1,1,e,t),s[0]instanceof n){const t=f(Z(s[0],e.spatialReference,"multipoint"),e.spatialReference);if(null==t)return null;u=t}else if(s[0]instanceof q)u=U(s[0].toJSON());else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),u=f(new q(t),e.spatialReference)}if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(u)}))},B.extent=function(e,t){return C(e,t,((i,l,s)=>{s=u(s),o(s,1,1,e,t);let m=null;if(s[0]instanceof n)m=f(Z(s[0],e.spatialReference),e.spatialReference);else if(s[0]instanceof A){const e={xmin:s[0].x,ymin:s[0].y,xmax:s[0].x,ymax:s[0].y,spatialReference:s[0].spatialReference.toJSON()},t=s[0];t.hasZ&&(e.zmin=t.z,e.zmax=t.z),t.hasM&&(e.mmin=t.m,e.mmax=t.m),m=U(e)}else if(s[0]instanceof D)m=U(s[0].extent?.toJSON());else if(s[0]instanceof G)m=U(s[0].extent?.toJSON());else if(s[0]instanceof q)m=U(s[0].extent?.toJSON());else if(s[0]instanceof z)m=U(s[0].toJSON());else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),m=f(new z(t),e.spatialReference)}if(null!==m&&!1===m.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(m)}))},B.geometry=function(e,t){return C(e,t,((i,l,s)=>{o(s,1,1,e,t);let u=null;if(null===s[0])return null;if(m(s[0]))u=f(s[0].geometry(),e.spatialReference);else if(s[0]instanceof n)u=f(Z(s[0],e.spatialReference),e.spatialReference);else{const t=JSON.parse(s[0]);t&&!t.spatialReference&&(t.spatialReference=e.spatialReference),u=f(U(t),e.spatialReference)}if(null!==u&&!1===u.spatialReference.equals(e.spatialReference))throw new r(e,a.WrongSpatialReference,t);return c(u)}))},B.setgeometry=function(e,t){return C(e,t,((n,i,l)=>{if(o(l,2,2,e,t),!m(l[0]))throw new r(e,a.InvalidParameter,t);if(!0===l[0].immutable)throw new r(e,a.Immutable,t);if(!(l[1]instanceof W||null===l[1]))throw new r(e,a.InvalidParameter,t);return l[0]._geometry=l[1],p}))},B.feature=function(e,t){return C(e,t,((l,o,s)=>{if(0===s.length)throw new r(e,a.WrongNumberOfParameters,t);let c;if(1===s.length)if(E(s[0]))c=i.fromJson(JSON.parse(s[0]),e.timeZone);else if(m(s[0]))c=i.createFromArcadeFeature(s[0]);else if(s[0]instanceof W)c=i.createFromGraphicLikeObject(s[0],null,null,e.timeZone);else{if(!(s[0]instanceof n))throw new r(e,a.InvalidParameter,t);{const n=s[0].hasField("geometry")?s[0].field("geometry"):null,l=s[0].hasField("attributes")?s[0].field("attributes"):null;let o,f;if(h(n))o=Z(n,e.spatialReference);else{if(null!=n&&!w(n))throw new r(e,a.InvalidParameter,t);o=n}if(h(l))f=i.parseAttributesFromDictionary(l);else{if(null!=l)throw new r(e,a.InvalidParameter,t);f=null}c=i.createFromGraphicLikeObject(o,f,null,e.timeZone)}}else if(2===s.length){let l=null,o=null;if(null!==s[0])if(s[0]instanceof W)l=s[0];else{if(!(s[0]instanceof n))throw new r(e,a.InvalidParameter,t);l=Z(s[0],e.spatialReference)}if(null!==s[1]){if(!(s[1]instanceof n))throw new r(e,a.InvalidParameter,t);o=i.parseAttributesFromDictionary(s[1])}c=i.createFromGraphicLikeObject(l,o,null,e.timeZone)}else{let l=null;const o={};if(null!==s[0])if(s[0]instanceof W)l=s[0];else{if(!(s[0]instanceof n))throw new r(e,a.InvalidParameter,t);l=Z(s[0],e.spatialReference)}for(let n=1;n<s.length;n+=2){const i=g(s[n]),l=s[n+1];if(!(null==l||E(l)||isNaN(l)||R(l)||_(l)||y(l)||d(l)||T(l)))throw new r(e,a.InvalidParameter,t);if(P(l)||!1===v(l))throw new r(e,a.InvalidParameter,t);o[i]=l===p?null:l}c=i.createFromGraphicLikeObject(l,o,null,e.timeZone)}return c._geometry=f(c.geometry(),e.spatialReference),c.immutable=!1,c}))},B.dictionary=function(e,t){return C(e,t,((i,l,o)=>{if(0===o.length||1===o.length&&null===o[0]){const e=new n;return e.immutable=!1,e}if(1===o.length&&E(o[0]))try{const t=JSON.parse(o[0]),r=n.convertObjectToArcadeDictionary(t,I(e),!1);return r.immutable=!1,r}catch(u){throw new r(e,a.InvalidParameter,t)}if(1===o.length&&o[0]instanceof W)try{const t=o[0].toJSON();t.hasZ=!0===o[0].hasZ,t.hasM=!0===o[0].hasM;const r=n.convertObjectToArcadeDictionary(t,I(e),!1);return r.immutable=!1,r}catch(u){throw new r(e,a.InvalidParameter,t)}if(1===o.length&&m(o[0]))try{const e=new n;e.immutable=!1,e.setField("geometry",o[0].geometry());const t=new n;t.immutable=!1,e.setField("attributes",t);for(const n of o[0].keys())t.setField(n,o[0].field(n));return e}catch(u){throw new r(e,a.InvalidParameter,t)}if(1===o.length&&(h(o[0])||b(o[0])))try{const e=new n;e.immutable=!1;for(const t of o[0].keys())e.setField(t,o[0].field(t));return e}catch(u){throw new r(e,a.InvalidParameter,t)}if(2===o.length&&o[0]instanceof n&&T(o[1]))try{if(!(!0===o[1])){const e=new n;e.immutable=!1;for(const t of o[0].keys())e.setField(t,o[0].field(t));return e}return o[0].deepClone()}catch(u){throw new r(e,a.InvalidParameter,t)}if(o.length%2!=0)throw new r(e,a.WrongNumberOfParameters,t);const f={};for(let n=0;n<o.length;n+=2){const i=g(o[n]),l=o[n+1];if(!(null==l||E(l)||isNaN(l)||R(l)||_(l)||T(l)||d(l)||y(l)||L(l)||s(l)))throw new r(e,a.InvalidParameter,t);if(P(l))throw new r(e,a.InvalidParameter,t);f[i]=l===p?null:l}const c=new n(f);return c.immutable=!1,c}))},B.haskey=function(t,i){return C(t,i,((l,s,f)=>{o(f,2,2,t,i);const c=g(f[1]);if(O(f[0])||f[0]instanceof n)return f[0].hasField(c);if(f[0]instanceof W){const t=e(f[0],c,null,null,2);return!t||"notfound"!==t.keystate}throw new r(t,a.InvalidParameter,i)}))},B.hasvalue=function(e,n){return C(e,n,((r,a,i)=>(o(i,2,2,e,n),null!=t(i[0],i[1]))))},B.indexof=function(e,t){return C(e,t,((n,i,l)=>{o(l,2,2,e,t);const f=l[1];if(L(l[0])){for(let e=0;e<l[0].length;e++)if(j(f,l[0][e]))return e;return-1}if(s(l[0])){const e=l[0].length();for(let t=0;t<e;t++)if(j(f,l[0].get(t)))return t;return-1}throw new r(e,a.InvalidParameter,t)}))},B.angle=function(e,t){return C(e,t,((n,i,l)=>{if(l=u(l),o(l,2,3,e,t),!(l[0]instanceof A))throw new r(e,a.InvalidParameter,t);if(!(l[1]instanceof A))throw new r(e,a.InvalidParameter,t);if(l.length>2&&!(l[2]instanceof A))throw new r(e,a.InvalidParameter,t);return 2===l.length?N(l[0],l[1]):x(l[0],l[1],l[2])}))},B.bearing=function(e,t){return C(e,t,((n,i,l)=>{if(l=u(l),o(l,2,3,e,t),!(l[0]instanceof A))throw new r(e,a.InvalidParameter,t);if(!(l[1]instanceof A))throw new r(e,a.InvalidParameter,t);if(l.length>2&&!(l[2]instanceof A))throw new r(e,a.InvalidParameter,t);return 2===l.length?J(l[0],l[1]):F(l[0],l[1],l[2])}))},B.isselfintersecting=function(e,t){return C(e,t,((n,r,a)=>{a=u(a),o(a,1,1,e,t);let i=a[0];if(i instanceof D)return i.isSelfIntersecting;if(i instanceof G)return k(i.paths);if(i instanceof q){const e=i.points;for(let t=0;t<e.length;t++)for(let n=0;n<e.length;n++)if(n!==t){let r=!0;for(let a=0;a<e[t].length;a++)if(e[t][a]!==e[n][a]){r=!1;break}if(!0===r)return!0}}if(L(i)||s(i)){const t=S(i,e.spatialReference);return null!==t&&(i=t.paths),k(i)}return!1}))}}export{B as registerFunctions};
