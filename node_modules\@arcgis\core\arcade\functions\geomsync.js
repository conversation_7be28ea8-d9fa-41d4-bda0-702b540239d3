/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as t}from"../executionError.js";import{cloneGeometry as r}from"../kernel.js";import{E as i,D as a,g as l,k as o,j as u,f,m as s,u as c,F as m,G as d,H as w,t as h,e as p,J as g,K as v}from"../../chunks/languageUtils.js";import{commonRelationsCheck as P,planarLength3D as x,measureToCoordinateFunc as I,pointToCoordinateFunc as y,distanceToCoordinateFunc as R}from"../geometry/functions.js";import{convertFromSpatialReferenceUnit as j,toAreaUnit as N,convert as b,toLengthUnit as E,convertToSpatialReferenceUnit as D}from"../geometry/unitConversion.js";import L from"../../geometry/Extent.js";import O from"../../geometry/Geometry.js";import k from"../../geometry/Multipoint.js";import M from"../../geometry/Point.js";import S from"../../geometry/Polygon.js";import A from"../../geometry/Polyline.js";import{fromJSON as C}from"../../geometry/support/jsonUtils.js";import{squareMeters as Z,meters as q}from"../geometry/extendedUnitData.js";import{isArray as J}from"../../support/guards.js";let U;async function z(){null==U&&(U=await import("../geometry/operators.js"),await U.loadAll())}function G(z,G){z.disjoint=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null===a[0]||null===a[1]||U.disjoint.execute(a[0],a[1]))))},z.intersects=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.intersects.execute(a[0],a[1]))))},z.touches=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.touches.execute(a[0],a[1]))))},z.crosses=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.crosses.execute(a[0],a[1]))))},z.within=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.within.execute(a[0],a[1]))))},z.contains=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.contains.execute(a[0],a[1]))))},z.overlaps=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null!==a[0]&&null!==a[1]&&U.overlaps.execute(a[0],a[1]))))},z.equals=function(e,n){return G(e,n,((t,r,i)=>(a(i,2,2,e,n),i[0]===i[1]||(i[0]instanceof O&&i[1]instanceof O?U.equals.execute(i[0],i[1]):(l(i[0])&&l(i[1])||o(i[0])&&o(i[1])||!(!u(i[0])||!u(i[1])))&&i[0].equals(i[1])))))},z.relate=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,3,3,e,r),u[0]instanceof O&&u[1]instanceof O)return U.relate.execute(u[0],u[1],f(u[2]));if(u[0]instanceof O&&null===u[1])return!1;if(u[1]instanceof O&&null===u[0])return!1;if(null===u[0]&&null===u[1])return!1;throw new n(e,t.InvalidParameter,r)}))},z.intersection=function(e,n){return G(e,n,((t,r,a)=>(a=i(a),P(a,e,n),null===a[0]||null===a[1]?null:U.intersection.execute(a[0],a[1]))))},z.union=function(e,a){return G(e,a,((l,o,u)=>{if(0===(u=i(u)).length)throw new n(e,t.WrongNumberOfParameters,a);const f=[];if(1===u.length)if(J(u[0])){for(const r of i(u[0]))if(null!==r){if(!(r instanceof O))throw new n(e,t.InvalidParameter,a);f.push(r)}}else{if(!s(u[0])){if(u[0]instanceof O)return c(r(u[0]),e.spatialReference);if(null===u[0])return null;throw new n(e,t.InvalidParameter,a)}for(const r of i(u[0].toArray()))if(null!==r){if(!(r instanceof O))throw new n(e,t.InvalidParameter,a);f.push(r)}}else for(const r of u)if(null!==r){if(!(r instanceof O))throw new n(e,t.InvalidParameter,a);f.push(r)}return 0===f.length?null:U.union.executeMany(f)}))},z.difference=function(e,n){return G(e,n,((t,a,l)=>(l=i(l),P(l,e,n),null===l[0]?null:null===l[1]?r(l[0]):U.difference.execute(l[0],l[1]))))},z.symmetricdifference=function(e,n){return G(e,n,((t,a,l)=>(l=i(l),P(l,e,n),null===l[0]&&null===l[1]?null:null===l[0]?r(l[1]):null===l[1]?r(l[0]):U.symmetricDifference.execute(l[0],l[1]))))},z.clip=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,2,e,r),!(u[1]instanceof L)&&null!==u[1])throw new n(e,t.InvalidParameter,r);if(null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);return null===u[1]?null:U.clip.execute(u[0],u[1])}))},z.cut=function(e,l){return G(e,l,((o,u,f)=>{if(f=i(f),a(f,2,2,e,l),!(f[1]instanceof A)&&null!==f[1])throw new n(e,t.InvalidParameter,l);if(null===f[0])return[];if(!(f[0]instanceof O))throw new n(e,t.InvalidParameter,l);return null===f[1]?[r(f[0])]:U.cut.execute(f[0],f[1])}))},z.area=function(e,r){return G(e,r,((l,o,u)=>{a(u,1,2,e,r);let f=(u=i(u))[0];if((J(u[0])||s(u[0]))&&(f=m(u[0],e.spatialReference)),null===f)return 0;if(!(f instanceof O))throw new n(e,t.InvalidParameter,r);return j(f.spatialReference,N(u[1]),U.area.execute(f))}))},z.areageodetic=function(e,r){return G(e,r,((l,o,u)=>{a(u,1,2,e,r);let f=(u=i(u))[0];if((J(u[0])||s(u[0]))&&(f=m(u[0],e.spatialReference)),null===f)return 0;if(!(f instanceof O))throw new n(e,t.InvalidParameter,r);return b(Z,N(u[1]),U.geodeticArea.execute(f))}))},z.length=function(e,r){return G(e,r,((l,o,u)=>{a(u,1,2,e,r);let f=(u=i(u))[0];if((J(u[0])||s(u[0]))&&(f=d(u[0],e.spatialReference)),null===f)return 0;if(!(f instanceof O))throw new n(e,t.InvalidParameter,r);return j(f.spatialReference,E(u[1]),U.length.execute(f))}))},z.length3d=function(e,r){return G(e,r,((l,o,u)=>{a(u,1,2,e,r);let f=(u=i(u))[0];if((J(u[0])||s(u[0]))&&(f=d(u[0],e.spatialReference)),null===f)return 0;if(!(f instanceof O))throw new n(e,t.InvalidParameter,r);return!0===f.hasZ?j(f.spatialReference,E(u[1]),x(f)):j(f.spatialReference,E(u[1]),U.length.execute(f))}))},z.lengthgeodetic=function(e,r){return G(e,r,((l,o,u)=>{a(u,1,2,e,r);let f=(u=i(u))[0];if((J(u[0])||s(u[0]))&&(f=d(u[0],e.spatialReference)),null===f)return 0;if(!(f instanceof O))throw new n(e,t.InvalidParameter,r);return b(q,E(u[1]),U.geodeticLength.execute(f))}))},z.distance=function(e,r){return G(e,r,((l,o,u)=>{u=i(u),a(u,2,3,e,r);let f=u[0];if((J(u[0])||s(u[0]))&&(f=w(u[0],e.spatialReference)),!(f instanceof O))throw new n(e,t.InvalidParameter,r);let c=u[1];if((J(u[1])||s(u[1]))&&(c=w(u[1],e.spatialReference)),!(c instanceof O))throw new n(e,t.InvalidParameter,r);return j(f.spatialReference,E(u[2]),U.distance.execute(f,c))}))},z.distancegeodetic=function(e,r){return G(e,r,((l,o,u)=>{u=i(u),a(u,2,3,e,r);const f=u[0];if(!(f instanceof M))throw new n(e,t.InvalidParameter,r);const s=u[1];if(!(s instanceof M))throw new n(e,t.InvalidParameter,r);const c=new A({paths:[],spatialReference:f.spatialReference});return c.addPath([f,s]),b(q,E(u[2]),U.geodeticLength.execute(c))}))},z.densify=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,3,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);const f=h(u[1]);if(isNaN(f))throw new n(e,t.InvalidParameter,r);if(f<=0)throw new n(e,t.InvalidParameter,r);const s=D(E(u[2]),u[0].spatialReference,f);switch(u[0].type){case"polygon":case"polyline":case"extent":return U.densify.execute(u[0],s);default:return u[0]}}))},z.densifygeodetic=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,3,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);const f=h(u[1]);if(isNaN(f))throw new n(e,t.InvalidParameter,r);if(f<=0)throw new n(e,t.InvalidParameter,r);const s=b(E(u[2]),q,f);switch(u[0].type){case"polygon":case"polyline":case"extent":return U.geodeticDensify.execute(u[0],s);default:return u[0]}}))},z.generalize=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,4,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);const f=h(u[1]);if(isNaN(f))throw new n(e,t.InvalidParameter,r);const s=D(E(u[3]),u[0].spatialReference,f);return U.generalize.execute(u[0],s,{removeDegenerateParts:p(g(u[2],!0))})}))},z.buffer=function(e,l){return G(e,l,((o,u,f)=>{if(f=i(f),a(f,2,3,e,l),null===f[0])return null;if(!(f[0]instanceof O))throw new n(e,t.InvalidParameter,l);const s=h(f[1]);if(isNaN(s))throw new n(e,t.InvalidParameter,l);return 0===s?r(f[0]):U.buffer.execute(f[0],D(E(f[2]),f[0].spatialReference,s))}))},z.buffergeodetic=function(e,l){return G(e,l,((o,u,f)=>{if(f=i(f),a(f,2,3,e,l),null===f[0])return null;if(!(f[0]instanceof O))throw new n(e,t.InvalidParameter,l);const s=h(f[1]);if(isNaN(s))throw new n(e,t.InvalidParameter,l);return 0===s?r(f[0]):U.geodesicBuffer.execute(f[0],b(E(f[2]),q,s))}))},z.offset=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,6,e,r),null===u[0])return null;if(!(u[0]instanceof S||u[0]instanceof A))throw new n(e,t.InvalidParameter,r);const s=h(u[1]);if(isNaN(s))throw new n(e,t.InvalidParameter,r);const c=D(E(u[2]),u[0].spatialReference,s),m=f(u[3]??"round").toLowerCase();let d;switch(m){case"round":case"bevel":case"miter":case"square":d=m;break;default:d="round"}const w=h(g(u[4],10));if(isNaN(w))throw new n(e,t.InvalidParameter,r);const p=h(g(u[5],0));if(isNaN(p))throw new n(e,t.InvalidParameter,r);return U.offset.execute(u[0],c,{joins:d,miterLimit:w,flattenError:p})}))},z.rotate=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,2,3,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);const f=u[0]instanceof L?S.fromExtent(u[0]):u[0],s=h(u[1]);if(isNaN(s))throw new n(e,t.InvalidParameter,r);const c=g(u[2],null);if(null===c){const e="point"===f.type?f:f.extent?.center;return U.rotate(f,s,e?.x,e?.y)}if(c instanceof M)return U.rotate(f,s,c.x,c.y);throw new n(e,t.InvalidParameter,r)}))},z.centroid=function(e,r){return G(e,r,((e,r,l)=>{if(l=i(l),a(l,1,2,e,r),null===l[0])return null;const o=f(l[1]??"geometric").toLowerCase();if("geometric"!==o&&"labelpoint"!==o)throw new n(e,t.InvalidParameter,r);let u=l[0];if((J(l[0])||s(l[0]))&&(u="geometric"===o?w(l[0],e.spatialReference):m(l[0],e.spatialReference),null===u))return null;if(!(u instanceof O))throw new n(e,t.InvalidParameter,r);return"geometric"===o?U.centroid.execute(u):U.labelPoint.execute(u)}))},z.measuretocoordinate=function(e,n){return G(e,n,I)},z.pointtocoordinate=function(e,n){return G(e,n,y)},z.distancetocoordinate=function(e,n){return G(e,n,R)},z.multiparttosinglepart=function(e,l){return G(e,l,((o,u,f)=>{if(f=i(f),a(f,1,1,e,l),null===f[0])return null;if(!(f[0]instanceof O))throw new n(e,t.InvalidParameter,l);if(f[0]instanceof M)return[c(r(f[0]),e.spatialReference)];if(f[0]instanceof L)return[c(r(f[0]),e.spatialReference)];const s=U.simplify.execute(f[0]);if(s instanceof S){const e=[],n=[];for(let t=0;t<s.rings.length;t++)if(s.isClockwise(s.rings[t])){const n=C({rings:[s.rings[t]],hasZ:!0===s.hasZ,hasM:!0===s.hasM,spatialReference:s.spatialReference.toJSON()});e.push(n)}else n.push({ring:s.rings[t],pt:s.getPoint(t,0)});for(let t=0;t<n.length;t++)for(let r=0;r<e.length;r++)if(e[r].contains(n[t].pt)){e[r].addRing(n[t].ring);break}return e}if(s instanceof A){const e=[];for(let n=0;n<s.paths.length;n++){const t=C({paths:[s.paths[n]],hasZ:!0===s.hasZ,hasM:!0===s.hasM,spatialReference:s.spatialReference.toJSON()});e.push(t)}return e}if(f[0]instanceof k){const n=[],t=c(r(f[0]),e.spatialReference);for(let e=0;e<t.points.length;e++)n.push(t.getPoint(e));return n}return null}))},z.issimple=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,1,1,e,r),null===u[0])return!0;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);return U.simplify.isSimple(u[0])}))},z.simplify=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,1,1,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);return U.simplify.execute(u[0])}))},z.convexhull=function(e,r){return G(e,r,((l,o,u)=>{if(u=i(u),a(u,1,1,e,r),null===u[0])return null;if(!(u[0]instanceof O))throw new n(e,t.InvalidParameter,r);return U.convexHull.execute(u[0])}))},z.nearestcoordinate=function(r,l){return G(r,l,((o,u,f)=>{if(f=i(f),a(f,2,2,r,l),!(f[0]instanceof O||null===f[0]))throw new n(r,t.InvalidParameter,l);if(!(f[1]instanceof M||null===f[1]))throw new n(r,t.InvalidParameter,l);if(null===f[0]||null===f[1])return null;const s=f[0]instanceof L?S.fromExtent(f[0]):f[0],c=U.proximity.getNearestCoordinate(s,f[1],{calculateLeftRightSide:!0});return null===c||c.isEmpty?null:e.convertObjectToArcadeDictionary({coordinate:c.coordinate,distance:c.distance,sideOfLine:0===c.distance?"straddle":c.rightSide?"right":"left"},v(r),!1,!0)}))},z.nearestvertex=function(r,l){return G(r,l,((o,u,f)=>{if(f=i(f),a(f,2,2,r,l),!(f[0]instanceof O||null===f[0]))throw new n(r,t.InvalidParameter,l);if(!(f[1]instanceof M||null===f[1]))throw new n(r,t.InvalidParameter,l);if(null===f[0]||null===f[1])return null;const s=f[0]instanceof L?S.fromExtent(f[0]):f[0],c=U.proximity.getNearestVertex(s,f[1]);return null===c||c.isEmpty?null:e.convertObjectToArcadeDictionary({coordinate:c.coordinate,distance:c.distance,sideOfLine:0===c.distance?"straddle":c.rightSide?"right":"left"},v(r),!1,!0)}))}}export{z as loadOperators,G as registerFunctions};
