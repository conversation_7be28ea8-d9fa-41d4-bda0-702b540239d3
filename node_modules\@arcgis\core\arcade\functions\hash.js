/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
const t=2654435761,s=2246822519,n=3266489917,e=668265263,r=374761393;function h(t){const s=[];for(let n=0,e=t.length;n<e;n++){let e=t.charCodeAt(n);e<128?s.push(e):e<2048?s.push(192|e>>6,128|63&e):e<55296||e>=57344?s.push(224|e>>12,128|e>>6&63,128|63&e):(n++,e=65536+((1023&e)<<10|1023&t.charCodeAt(n)),s.push(240|e>>18,128|e>>12&63,128|e>>6&63,128|63&e))}return new Uint8Array(s)}class i{constructor(t){this._seed=t,this._totallen=0,this._bufs=[],this.init()}init(){return this._bufs=[],this._totallen=0,this}updateFloatArray(t){const s=[];for(const n of t)isNaN(n)?s.push("NaN"):n===1/0?s.push("Infinity"):n===-1/0?s.push("-Infinity"):0===n?s.push("0"):s.push(n.toString(16));this.update(h(s.join("")))}updateIntArray(t){const s=Int32Array.from(t);this.update(new Uint8Array(s.buffer))}updateUint8Array(t){this.update(Uint8Array.from(t))}updateWithString(t){return this.update(h(t))}update(t){return this._bufs.push(t),this._totallen+=t.length,this}digest(){const t=new Uint8Array(this._totallen);let s=0;for(const n of this._bufs)t.set(n,s),s+=n.length;return this.init(),this._xxHash32(t,this._seed)}_xxHash32(h,i=0){const o=h;let u=i+r&4294967295,a=0;if(o.length>=16){const n=[i+t+s&4294967295,i+s&4294967295,i+0&4294967295,i-t&4294967295],e=h,r=e.length-16;let o=0;for(a=0;(4294967280&a)<=r;a+=4){const r=a,h=e[r]+(e[r+1]<<8),i=e[r+2]+(e[r+3]<<8),u=h*s+(i*s<<16);let l=n[o]+u&4294967295;l=l<<13|l>>>19;const f=65535&l,p=l>>>16;n[o]=f*t+(p*t<<16)&4294967295,o=o+1&3}u=(n[0]<<1|n[0]>>>31)+(n[1]<<7|n[1]>>>25)+(n[2]<<12|n[2]>>>20)+(n[3]<<18|n[3]>>>14)&4294967295}u=u+h.length&4294967295;const l=h.length-4;for(;a<=l;a+=4){const t=a,s=o[t]+(o[t+1]<<8),r=o[t+2]+(o[t+3]<<8);u=u+(s*n+(r*n<<16))&4294967295,u=u<<17|u>>>15,u=(65535&u)*e+((u>>>16)*e<<16)&4294967295}for(;a<o.length;++a){u+=o[a]*r,u=u<<11|u>>>21,u=(65535&u)*t+((u>>>16)*t<<16)&4294967295}return u^=u>>>15,u=((65535&u)*s&4294967295)+((u>>>16)*s<<16),u^=u>>>13,u=((65535&u)*n&4294967295)+((u>>>16)*n<<16),u^=u>>>16,u<0?u+4294967296:u}}export{i as XXH};
