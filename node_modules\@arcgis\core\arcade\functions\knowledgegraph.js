/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../../config.js";import r from"../ArcadePortal.js";import t from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as o}from"../executionError.js";import{D as a,f as i,r as s,J as p,K as l,g as c,j as f,k as u,q as m,l as d}from"../../chunks/languageUtils.js";import{getPortal as g}from"../portalUtils.js";import h from"../../geometry/Geometry.js";import{isLoaded as w,load as y,project as j}from"../../geometry/projection.js";import S from"../../geometry/SpatialReference.js";import{webMercatorToGeographic as R,geographicToWebMercator as v}from"../../geometry/support/webMercatorUtils.js";import G from"../../portal/Portal.js";import k from"../../portal/PortalItem.js";import{project as x}from"../../rest/geometryService/project.js";import P from"../../rest/knowledgeGraph/Entity.js";import b from"../../rest/knowledgeGraph/GraphQueryStreaming.js";import I from"../../rest/knowledgeGraph/ObjectValue.js";import D from"../../rest/knowledgeGraph/Path.js";import W from"../../rest/knowledgeGraph/Relationship.js";import T from"../../rest/support/ProjectParameters.js";import{isString as q,isArray as A,isNumber as J,isDate as N}from"../../support/guards.js";let U=null;async function F(r){const t=e.geometryServiceUrl??"";if(!t){w()||await y();for(const e of r)e.container[e.indexer]=j(e.container[e.indexer],S.WGS84);return}const n=r.map((e=>e.container[e.indexer])),o=new T({geometries:n,outSpatialReference:S.WGS84}),a=await x(t,o);for(let e=0;e<a.length;e++){const t=r[e];t.container[t.indexer]=a[e]}}async function M(e,r){const t=new k({portal:e,id:r});return await t.load(),null===U&&(U=await import("../../rest/knowledgeGraphService.js")),await U.fetchKnowledgeGraph(t.url)}function Q(e,r,t,n,o){if(null===e)return null;if(q(e)||J(e))return e;if(c(e))return e.toJSDate();if(c(e))return e.toJSDate();if(f(e))return e.toStorageFormat();if(u(e))return e.toStorageString();if(m(e)){const a={};for(const i of e.keys())a[i]=Q(e.field(i),r,t,n,o),a[i]instanceof h&&o.push({container:a,indexer:i});return a}if(A(e)){const a=e.map((e=>Q(e,r,t,n,o)));for(let e=0;e<a.length;e++)a[e]instanceof h&&o.push({container:a,indexer:e});return a}return d(e)?e.spatialReference.isWGS84?e:e.spatialReference.isWebMercator&&r?R(e):e:void 0}function E(e,r){if(!e)return e;if(e.spatialReference.isWGS84&&r.spatialReference.isWebMercator)return v(e);if(e.spatialReference.equals(r.spatialReference))return e;throw new n(r,o.WrongSpatialReference,null)}function K(e,r){if(!e)return null;const t={};for(const n in e)t[n]=V(e[n],r);return t}function V(e,r){return null===e?null:A(e)?e.map((e=>V(e,r))):e instanceof P?{graphTypeName:e.typeName,id:e.id,graphType:"entity",properties:K(e.properties,r)}:e instanceof I?{graphType:"object",properties:K(e.properties,r)}:e instanceof W?{graphTypeName:e.typeName,id:e.id,graphType:"relationship",originId:e.originId??null,destinationId:e.destinationId??null,properties:K(e.properties,r)}:e instanceof D?{graphType:"path",path:e.path?e.path.map((e=>V(e,r))):null}:d(e)?E(e,r):q(e)||J(e)||N(e)?e:null}function C(e){"async"===e.mode&&(e.functions.knowledgegraphbyportalitem=function(t,s){return e.standardFunctionAsync(t,s,((e,p,l)=>{if(a(l,2,2,t,s),null===l[0])throw new n(t,o.PortalRequired,s);if(l[0]instanceof r){const e=i(l[1]);let r;r=t.services?.portal?t.services.portal:G.getDefault();return M(g(l[0],r),e)}if(!1===q(l[0]))throw new n(t,o.InvalidParameter,s);const c=i(l[0]);return M(t.services?.portal??G.getDefault(),c)}))},e.signatures.push({name:"knowledgegraphbyportalitem",min:2,max:2}),e.functions.querygraph=function(r,i){return e.standardFunctionAsync(r,i,(async(e,c,f)=>{a(f,2,4,r,i);const u=f[0];if(!s(u))throw new n(r,o.InvalidParameter,i);const m=f[1];if(!q(m))throw new n(r,o.InvalidParameter,i);null===U&&(U=await import("../../rest/knowledgeGraphService.js"));let d=null;const g=p(f[2],null);if(!(g instanceof t||null===g))throw new n(r,o.InvalidParameter,i);if(g){let e=[];d=Q(g,!0,!1,r,e),e=e.filter((e=>!e.container[e.indexer].spatialReference.isWGS84)),e.length>0&&await F(e)}const h=new b({openCypherQuery:m,bindParameters:d});(u?.serviceDefinition?.currentVersion??11.3)>11.2&&(h.outputSpatialReference=r.spatialReference);const w=(await U.executeQueryStreaming(u,h)).resultRowsStream.getReader(),y=[];try{for(;;){const{done:e,value:t}=await w.read();if(e)break;if(A(t))for(const n of t)y.push(V(n,r));else{const e=[];for(const n of t)e.push(V(t[n],r));y.push(e)}}}catch(j){throw j}return t.convertJsonToArcade(y,l(r),!1,!0)}))},e.signatures.push({name:"querygraph",min:2,max:4}))}export{C as registerFunctions};
