/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{D as n,g as t,k as r,j as u,W as e,t as o,w as i,e as a,m as s}from"../../chunks/languageUtils.js";import{decimalAdjust as f}from"../../core/mathUtils.js";import{parse as c}from"../../core/number.js";import{isNumber as l,isBoolean as N,isArray as h,isString as m}from"../../support/guards.js";function p(p,M){function b(n,t,r){const u=o(n);return isNaN(u)?u:isNaN(t)||isNaN(r)||t>r?NaN:u<t?t:u>r?r:u}p.number=function(o,i){return M(o,i,((a,s,f)=>{n(f,1,2,o,i);const p=f[0];if(l(p))return p;if(null===p)return 0;if(t(p)||r(p)||u(p))return p.toNumber();if(N(p))return Number(p);if(h(p))return NaN;if(""===p)return Number(p);if(void 0===p)return Number(p);if(m(p)){if(void 0!==f[1]){let n=e(f[1],"‰","");return n=e(n,"¤",""),c(p,{pattern:n})}return Number(p.trim())}return Number(p)}))},p.abs=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.abs(o(i[0])))))},p.acos=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.acos(o(i[0])))))},p.asin=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.asin(o(i[0])))))},p.atan=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.atan(o(i[0])))))},p.atan2=function(t,r){return M(t,r,((u,e,i)=>(n(i,2,2,t,r),Math.atan2(o(i[0]),o(i[1])))))},p.ceil=function(t,r){return M(t,r,((u,e,i)=>{if(n(i,1,2,t,r),2===i.length){let n=o(i[1]);return isNaN(n)&&(n=0),f("ceil",o(i[0]),-1*n)}return Math.ceil(o(i[0]))}))},p.round=function(t,r){return M(t,r,((u,e,i)=>{if(n(i,1,2,t,r),2===i.length){let n=o(i[1]);return isNaN(n)&&(n=0),f("round",o(i[0]),-1*n)}return Math.round(o(i[0]))}))},p.floor=function(t,r){return M(t,r,((u,e,i)=>{if(n(i,1,2,t,r),2===i.length){let n=o(i[1]);return isNaN(n)&&(n=0),f("floor",o(i[0]),-1*n)}return Math.floor(o(i[0]))}))},p.cos=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.cos(o(i[0])))))},p.isnan=function(t,r){return M(t,r,((u,e,o)=>(n(o,1,1,t,r),"number"==typeof o[0]&&isNaN(o[0]))))},p.exp=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.exp(o(i[0])))))},p.log=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.log(o(i[0])))))},p.pow=function(t,r){return M(t,r,((u,e,i)=>(n(i,2,2,t,r),o(i[0])**o(i[1]))))},p.random=function(t,r){return M(t,r,((u,e,o)=>(n(o,0,0,t,r),Math.random())))},p.sin=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.sin(o(i[0])))))},p.sqrt=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.sqrt(o(i[0])))))},p.tan=function(t,r){return M(t,r,((u,e,i)=>(n(i,1,1,t,r),Math.tan(o(i[0])))))},p.isempty=function(t,r){return M(t,r,((u,e,o)=>(n(o,1,1,t,r),null===o[0]||(""===o[0]||(void 0===o[0]||o[0]===i)))))},p.boolean=function(t,r){return M(t,r,((u,e,o)=>{n(o,1,1,t,r);const i=o[0];return a(i)}))},p.constrain=function(t,r){return M(t,r,((u,e,i)=>{n(i,3,3,t,r);const a=o(i[1]),f=o(i[2]);if(h(i[0])){const n=[];for(const t of i[0])n.push(b(t,a,f));return n}if(s(i[0])){const n=[];for(let t=0;t<i[0].length();t++)n.push(b(i[0].get(t),a,f));return n}return b(i[0],a,f)}))}}export{p as registerFunctions};
