/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{getMetersPerVerticalUnitForSR as e,segmentLength3d as t,segmentLength as n,segmentLength3dSqr as s,segmentLengthSqr as a,closestPointOnLineSegmentWithZ as r,closestPointOnLineSegment as l}from"./centroid.js";import{getMetersPerUnitForSR as i}from"../../core/unitUtils.js";import c from"../../geometry/Point.js";import o from"../../geometry/Polygon.js";function f(e,t,n){const s=t[0]-e[0],a=t[1]-e[1];return Math.sqrt(s*s+a*a)}function u(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1]+e[2]*e[2])}function h(e){const t=u(e);return[e[0]/t,e[1]/t,e[2]/t]}function p(e,t,n,s){const a=h([t[0]-e[0],t[1]-e[1],t[2]*s-e[2]*s]);return[e[0]+a[0]*n,e[1]+a[1]*n,e[2]+a[2]*n]}function m(e,t,n,s){return e+(t-e)/n*s}function d(e,t,n){let s=t[0]-e[0],a=t[1]-e[1];const r=Math.sqrt(s*s+a*a);return s/=r,a/=r,s*=n,a*=n,[e[0]+s,e[1]+a]}function y(n,s){if(!n)return null;switch(n.type){case"extent":case"multipoint":case"mesh":case"point":return null}const a="polygon"===n.type?n.rings:n.paths;let r=1;if(n.spatialReference.vcsWkid||n.spatialReference.latestVcsWkid){r=e(n.spatialReference)/i(n.spatialReference)}if(0===a.length)return null;if(0===a[0].length)return null;if(!1===n.hasM)return null;let l=-1,o=0;const u=n.hasZ?t:f,h=n.hasZ?3:2,m=2;for(const e of a){if(l++,e.length>0&&e[0][h]===s)return{partId:l,distanceAlong:o,coordinate:new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:e[0][0],y:e[0][1],...n.hasZ?{z:e[0][m]}:{},...n.hasM?{m:e[0][h]}:{}}),segmentId:0};let t=-1;for(let a=1;a<e.length;a++){const i=u(e[a-1],e[a],r);t++;const f=e[a][h]-e[a-1][h],y=e[a][h];if(y===s)return{partId:l,distanceAlong:i+o,coordinate:new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:e[a][0],y:e[a][1],...n.hasZ?{z:e[a][m]}:{},...n.hasM?{m:e[a][h]}:{}}),segmentId:t};if(y>s&&s>e[a-1][h]){const y=(s-e[a-1][h])/f*i;let Z=n.hasZ?p(e[a-1],e[a],y,r):d(e[a-1],e[a],y);Z=[...Z,s];const R=new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:Z[0],y:Z[1],...n.hasZ?{z:Z[m]}:{},...n.hasM?{m:Z[h]}:{}});return{partId:l,distanceAlong:o+u(e[a-1],[R.x,R.y,...R.hasZ?[R.z]:[],...R.hasM?[R.m]:[]],r),coordinate:R,segmentId:t}}o+=i}}return null}function Z(n,s){if(!n)return null;switch(n.type){case"extent":case"multipoint":case"mesh":case"point":return null}const a="polygon"===n.type?n.rings:n.paths;if(s<0)return null;let r=1;if(n.spatialReference.vcsWkid||n.spatialReference.latestVcsWkid){r=e(n.spatialReference)/i(n.spatialReference)}let l=0;const o=n.hasZ?3:2,u=2,h=n.hasZ?t:f;let y=-1;if(0===s)return 0===a.length||0===a[0].length?null:{partId:0,coordinate:new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:a[0][0][0],y:a[0][0][1],...n.hasZ?{z:a[0][0][u]}:{},...n.hasM?{m:a[0][0][o]}:{}}),segmentId:0};for(const e of a){y++;let t=-1;for(let a=1;a<e.length;a++){t++;const i=h(e[a-1],e[a],r),f=l+i;if(f===s)return{partId:y,coordinate:new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:e[a][0],y:e[a][1],...n.hasZ?{z:e[a][u]}:{},...n.hasM?{m:e[a][o]}:{}}),segmentId:t};if(f>s){let f=n.hasZ?p(e[a-1],e[a],s-l,r):d(e[a-1],e[a],s-l);return f=[...f,m(e[a-1][o],e[a][o],i,s-l)],{partId:y,coordinate:new c({hasZ:n.hasZ,hasM:n.hasM,spatialReference:n.spatialReference,x:f[0],y:f[1],...n.hasZ?{z:f[u]}:{},...n.hasM?{m:f[o]}:{}}),segmentId:t}}l=f}}return null}function R(s,a){if(!s)return null;if(!a)return null;let r=1;if(a.spatialReference.vcsWkid||a.spatialReference.latestVcsWkid){r=e(a.spatialReference)/i(a.spatialReference)}let l=null,c=0;return l=s,c=s.hasZ&&a.hasZ?t([a.x,a.y,a.z],[s.x,s.y,s.z],r):n([a.x,a.y],[s.x,s.y],!1),{coordinate:l,distance:c}}function g(t,n){if(!t)return null;if(!n)return null;let r=1;if(n.spatialReference.vcsWkid||n.spatialReference.latestVcsWkid){r=e(n.spatialReference)/i(n.spatialReference)}let l=null,c=0,o=Number.MAX_VALUE,f=-1,u=-1;for(const e of t.points||[]){u++;const l=t.hasZ&&n.hasZ?s([e[0],e[1],e[2]],[n.x,n.y,n.z],r):a([e[0],e[1]],[n.x,n.y],!1);l<o&&(o=l,f=u)}return f<0?null:(c=o,l=t.getPoint(f),{coordinate:l,distance:Math.sqrt(c)})}function M(o,f){if(!o)return null;if(!f)return null;const u="polygon"===o.type?o.rings:o.paths;let h=1;if(f.spatialReference.vcsWkid||f.spatialReference.latestVcsWkid){h=e(f.spatialReference)/i(f.spatialReference)}let p=Number.MAX_VALUE,d=-1,y=-1,Z=-1;const R=o.hasZ&&f.hasZ;let g=null;const M=R?[f.x,f.y,f.z]:[f.x,f.y];for(const e of u){y++;for(let t=1;t<e.length;t++){const n=R?r(M,e[t-1],e[t]):l(M,e[t-1],e[t]),i=R?s(n,M,h):a(n,M,!1);i<p&&(p=i,g=n,Z=y,d=t-1)}}if(d<0)return null;const x=o.hasM&&o.hasZ?3:2,I=2,z=u[Z][d],w=u[Z][d+1];let k=null,W=null,A=R?g[2]:null;o.hasM&&(W=m(z[x],w[x],R?t(z,w,h):n(z,w,!1),R?t(z,g,h):n(z,g,!1))),o.hasZ&&!1===f.hasZ&&(A=m(z[I],w[I],R?t(z,w,h):n(z,w,!1),R?t(z,g,h):n(z,g,!1))),k=new c({hasZ:R,hasM:o.hasM,spatialReference:f.spatialReference,x:g[0],y:g[1],...o.hasZ?{z:A}:{},...o.hasM?{m:W}:{}});let V=0;for(let e=0;e<=Z;e++){const s=u[e],a=e===Z?d:s.length-1;for(let e=1;e<=a;e++)V+=o.hasZ?t(s[e-1],s[e],h):n([s[e-1][0],s[e-1][1]],[s[e][0],s[e][1]],!1)}return V+=o.hasZ?t(z,[k.x,k.y,k.z],h):n(z,[k.x,k.y],!1),{partId:Z,segmentId:d,coordinate:k,distance:Math.sqrt(p),distanceAlong:V}}function x(e,t){if(!e)return null;if(!t)return null;if("extent"===e.type){const t=e;e=new o({spatialReference:e.spatialReference,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]})}switch(e.type){case"point":return R(e,t)??null;case"multipoint":return g(e,t)??null;case"polygon":case"polyline":return M(e,t)??null;default:return null}}export{Z as distanceToCoordinate,y as measureToCoordinate,x as pointToCoordinate};
