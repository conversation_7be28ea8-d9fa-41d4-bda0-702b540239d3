/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeExecutionError as n,ExecutionErrorCodes as r}from"../executionError.js";import{D as t,m as e}from"../../chunks/languageUtils.js";import{calculateStat as u}from"./fieldStats.js";import{isArray as i,isString as o}from"../../support/guards.js";function a(n,r){if(1===r.length){if(i(r[0]))return u(n,r[0],-1);if(e(r[0]))return u(n,r[0].toArray(),-1)}return u(n,r,-1)}function f(u,f){u.stdev=function(n,r){return f(n,r,((n,r,t)=>a("stdev",t)))},u.variance=function(n,r){return f(n,r,((n,r,t)=>a("variance",t)))},u.average=function(n,r){return f(n,r,((n,r,t)=>a("mean",t)))},u.mean=function(n,r){return f(n,r,((n,r,t)=>a("mean",t)))},u.sum=function(n,r){return f(n,r,((n,r,t)=>a("sum",t)))},u.min=function(n,r){return f(n,r,((n,r,t)=>a("min",t)))},u.max=function(n,r){return f(n,r,((n,r,t)=>a("max",t)))},u.distinct=function(n,r){return f(n,r,((n,r,t)=>a("distinct",t)))},u.count=function(u,a){return f(u,a,((f,m,c)=>{if(t(c,1,1,u,a),i(c[0])||o(c[0]))return c[0].length;if(e(c[0]))return c[0].length();throw new n(u,r.InvalidParameter,a)}))}}export{f as registerFunctions};
