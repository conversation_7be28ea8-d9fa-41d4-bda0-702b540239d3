/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import t from"../ArcadePortal.js";import e from"../Attachment.js";import r from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as a}from"../executionError.js";import{D as o,f as i,L as u,t as s,w as l,J as p,e as c,s as f,m as d,W as h,K as y,X as g,g as m,j as A,k as w,n as I,i as U,p as v,o as j}from"../../chunks/languageUtils.js";import{convertDirection as x}from"./convertdirection.js";import{XXH as N}from"./hash.js";import{hasSamePortal as C}from"../../core/urlUtils.js";import{generateUUID as k}from"../../core/uuid.js";import P from"../../geometry/Extent.js";import L from"../../geometry/Multipoint.js";import z from"../../geometry/Point.js";import O from"../../geometry/Polygon.js";import S from"../../geometry/Polyline.js";import F from"../../geometry/SpatialReference.js";import T from"../../portal/Portal.js";import{isArray as R,isString as b,isBoolean as M,isNumber as D}from"../../support/guards.js";function H(t){if("loaded"===t.loadStatus&&t.user?.sourceJSON){return t.user.sourceJSON}return null}function J(t,e){return!!t&&C(t,e?.restUrl||"")}function W(t,e){if(!t||!e)return t===e;if(t.x===e.x&&t.y===e.y){if(t.hasZ){if(t.z!==e.z)return!1}else if(e.hasZ)return!1;if(t.hasM){if(t.m!==e.m)return!1}else if(e.hasM)return!1;return!0}return!1}function B(o,i,u){if(null!==o)if(R(o)){if(i.updateUint8Array([61]),u.map.has(o)){const t=u.map.get(o);i.updateIntArray([61237541^t])}else{u.map.set(o,u.currentLength++);for(const t of o)B(t,i,u);u.map.delete(o),u.currentLength--}i.updateUint8Array([199])}else if(d(o)){if(i.updateUint8Array([61]),u.map.has(o)){const t=u.map.get(o);i.updateIntArray([61237541^t])}else{u.map.set(o,u.currentLength++);for(const t of o.toArray())B(t,i,u);u.map.delete(o),u.currentLength--}i.updateUint8Array([199])}else{if(m(o))return i.updateIntArray([o.toNumber()]),void i.updateUint8Array([241]);if(A(o))return i.updateIntArray([o.toNumber()]),void i.updateIntArray([257]);if(w(o))return i.updateIntArray([o.toNumber()]),void i.updateIntArray([263]);if(b(o))return i.updateIntArray([o.length]),i.updateWithString(o),void i.updateUint8Array([41]);if(M(o))i.updateUint8Array([!0===o?1:0,113]);else{if(D(o))return i.updateFloatArray([o]),void i.updateUint8Array([173]);if(o instanceof e)throw new n(u.context,a.UnsupportedHashType,u.node);if(o instanceof t)throw new n(u.context,a.UnsupportedHashType,u.node);if(!(o instanceof r)){if(I(o))throw new n(u.context,a.UnsupportedHashType,u.node);if(o instanceof z)return i.updateIntArray([3833836621]),i.updateIntArray([0]),i.updateFloatArray([o.x]),i.updateIntArray([1]),i.updateFloatArray([o.y]),o.hasZ&&(i.updateIntArray([2]),i.updateFloatArray([o.z])),o.hasM&&(i.updateIntArray([3]),i.updateFloatArray([o.m])),i.updateIntArray([3765347959]),void B(o.spatialReference.wkid,i,u);if(o instanceof O){i.updateIntArray([1266616829]);for(let t=0;t<o.rings.length;t++){const e=o.rings[t],r=[];let n=null,a=null;for(let i=0;i<e.length;i++){const u=o.getPoint(t,i);if(0===i)n=u;else if(W(a,u))continue;a=u,i===e.length-1&&W(n,u)||r.push(u)}i.updateIntArray([1397116793,r.length]);for(let t=0;t<r.length;t++){const e=r[t];i.updateIntArray([3962308117,t]),B(e,i,u),i.updateIntArray([2716288009])}i.updateIntArray([2278822459])}return i.updateIntArray([3878477243]),void B(o.spatialReference.wkid,i,u)}if(o instanceof S){i.updateIntArray([4106883559]);for(let t=0;t<o.paths.length;t++){const e=o.paths[t];i.updateIntArray([1397116793,e.length]);for(let r=0;r<e.length;r++)i.updateIntArray([3962308117,r]),B(o.getPoint(t,r),i,u),i.updateIntArray([2716288009]);i.updateIntArray([2278822459])}return i.updateIntArray([2568784753]),void B(o.spatialReference.wkid,i,u)}if(o instanceof L){i.updateIntArray([588535921,o.points.length]);for(let t=0;t<o.points.length;t++){const e=o.getPoint(t);i.updateIntArray([t]),B(e,i,u)}return i.updateIntArray([1700171621]),void B(o.spatialReference.wkid,i,u)}if(o instanceof P)return i.updateIntArray([3483648373]),i.updateIntArray([0]),i.updateFloatArray([o.xmax]),i.updateIntArray([1]),i.updateFloatArray([o.xmin]),i.updateIntArray([2]),i.updateFloatArray([o.ymax]),i.updateIntArray([3]),i.updateFloatArray([o.ymin]),o.hasZ&&(i.updateIntArray([4]),i.updateFloatArray([o.zmax]),i.updateIntArray([5]),i.updateFloatArray([o.zmin])),o.hasM&&(i.updateIntArray([6]),i.updateFloatArray([o.mmax]),i.updateIntArray([7]),i.updateFloatArray([o.mmin])),i.updateIntArray([3622027469]),void B(o.spatialReference.wkid,i,u);if(o instanceof F)return i.updateIntArray([14]),void 0!==o.wkid&&null!==o.wkid&&i.updateIntArray([o.wkid]),o.wkt&&i.updateWithString(o.wkt),void(o.wkt2&&i.updateWithString(o.wkt2));if(U(o))throw new n(u.context,a.UnsupportedHashType,u.node);if(v(o))throw new n(u.context,a.UnsupportedHashType,u.node);if(j(o))throw new n(u.context,a.UnsupportedHashType,u.node);if(o===l)throw new n(u.context,a.UnsupportedHashType,u.node);throw new n(u.context,a.UnsupportedHashType,u.node)}if(i.updateUint8Array([223]),u.map.has(o)){const t=u.map.get(o);i.updateIntArray([61237541^t])}else{u.map.set(o,u.currentLength++);for(const t of o.keys()){i.updateIntArray([t.length]),i.updateWithString(t),i.updateUint8Array([251]);B(o.field(t),i,u),i.updateUint8Array([239])}u.map.delete(o),u.currentLength--}i.updateUint8Array([73])}}else i.updateUint8Array([0,139])}function E(e,m){e.portal=function(e,r){return m(e,r,((n,a,u)=>(o(u,1,1,e,r),new t(i(u[0])))))},e.typeof=function(t,e){return m(t,e,((r,i,s)=>{o(s,1,1,t,e);const l=u(s[0]);if("Unrecognized Type"===l)throw new n(t,a.UnrecognizedType,e);return l}))},e.trim=function(t,e){return m(t,e,((r,n,a)=>(o(a,1,1,t,e),i(a[0]).trim())))},e.tohex=function(t,e){return m(t,e,((r,n,a)=>{o(a,1,1,t,e);const i=s(a[0]);return isNaN(i)?i:i.toString(16)}))},e.upper=function(t,e){return m(t,e,((r,n,a)=>(o(a,1,1,t,e),i(a[0]).toUpperCase())))},e.proper=function(t,e){return m(t,e,((r,n,a)=>{o(a,1,2,t,e);let u=1;2===a.length&&"firstword"===i(a[1]).toLowerCase()&&(u=2);const s=/\s/,l=i(a[0]);let p="",c=!0;for(let t=0;t<l.length;t++){let e=l[t];if(s.test(e))1===u&&(c=!0);else{e.toUpperCase()!==e.toLowerCase()&&(c?(e=e.toUpperCase(),c=!1):e=e.toLowerCase())}p+=e}return p}))},e.lower=function(t,e){return m(t,e,((r,n,a)=>(o(a,1,1,t,e),i(a[0]).toLowerCase())))},e.guid=function(t,e){return m(t,e,((r,n,a)=>{if(o(a,0,1,t,e),a.length>0)switch(i(a[0]).toLowerCase()){case"digits":return k().replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return k();case"digits-hyphen-braces":return"{"+k()+"}";case"digits-hyphen-parentheses":return"("+k()+")"}return"{"+k()+"}"}))},e.standardizeguid=function(t,e){return m(t,e,((r,n,a)=>{o(a,2,2,t,e);let u=i(a[0]);if(""===u||null===u)return"";const s=/^(\{|\()?(?<partA>[0-9a-z]{8})(-?)(?<partB>[0-9a-z]{4})(-?)(?<partC>[0-9a-z]{4})(-?)(?<partD>[0-9a-z]{4})(-?)(?<partE>[0-9a-z]{12})(\}|\))?$/gim.exec(u);if(!s)return"";const l=s.groups;switch(u=l.partA+"-"+l.partB+"-"+l.partC+"-"+l.partD+"-"+l.partE,i(a[1]).toLowerCase()){case"digits":return u.replace("-","").replace("-","").replace("-","").replace("-","");case"digits-hyphen":return u;case"digits-hyphen-braces":return"{"+u+"}";case"digits-hyphen-parentheses":return"("+u+")"}return"{"+u+"}"}))},e.console=function(t,e){return m(t,e,((e,r,n)=>(0===n.length||(1===n.length?t.console(i(n[0])):t.console(i(n))),l)))},e.mid=function(t,e){return m(t,e,((r,n,a)=>{o(a,2,3,t,e);let u=s(a[1]);if(isNaN(u))return"";if(u=Math.max(0,u),2===a.length)return i(a[0]).slice(u);let l=s(a[2]);return isNaN(l)?"":(l<0&&(l=0),i(a[0]).slice(u,u+l))}))},e.find=function(t,e){return m(t,e,((r,n,a)=>{o(a,2,3,t,e);let u=0;if(a.length>2){if(u=s(p(a[2],0)),isNaN(u))return-1;u<0&&(u=0)}return i(a[1]).indexOf(i(a[0]),u)}))},e.left=function(t,e){return m(t,e,((r,n,a)=>{o(a,2,2,t,e);let u=s(a[1]);return isNaN(u)?"":(u<0&&(u=0),i(a[0]).slice(0,u))}))},e.right=function(t,e){return m(t,e,((r,n,a)=>{o(a,2,2,t,e);const u=s(a[1]);if(isNaN(u)||u<=0)return"";return i(a[0]).slice(-u)}))},e.split=function(t,e){return m(t,e,((r,n,a)=>{let u;o(a,2,4,t,e);let l=s(p(a[2],-1));const f=c(p(a[3],!1));if(-1===l||null===l||!0===f?u=i(a[0]).split(i(a[1])):(isNaN(l)&&(l=-1),l<-1&&(l=-1),u=i(a[0]).split(i(a[1]),l)),!1===f)return u;const d=[];for(let t=0;t<u.length&&!(-1!==l&&d.length>=l);t++)""!==u[t]&&void 0!==u[t]&&d.push(u[t]);return d}))},e.text=function(t,e){return m(t,e,((r,n,a)=>(o(a,1,2,t,e),f(a[0],a[1]))))},e.concatenate=function(t,e){return m(t,e,((t,e,r)=>{const n=[];if(r.length<1)return"";if(R(r[0])){const t=p(r[2],"");for(let e=0;e<r[0].length;e++)n[e]=f(r[0][e],t);return r.length>1?n.join(r[1]):n.join("")}if(d(r[0])){const t=p(r[2],"");for(let e=0;e<r[0].length();e++)n[e]=f(r[0].get(e),t);return r.length>1?n.join(r[1]):n.join("")}for(let a=0;a<r.length;a++)n[a]=f(r[a]);return n.join("")}))},e.reverse=function(t,e){return m(t,e,((r,i,u)=>{if(o(u,1,1,t,e),R(u[0])){const t=u[0].slice();return t.reverse(),t}if(d(u[0])){const t=u[0].toArray().slice();return t.reverse(),t}throw new n(t,a.InvalidParameter,e)}))},e.replace=function(t,e){return m(t,e,((r,n,a)=>{o(a,3,4,t,e);const u=i(a[0]),s=i(a[1]),l=i(a[2]);return 4!==a.length||c(a[3])?h(u,s,l):u.replace(s,l)}))},e.urlencode=function(t,e){return m(t,e,((n,a,u)=>{if(o(u,1,1,t,e),null===u[0])return"";if(u[0]instanceof r){let t="";for(const e of u[0].keys()){const r=u[0].field(e);""!==t&&(t+="&"),t+=null===r?encodeURIComponent(e)+"=":encodeURIComponent(e)+"="+encodeURIComponent(r)}return t}return encodeURIComponent(i(u[0]))}))},e.hash=function(t,e){return m(t,e,((r,n,a)=>{o(a,1,1,t,e);const i=new N(0);return B(a[0],i,{context:t,node:e,map:new Map,currentLength:0}),i.digest()}))},e.convertdirection=function(t,e){return m(t,e,((r,n,a)=>(o(a,3,3,t,e),x(a[0],a[1],a[2]))))},e.fromjson=function(t,e){return m(t,e,((u,s,l)=>{if(o(l,1,1,t,e),!1===b(l[0]))throw new n(t,a.InvalidParameter,e);return r.convertJsonToArcade(JSON.parse(i(l[0])),y(t))}))},e.tocharcode=function(t,e){return m(t,e,((r,u,l)=>{o(l,1,2,t,e);const c=s(p(l[1],0)),f=i(l[0]);if(0===f.length&&1===l.length)return null;if(f.length<=c||c<0)throw new n(t,a.OutOfBounds,e);return f.charCodeAt(c)}))},e.tocodepoint=function(t,e){return m(t,e,((r,u,l)=>{o(l,1,2,t,e);const c=s(p(l[1],0)),f=i(l[0]);if(0===f.length&&1===l.length)return null;if(f.length<=c||c<0)throw new n(t,a.OutOfBounds,e);return f.codePointAt(c)}))},e.fromcharcode=function(t,e){return m(t,e,((r,o,i)=>{if(i.length<1)throw new n(t,a.WrongNumberOfParameters,e);const u=i.map((t=>Math.trunc(s(t)))).filter((t=>t>=0&&t<=65535));return 0===u.length?null:String.fromCharCode.apply(null,u)}))},e.fromcodepoint=function(t,e){return m(t,e,((r,o,i)=>{if(i.length<1)throw new n(t,a.WrongNumberOfParameters,e);let u;try{u=i.map((t=>Math.trunc(s(t)))).filter((t=>t<=1114111&&t>>>0===t))}catch(l){return null}return 0===u.length?null:String.fromCodePoint.apply(null,u)}))},e.getuser=function(e,u){return m(e,u,((s,l,c)=>{o(c,0,2,e,u);let f=p(c[1],"");if(f=!0===f||!1===f?"":i(f),null!==f&&""!==f)return null;if(0===c.length||c[0]instanceof t){let t=null;if(t=e.services?.portal?e.services.portal:T.getDefault(),c.length>0){if(!J(c[0].field("url"),t))return null}if(!t)return null;if(""===f){const n=H(t);if(n){const t=JSON.parse(JSON.stringify(n));for(const e of["lastLogin","created","modified"])void 0!==t[e]&&null!==t[e]&&(t[e]=new Date(t[e]));return r.convertObjectToArcadeDictionary(t,y(e))}}return null}throw new n(e,a.InvalidParameter,u)}))},e.getenvironment=function(t,e){return m(t,e,((n,a,i)=>(o(i,0,0,t,e),r.convertObjectToArcadeDictionary(g(y(t),t.spatialReference),y(t),!0))))},e.standardizefilename=function(t,e){return m(t,e,((t,e,r)=>{o(r,1,1,t,e);const[i]=r;if(null==i)return"";if(!b(i))throw new n(t,a.InvalidParameter,e);return i.replaceAll(/[<>"?*]/g,"_").replaceAll(/[/\\|]/g,"-").replaceAll(":",", ")}))}}export{E as registerFunctions};
