/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeExecutionError as n,ExecutionErrorCodes as r}from"../executionError.js";import t from"../ImmutableArray.js";import{q as s}from"../../chunks/languageUtils.js";import{ensureNumber as e}from"../../core/accessorSupport/ensureType.js";import a from"../../geometry/Point.js";import{fromJSON as o}from"../../geometry/support/jsonUtils.js";function i(t,s,e=null){const a=u(t,!0);if(void 0!==a.hasm&&(a.hasM=a.hasm,delete a.hasm),void 0!==a.hasz&&(a.hasZ=a.hasz,delete a.hasz),void 0!==a.spatialreference&&(a.spatialReference=a.spatialreference,delete a.spatialreference),a.spatialReference||(a.spatialReference=s),void 0!==a.rings){const n=I(f(a.rings),a.hasZ,a.hasM);if(null==n)return null;a.rings=n.arrays,a.hasZ=n.hasZ,a.hasM=n.hasM}if(void 0!==a.paths){const n=I(f(a.paths),a.hasZ,a.hasM);if(null==n)return null;a.paths=n.arrays,a.hasZ=n.hasZ,a.hasM=n.hasM}if(void 0!==a.points){const n=k(a.points,a.hasZ,a.hasM);if(null==n)return null;a.points=n.array,a.hasZ=n.hasZ,a.hasM=n.hasM}const i=o(a);if(null!=e&&i?.type!==e)throw new n(null,r.InvalidParameter,null);return i}function u(n,r=!1){const t={};for(const e of n.keys()){const a=r?e.toLowerCase():e,o=n.attributes[e];t[a]=s(o)?u(o):o}return t}const l=Symbol("NoValue");function h(n){return Array.isArray(n)&&n.length>0?n[0]:n instanceof t&&n.length()>0?n.get(0):l}function f(n){const r=h(h(n));return r===l||Array.isArray(r)||r instanceof t||r instanceof a?n:[n]}const c=0;function p(n){return e(n,c)??c}function y(n){return"number"==typeof n&&!Number.isNaN(n)}const m=null;function g(n){return e(n,m)??m}function M(n){return"number"==typeof n&&!Number.isNaN(n)||null===n}function Z(n){return!(n.length<2)&&("number"==typeof n[0]&&!Number.isNaN(n[0])&&("number"==typeof n[1]&&!Number.isNaN(n[1])))}function d(n){return Z(n)?n.length>2?n.slice(0,2):n:null}function A(n){return Z(n)?y(n[2])?n.length>3?n.slice(0,3):n:[n[0],n[1],p(n[2])]:null}function N(n){return Z(n)?n.length>=3&&!M(n[2])?[n[0],n[1],g(n[2])]:n.length>3?n.slice(0,3):n:null}function b(n){return Z(n)?y(n[2])&&M(n[3])?n.length>4?n.slice(0,4):n:[n[0],n[1],p(n[2]),g(n[3])]:null}function v(n,r){return n?r?b:A:r?N:d}function j(n,r){return n?r?n=>[n.x,n.y,n.z??c,n.m??m]:n=>[n.x,n.y,n.z??c]:r?n=>[n.x,n.y,n.m??m]:n=>[n.x,n.y]}function x(n,r,s){return Array.isArray(n)?s(n):n instanceof a?r(n):n instanceof t?s(n.toArray()):null}function z(n,r,s){return Array.isArray(n)?n.length>=r:n instanceof t?n.length()>=r:n instanceof a&&n[s]}function w(n,r,t){return void 0===n&&void 0===r?{hasZ:z(t,3,"hasZ"),hasM:z(t,4,"hasM")}:void 0===n?!0===r?{hasZ:z(t,4,"hasZ"),hasM:!0}:{hasZ:z(t,3,"hasZ"),hasM:!1}:void 0===r?!0===n?{hasZ:!0,hasM:z(t,4,"hasM")}:{hasZ:!1,hasM:z(t,3,"hasM")}:{hasZ:!0===n,hasM:!0===r}}function R(n,r,s){const e=[];if(Array.isArray(n))for(let t=0;t<n.length;t++){const a=x(n[t],r,s);null!=a&&e.push(a)}else if(n instanceof t)for(let t=0;t<n.length();t++){const a=x(n.get(t),r,s);null!=a&&e.push(a)}return e}function k(n,r,t){const s=h(n);if(s===l)return null;const{hasZ:e,hasM:a}=w(r,t,s);return{array:R(n,j(e,a),v(e,a)),hasZ:e,hasM:a}}function I(n,r,s){const e=h(h(n));if(e===l)return null;const{hasZ:a,hasM:o}=w(r,s,e),i=j(a,o),u=v(a,o),f=[];if(Array.isArray(n))for(let t=0;t<n.length;t++)f.push(R(n[t],i,u));else if(n instanceof t)for(let t=0;t<n.length();t++)f.push(R(n.get(t),i,u));return{arrays:f,hasZ:a,hasM:o}}export{i as constructGeometryFromDictionary};
