/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../Dictionary.js";import{ArcadeExecutionError as n,ExecutionErrorCodes as t}from"../executionError.js";import{D as r,J as i,E as l,m as o,G as a,K as f}from"../../chunks/languageUtils.js";import{getMetersPerVerticalUnitForSR as s,segmentLength3d as c}from"../functions/centroid.js";import{measureToCoordinate as m,pointToCoordinate as u,distanceToCoordinate as p}from"../functions/measures.js";import{getMetersPerUnitForSR as y}from"../../core/unitUtils.js";import w from"../../geometry/Geometry.js";import h from"../../geometry/Point.js";import d from"../../geometry/Polyline.js";import{isArray as g,isNumber as v}from"../../support/guards.js";function j(e,i,l){if(r(e,2,2,i,l),e[0]instanceof w&&e[1]instanceof w);else if(e[0]instanceof w&&null===e[1]);else if(e[1]instanceof w&&null===e[0]);else if(null!==e[0]||null!==e[1])throw new n(i,t.InvalidParameter,l)}function P(e){if("polygon"!==e.type&&"polyline"!==e.type&&"extent"!==e.type)return 0;let n=1;if(e.spatialReference.vcsWkid||e.spatialReference.latestVcsWkid){n=s(e.spatialReference)/y(e.spatialReference)}let t=0;if("polyline"===e.type)for(const r of e.paths)for(let e=1;e<r.length;e++)t+=c(r[e],r[e-1],n);else if("polygon"===e.type)for(const r of e.rings){for(let e=1;e<r.length;e++)t+=c(r[e],r[e-1],n);(r[0][0]!==r[r.length-1][0]||r[0][1]!==r[r.length-1][1]||void 0!==r[0][2]&&r[0][2]!==r[r.length-1][2])&&(t+=c(r[0],r[r.length-1],n))}else"extent"===e.type&&(t+=2*c([e.xmin,e.ymin,0],[e.xmax,e.ymin,0],n),t+=2*c([e.xmin,e.ymin,0],[e.xmin,e.ymax,0],n),t*=2,t+=4*Math.abs(i(e.zmax,0)*n-i(e.zmin,0)*n));return t}const x=(i,s,c)=>{if(c=l(c),r(c,2,2,i,s),null===c[0])return null;let u=c[0];if((g(c[0])||o(c[0]))&&(u=a(c[0],i.spatialReference)),null===u)return null;if(!(u instanceof w))throw new n(i,t.InvalidParameter,s);if(!(u instanceof d))throw new n(i,t.InvalidParameter,s);if(!v(c[1]))throw new n(i,t.InvalidParameter,s);const p=m(u,c[1]);return p?e.convertObjectToArcadeDictionary(p,f(i),!1,!0):null},I=(i,s,c)=>{if(c=l(c),r(c,2,2,i,s),null===c[0])return null;let m=c[0];if((g(c[0])||o(c[0]))&&(m=a(c[0],i.spatialReference)),null===m)return null;if(!(m instanceof w))throw new n(i,t.InvalidParameter,s);if(!(m instanceof d))throw new n(i,t.InvalidParameter,s);const p=c[1];if(null===p)return null;if(!(p instanceof h))throw new n(i,t.InvalidParameter,s);const y=u(m,p);return y?e.convertObjectToArcadeDictionary(y,f(i),!1,!0):null},R=(i,s,c)=>{if(c=l(c),r(c,2,2,i,s),null===c[0])return null;let m=c[0];if((g(c[0])||o(c[0]))&&(m=a(c[0],i.spatialReference)),null===m)return null;if(!(m instanceof w))throw new n(i,t.InvalidParameter,s);if(!(m instanceof d))throw new n(i,t.InvalidParameter,s);if(!v(c[1]))throw new n(i,t.InvalidParameter,s);const u=p(m,c[1]);return u?e.convertObjectToArcadeDictionary(u,f(i),!1,!0):null};export{j as commonRelationsCheck,R as distanceToCoordinateFunc,x as measureToCoordinateFunc,P as planarLength3D,I as pointToCoordinateFunc};
