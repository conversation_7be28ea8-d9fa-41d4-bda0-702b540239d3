/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{execute as n}from"../../geometry/operators/affineTransformOperator.js";import{l as f}from"../../chunks/geodesicBufferOperator.js";export{g as geodesicBuffer}from"../../chunks/geodesicBufferOperator.js";import{l as m}from"../../chunks/geodeticAreaOperator.js";export{g as geodeticArea}from"../../chunks/geodeticAreaOperator.js";import{l as h}from"../../chunks/geodeticDensifyOperator.js";export{g as geodeticDensify}from"../../chunks/geodeticDensifyOperator.js";import{l as j}from"../../chunks/geodeticLengthOperator.js";export{g as geodeticLength}from"../../chunks/geodeticLengthOperator.js";import x from"../../geometry/operators/support/Transformation.js";export{a as area}from"../../chunks/areaOperator.js";export{b as buffer}from"../../chunks/bufferOperator.js";export{c as centroid}from"../../chunks/centroidOperator.js";export{c as clip}from"../../chunks/clipOperator.js";export{c as contains}from"../../chunks/containsOperator.js";export{c as convexHull}from"../../chunks/convexHullOperator.js";export{c as crosses}from"../../chunks/crossesOperator.js";export{c as cut}from"../../chunks/cutOperator.js";export{d as densify}from"../../chunks/densifyOperator.js";export{d as difference}from"../../chunks/differenceOperator.js";export{d as disjoint}from"../../chunks/disjointOperator.js";export{d as distance}from"../../chunks/distanceOperator.js";export{e as equals}from"../../chunks/equalsOperator.js";export{g as generalize}from"../../chunks/generalizeOperator.js";export{i as intersection}from"../../chunks/intersectionOperator.js";export{i as intersects}from"../../chunks/intersectsOperator.js";export{l as labelPoint}from"../../chunks/labelPointOperator.js";export{l as length}from"../../chunks/lengthOperator.js";export{o as offset}from"../../chunks/offsetOperator.js";export{o as overlaps}from"../../chunks/overlapsOperator.js";export{p as proximity}from"../../chunks/proximityOperator.js";export{r as relate}from"../../chunks/relateOperator.js";export{s as simplify}from"../../chunks/simplifyOperator.js";export{s as symmetricDifference}from"../../chunks/symmetricDifferenceOperator.js";export{t as touches}from"../../chunks/touchesOperator.js";export{u as union}from"../../chunks/unionOperator.js";export{w as within}from"../../chunks/withinOperator.js";function O(r,e,o,s){const t=(new x).rotate(e,o,s);return n(r,t)}async function k(){await Promise.all([m(),f(),h(),j()])}export{k as loadAll,O as rotate};
