/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{fromJSON as e}from"../../geometry/support/jsonUtils.js";function t(e,t){let r;return{loaded:!1,load:()=>r??=t().then((t=>{o[e]={loaded:!0,execute:t}}))}}function r(e){return null==e?null:e.toJSON()}const o={disjoint:t("disjoint",(()=>import("../../geometry/operators/json/disjointOperator.js").then((e=>e.execute)))),intersects:t("intersects",(()=>import("../../geometry/operators/json/intersectsOperator.js").then((e=>e.execute)))),touches:t("touches",(()=>import("../../geometry/operators/json/touchesOperator.js").then((e=>e.execute)))),crosses:t("crosses",(()=>import("../../geometry/operators/json/crossesOperator.js").then((e=>e.execute)))),within:t("within",(()=>import("../../geometry/operators/json/withinOperator.js").then((e=>e.execute)))),contains:t("contains",(()=>import("../../geometry/operators/json/containsOperator.js").then((e=>e.execute)))),overlaps:t("overlaps",(()=>import("../../geometry/operators/json/overlapsOperator.js").then((e=>e.execute)))),equals:t("equals",(async()=>{const t=await import("../../geometry/operators/equalsOperator.js");return(r,o)=>t.execute(e(r),e(o))})),relate:t("relate",(async()=>{const t=await import("../../geometry/operators/relateOperator.js");return(r,o,n)=>t.execute(e(r),e(o),n)})),intersection:t("intersection",(()=>import("../../geometry/operators/json/intersectionOperator.js").then((e=>e.execute)))),union:t("union",(()=>import("../../geometry/operators/json/unionOperator.js").then((e=>e.executeMany)))),difference:t("difference",(async()=>{const t=await import("../../geometry/operators/differenceOperator.js");return(o,n)=>r(t.execute(e(o),e(n)))})),symmetricDifference:t("symmetricDifference",(async()=>{const t=await import("../../geometry/operators/symmetricDifferenceOperator.js");return(o,n)=>r(t.execute(e(o),e(n)))})),clip:t("clip",(async()=>{const t=await import("../../geometry/operators/clipOperator.js");return(o,n)=>r(t.execute(e(o),e(n)))})),cut:t("cut",(async()=>{const t=await import("../../geometry/operators/cutOperator.js");return(o,n)=>t.execute(e(o),e(n)).map((e=>r(e)))})),area:t("area",(async()=>{const t=await import("../../geometry/operators/areaOperator.js"),{convertFromSpatialReferenceUnit:r,toAreaUnit:o}=await import("./unitConversion.js");return(n,a)=>{const s=t.execute(e(n));return r(n.spatialReference,o(a),s)}})),geodeticArea:t("geodeticArea",(async()=>{const t=await import("../../geometry/operators/geodeticAreaOperator.js"),{convert:r,squareMeters:o,toAreaUnit:n}=await import("./unitConversion.js");return await t.load(),(a,s)=>{const i=t.execute(e(a));return r(o,n(s),i)}})),length:t("length",(async()=>{const e=await import("../../geometry/operators/json/lengthOperator.js"),{convertFromSpatialReferenceUnit:t,toLengthUnit:r}=await import("./unitConversion.js");return(o,n)=>{const a=e.execute(o);return t(o.spatialReference,r(n),a)}})),geodeticLength:t("geodeticLength",(async()=>{const t=await import("../../geometry/operators/geodeticLengthOperator.js"),{convert:r,meters:o,toLengthUnit:n}=await import("./unitConversion.js");return await t.load(),(a,s)=>{const i=t.execute(e(a));return r(o,n(s),i)}})),distance:t("distance",(async()=>{const t=await import("../../geometry/operators/distanceOperator.js"),{convertFromSpatialReferenceUnit:r,toLengthUnit:o}=await import("./unitConversion.js");return(n,a,s)=>{const i=t.execute(e(n),e(a));return r(n.spatialReference,o(s),i)}})),densify:t("densify",(async()=>{const t=await import("../../geometry/operators/densifyOperator.js"),{convertToSpatialReferenceUnit:o,toLengthUnit:n}=await import("./unitConversion.js");return(a,s,i)=>(s=o(n(i),a.spatialReference,s),r(t.execute(e(a),s)))})),geodeticDensify:t("geodeticDensify",(async()=>{const t=await import("../../geometry/operators/geodeticDensifyOperator.js"),{convert:o,meters:n,toLengthUnit:a}=await import("./unitConversion.js");return await t.load(),(s,i,c)=>(i=o(a(c),n,i),r(t.execute(e(s),i)))})),generalize:t("generalize",(async()=>{const t=await import("../../geometry/operators/generalizeOperator.js"),{convertToSpatialReferenceUnit:o,toLengthUnit:n}=await import("./unitConversion.js");return(a,s,i,c)=>(s=o(n(i),a.spatialReference,s),r(t.execute(e(a),s,c)))})),buffer:t("buffer",(async()=>{const e=await import("../../geometry/operators/json/bufferOperator.js"),{convertToSpatialReferenceUnit:t,toLengthUnit:r}=await import("./unitConversion.js");return(o,n,a)=>(n=t(r(a),o.spatialReference,n),e.execute(o,n))})),geodesicBuffer:t("geodesicBuffer",(async()=>{const e=await import("../../geometry/operators/json/geodesicBufferOperator.js"),{convert:t,meters:r,toLengthUnit:o}=await import("./unitConversion.js");return await e.load(),(n,a,s)=>(a=t(o(s),r,a),e.execute(n,a))})),offset:t("offset",(async()=>{const e=await import("../../geometry/operators/json/offsetOperator.js"),{convertToSpatialReferenceUnit:t,toLengthUnit:r}=await import("./unitConversion.js");return(o,n,a,s)=>(n=t(r(a),o.spatialReference,n),e.execute(o,n,s))})),rotate:t("rotate",(async()=>{const t=await import("../../geometry/operators/affineTransformOperator.js"),{default:o}=await import("../../geometry/operators/support/Transformation.js");return(n,a,s,i)=>{const c=(new o).rotate(a,s,i);return r(t.execute(e(n),c))}})),centroid:t("centroid",(async()=>{const t=await import("../../geometry/operators/centroidOperator.js");return o=>r(t.execute(e(o)))})),labelPoint:t("labelPoint",(async()=>{const t=await import("../../geometry/operators/labelPointOperator.js");return o=>r(t.execute(e(o)))})),simplify:t("simplify",(()=>import("../../geometry/operators/json/simplifyOperator.js").then((e=>e.execute)))),isSimple:t("isSimple",(()=>import("../../geometry/operators/json/simplifyOperator.js").then((e=>e.isSimple)))),convexHull:t("convexHull",(()=>import("../../geometry/operators/json/convexHullOperator.js").then((e=>e.execute)))),getNearestCoordinate:t("getNearestCoordinate",(async()=>{const t=await import("../../geometry/operators/proximityOperator.js");return(o,n,a)=>{const s=t.getNearestCoordinate(e(o),e(n),a);return{...s,coordinate:r(s.coordinate)}}})),getNearestVertex:t("getNearestVertex",(async()=>{const t=await import("../../geometry/operators/proximityOperator.js");return(o,n)=>{const a=t.getNearestVertex(e(o),e(n));return{...a,coordinate:r(a.coordinate)}}}))};function n(e,t){const r=o[e];return r.loaded?r.execute.apply(void 0,t):r.load().then((()=>n(e,t)))}export{n as invokeGeometryOp};
