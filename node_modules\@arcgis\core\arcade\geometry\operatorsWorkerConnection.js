/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{open as r}from"../../core/workers/workers.js";let e,o,t=!1;function n(){return e??=r("arcadeGeometryOperatorsWorker").then((r=>{o=r,t=!0,e=void 0}))}async function a(r,e){return t?o.apply("invokeGeometryOp",[r,e]):(await n(),a(r,e))}export{a as invokeRemoteGeometryOp};
