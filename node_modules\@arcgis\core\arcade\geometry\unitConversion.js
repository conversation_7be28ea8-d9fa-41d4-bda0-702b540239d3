/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{ArcadeExecutionError as e,ExecutionErrorCodes as r}from"../executionError.js";import{areaUnitLookup as a,linearUnitLookup as s,angularUnitLookup as t,linearUnitToAreaUnit as n}from"./extendedUnitData.js";export{meters,squareMeters}from"./extendedUnitData.js";import{findSpatialReferenceUnitFromWkt as c}from"./wkt.js";import"../../core/has.js";import{gradGcsIds as u}from"../../core/unitUtils.js";import i from"../../geometry/support/WKIDUnitConversion.js";const l=-1;function o(e,r){let n;switch(e){case"linear":n=s;break;case"angular":n=t;break;case"area":n=a;break;default:return null}return n.get(r)}function m(e){const r=e.wkid;if(null!=r){const e=i.units[i[r]];if(null!=e)switch(e){case"Meter":return o("linear",9001);case"Foot":return o("linear",9002);case"Foot_US":return o("linear",9003);case"Foot_Clarke":return o("linear",9005);case"Yard_Clarke":return o("linear",9037);case"Link_Clarke":return o("linear",9039);case"Yard_Sears":return o("linear",9040);case"Foot_Sears":return o("linear",9041);case"Chain_Sears":return o("linear",9042);case"Chain_Benoit_1895_B":return o("linear",9062);case"Yard_Indian":return o("linear",9084);case"Yard_Indian_1937":return o("linear",9085);case"Foot_Gold_Coast":return o("linear",9094);case"Chain":return o("linear",9097);case"Chain_Sears_1922_Truncated":return o("linear",9301);case"50_Kilometers":return o("linear",109030);case"150_Kilometers":return o("linear",109031);default:throw new Error(`Unknown unit name: ${e}`)}return u.has(r)?o("angular",9105):o("angular",9102)}const a=e.wkt2||e.wkt;if(null!=a){const e=c(a);if(null!=e){if(null!=e.wkid){const r=o(e.type,e.wkid);if(null!=r)return r}return{type:e.type,wkid:l,factor:e.factor}}}return null}function f(e){if("linear"!==e.type)return null;if(e.wkid===l)return{type:"area",wkid:l,factor:e.factor**2};const r=n.get(e.wkid);return null==r?null:o("area",r)}function q(n){if(null!=n){if("number"==typeof n)return a.get(n)??s.get(n)??t.get(n);if("string"!=typeof n)throw new e(null,r.InvalidParameter,null);switch(n.toLowerCase().replaceAll(/[\s-]+/g,"")){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":return a.get(109404);case"miles":case"mile":case"squaremile":case"squaremiles":return a.get(109439);case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return a.get(109414);case"acres":case"acre":case"ac":return a.get(109402);case"hectares":case"hectare":case"ha":return a.get(109401);case"yard":case"yd":case"yards":case"squareyards":case"squareyard":return a.get(109442);case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return a.get(109405);case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return a.get(109409);case"millimeter":case"millimeters":case"squaremillimeter":case"squaremillimeters":return a.get(109452);case"centimeter":case"centimeters":case"squarecentimeter":case"squarecentimeters":return a.get(109451);case"decimeter":case"decimeters":case"squaredecimeter":case"squaredecimeters":return a.get(109450);case"inch":case"inches":case"squareinch":case"squareinches":return a.get(109453);case"usfoot":case"usfeet":case"squareusfoot":case"squareusfeet":return a.get(109406);case"usmile":case"usmiles":case"squareusmile":case"squareusmiles":return a.get(109413)}}}function d(n){if(null!=n){if("number"==typeof n)return s.get(n)??t.get(n)??a.get(n);if("string"!=typeof n)throw new e(null,r.InvalidParameter,null);switch(n.toLowerCase().replaceAll(/[\s-]+/g,"")){case"meters":case"meter":case"m":case"squaremeters":case"squaremeter":case"hectares":case"hectare":case"ha":return s.get(9001);case"miles":case"mile":case"squaremile":case"squaremiles":return s.get(9093);case"kilometers":case"kilometer":case"squarekilometers":case"squarekilometer":case"km":return s.get(9036);case"yard":case"yd":case"yards":case"squareyards":case"squareyard":case"acres":case"acre":case"ac":return s.get(9096);case"feet":case"ft":case"foot":case"squarefeet":case"squarefoot":return s.get(9002);case"nmi":case"nauticalmile":case"nauticalmiles":case"squarenauticalmile":case"squarenauticalmiles":return s.get(9030);case"millimeter":case"millimeters":case"squaremillimeter":case"squaremillimeters":return s.get(109007);case"centimeter":case"centimeters":case"squarecentimeter":case"squarecentimeters":return s.get(109006);case"decimeter":case"decimeters":case"squaredecimeter":case"squaredecimeters":return s.get(109005);case"inch":case"inches":case"squareinch":case"squareinches":return s.get(109008);case"usfoot":case"usfeet":case"squareusfoot":case"squareusfeet":return s.get(9003);case"usmile":case"usmiles":case"squareusmile":case"squareusmiles":return s.get(9035)}}}function g(e,r,a){if(e.type!==r.type)throw new Error(`Incompatible unit types. src=${e.type} dest=${r.type}`);return a*(e.factor/r.factor)}function p(e,r,a){if(null==e||null==r)return a;const s=m(e);if(null==s)throw new Error("Unknown spatial reference unit.");const t="area"===r.type&&"linear"===s.type?f(s):s;if(null==t)throw new Error(`Unknown spatial reference ${r.type} unit.`);return g(t,r,a)}function w(e,r,a){if(null==e||null==r)return a;const s=m(r);if(null==s)throw new Error("Unknown spatial reference unit.");const t="area"===e.type&&"linear"===s.type?f(s):s;if(null==t)throw new Error(`Unknown spatial reference ${e.type} unit.`);return g(e,t,a)}function h(e,r,a){return null==e||null==r?a:g(e,r,a)}export{h as convert,p as convertFromSpatialReferenceUnit,w as convertToSpatialReferenceUnit,m as getSpatialReferenceUnit,q as toAreaUnit,d as toLengthUnit};
