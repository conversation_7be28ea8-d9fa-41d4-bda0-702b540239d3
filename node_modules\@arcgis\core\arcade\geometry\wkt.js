/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
const t=[];function e(t){return 0===t.length?'""':'"'===t[0]||"."===t[0]||t[0]>="0"&&t[0]<="9"?t:'"'+t.trim()+'"'}function n(t){let n="",r="",u=!1;for(let l=0;l<t.length;l++){const s=t[l];u?'"'===s?'"'===t[l+1]?(n+=`\\${s}`,l+=1):u=!1:n+=s:/\s/.test(s)||(","===s?(r+=""!==n?e(n)+",":",",n=""):")"===s||"]"===s?(r+=""!==n?e(n)+"]}":"]}",n=""):"("===s||"["===s?(r+='{ "entity": "'+n.toUpperCase().trim()+'", "values":[',n=""):'"'===s?(u=!0,n=""):n+=s)}return JSON.parse(r)}function r(e){try{for(let n=0;n<t.length;n++)if(t[n].spatialReferenceWkt===e)return t[n].unit;const r=l(n(e));if(null===r)return null;let s=null;for(const t of r.values)if("object"==typeof t&&("UNIT"===t.entity||"ANGLEUNIT"===t.entity||"LENGTHUNIT"===t.entity)){s=t;break}if(null===s)return null;const i=u("GEOGCS"===r.entity||"GEOGCRS"===r.entity?"angular":"linear",s.values[1],s.values[2]);return t.push({spatialReferenceWkt:e,unit:i}),t.length>10&&t.shift(),i}catch(r){return null}}function u(t,e,n){if(null!=n)try{if("EPSG"===n.values[0]){return{type:t,wkid:Number.parseInt(n.values[1],10),factor:e}}}catch(r){}return{type:t,factor:e}}function l(t){if(null===t)return null;switch(t.entity){case"GEOGCRS":case"GEOGCS":case"PROJCRS":case"PROJCS":return t}const e=[];for(const n of t.values)if("object"==typeof n&&void 0!==n.entity)switch(n.entity){case"GEOGCRS":case"GEOGCS":case"PROJCRS":case"PROJCS":return n;default:e.push(n)}for(const n of e){const t=l(n);if(null!=t)return t}return null}export{r as findSpatialReferenceUnitFromWkt};
