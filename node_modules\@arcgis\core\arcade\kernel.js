/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import e from"../geometry/Extent.js";function n(n){if(null==n)return null;switch(n.type){case"polygon":case"multipoint":case"polyline":return n.extent;case"point":return new e({xmin:n.x,ymin:n.y,xmax:n.x,ymax:n.y,spatialReference:n.spatialReference});case"extent":return n}return null}function t(e,n){return e===n||("point"===e&&"esriGeometryPoint"===n||("polyline"===e&&"esriGeometryPolyline"===n||("polygon"===e&&"esriGeometryPolygon"===n||("extent"===e&&"esriGeometryEnvelope"===n||("multipoint"===e&&"esriGeometryMultipoint"===n||("point"===n&&"esriGeometryPoint"===e||("polyline"===n&&"esriGeometryPolyline"===e||("polygon"===n&&"esriGeometryPolygon"===e||("extent"===n&&"esriGeometryEnvelope"===e||"multipoint"===n&&"esriGeometryMultipoint"===e)))))))))}function o(e){if(null==e)return null;const n=e.clone();return void 0!==e.cache._geVersion&&(n.cache._geVersion=e.cache._geVersion),n}function i(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e}export{o as cloneGeometry,i as isInteger,t as sameGeomType,n as shapeExtent};
