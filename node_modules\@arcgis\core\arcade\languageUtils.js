/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import"../config.js";import"../kernel.js";import"./ArcadeDate.js";import"./ArcadeModule.js";import"./executionError.js";import"./FunctionWrapper.js";import"./ImmutableArray.js";import"./ImmutablePathArray.js";import"./ImmutablePointArray.js";import"./featureset/support/shared.js";import"../core/number.js";import"../core/sql/DateOnly.js";import"../core/sql/TimeOnly.js";import"../geometry/Extent.js";import"../geometry/Geometry.js";import"../geometry/Multipoint.js";import"../geometry/Point.js";import"../geometry/Polygon.js";import"../geometry/Polyline.js";import"../geometry/SpatialReference.js";import"../geometry/support/coordsUtils.js";import"../geometry/support/jsonUtils.js";import"../intl/locale.js";export{isArray,isBoolean,isGraphic,isInteger,isDate as isJsDate,isNumber,isString}from"../support/guards.js";import"luxon";export{I as ImplicitResult,R as ReturnResult,N as absRound,aa as arcadeVersion,H as autoCastArrayOfPointsToMultiPoint,F as autoCastArrayOfPointsToPolygon,G as autoCastArrayOfPointsToPolyline,E as autoCastFeatureToGeometry,C as binaryOperator,x as breakResult,a as castAsJson,b as castAsJsonAsync,c as castRecordToText,y as continueResult,X as defaultExecutingContext,K as defaultTimeZone,J as defaultUndefined,B as equalityTest,P as featureDomainCodeLookup,Q as featureDomainValueLookup,O as featureFullDomain,S as featureSchema,T as featureSubtypes,U as fixNullGeometry,u as fixSpatialReference,a6 as formatDate,a5 as formatNumber,a0 as getDomain,a2 as getDomainCode,a1 as getDomainValue,L as getType,A as greaterThanLessThan,g as isDate,j as isDateOnly,q as isDictionary,z as isDictionaryLike,n as isFeature,p as isFeatureSet,o as isFeatureSetCollection,i as isFunctionParameter,l as isGeometry,m as isImmutableArray,r as isKnowledgeGraph,a3 as isModule,a4 as isObject,d as isSimpleType,a9 as isSubtypeGrouplayer,Z as isSubtypeSublayer,k as isTime,V as isVoxel,W as multiReplace,a7 as parseGeometryFromJson,D as pcCheck,a8 as stableStringify,M as standardiseDateFormat,_ as tick,e as toBoolean,h as toDate,t as toNumber,Y as toNumberArray,f as toString,$ as toStringArray,s as toStringExplicit,w as voidOperation}from"../chunks/languageUtils.js";
