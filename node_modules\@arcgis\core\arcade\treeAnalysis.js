/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
import{toSymbolId as e}from"./arcadeEnvironment.js";import"../core/has.js";const n={all:{min:2,max:2},time:{min:0,max:4},dateonly:{min:0,max:3},getenvironment:{min:0,max:0},none:{min:2,max:2},any:{min:2,max:2},reduce:{min:2,max:3},map:{min:2,max:2},filter:{min:2,max:2},fromcodepoint:{min:1,max:-1},fromcharcode:{min:1,max:-1},tocodepoint:{min:1,max:2},tocharcode:{min:1,max:2},concatenate:{min:0,max:-1},expects:{min:1,max:-1},getfeatureset:{min:1,max:2},week:{min:1,max:2},fromjson:{min:1,max:1},length3d:{min:1,max:2},tohex:{min:1,max:1},hash:{min:1,max:1},timezone:{min:1,max:1},timezoneoffset:{min:1,max:1},changetimezone:{min:2,max:2},isoweek:{min:1,max:1},isoweekday:{min:1,max:1},hasvalue:{min:2,max:2},isomonth:{min:1,max:1},isoyear:{min:1,max:1},resize:{min:2,max:3},slice:{min:0,max:-1},splice:{min:0,max:-1},push:{min:2,max:2},pop:{min:1,max:1},includes:{min:2,max:2},array:{min:0,max:2},front:{min:1,max:1},back:{min:1,max:1},insert:{min:3,max:3},erase:{min:2,max:2},split:{min:2,max:4},guid:{min:0,max:1},standardizeguid:{min:2,max:2},today:{min:0,max:0},angle:{min:2,max:3},bearing:{min:2,max:3},urlencode:{min:1,max:1},now:{min:0,max:0},timestamp:{min:0,max:0},day:{min:1,max:1},month:{min:1,max:1},year:{min:1,max:1},hour:{min:1,max:1},second:{min:1,max:1},millisecond:{min:1,max:1},minute:{min:1,max:1},weekday:{min:1,max:1},toutc:{min:1,max:1},tolocal:{min:1,max:1},date:{min:0,max:8},datediff:{min:2,max:4},dateadd:{min:2,max:3},trim:{min:1,max:1},text:{min:1,max:2},left:{min:2,max:2},right:{min:2,max:2},mid:{min:2,max:3},upper:{min:1,max:1},proper:{min:1,max:2},lower:{min:1,max:1},find:{min:2,max:3},iif:{min:3,max:3},decode:{min:2,max:-1},when:{min:2,max:-1},defaultvalue:{min:2,max:3},isempty:{min:1,max:1},domaincode:{min:2,max:4},domainname:{min:2,max:4},polygon:{min:1,max:1},point:{min:1,max:1},polyline:{min:1,max:1},extent:{min:1,max:1},multipoint:{min:1,max:1},ringisclockwise:{min:1,max:1},geometry:{min:1,max:1},count:{min:0,max:-1},number:{min:1,max:2},acos:{min:1,max:1},asin:{min:1,max:1},atan:{min:1,max:1},atan2:{min:2,max:2},ceil:{min:1,max:2},floor:{min:1,max:2},round:{min:1,max:2},cos:{min:1,max:1},exp:{min:1,max:1},log:{min:1,max:1},min:{min:0,max:-1},constrain:{min:3,max:3},console:{min:0,max:-1},max:{min:0,max:-1},pow:{min:2,max:2},random:{min:0,max:0},sqrt:{min:1,max:1},sin:{min:1,max:1},tan:{min:1,max:1},abs:{min:1,max:1},isnan:{min:1,max:1},stdev:{min:0,max:-1},average:{min:0,max:-1},mean:{min:0,max:-1},sum:{min:0,max:-1},variance:{min:0,max:-1},distinct:{min:0,max:-1},first:{min:1,max:1},top:{min:2,max:2},boolean:{min:1,max:1},dictionary:{min:0,max:-1},typeof:{min:1,max:1},reverse:{min:1,max:1},replace:{min:3,max:4},sort:{min:1,max:2},feature:{min:1,max:-1},haskey:{min:2,max:2},indexof:{min:2,max:2},disjoint:{min:2,max:2},intersects:{min:2,max:2},touches:{min:2,max:2},crosses:{min:2,max:2},within:{min:2,max:2},contains:{min:2,max:2},overlaps:{min:2,max:2},equals:{min:2,max:2},relate:{min:3,max:3},intersection:{min:2,max:2},union:{min:1,max:2},difference:{min:2,max:2},symmetricdifference:{min:2,max:2},clip:{min:2,max:2},cut:{min:2,max:2},area:{min:1,max:2},areageodetic:{min:1,max:2},length:{min:1,max:2},lengthgeodetic:{min:1,max:2},distancegeodetic:{min:2,max:3},distance:{min:2,max:3},densify:{min:2,max:3},densifygeodetic:{min:2,max:3},generalize:{min:2,max:4},buffer:{min:2,max:3},buffergeodetic:{min:2,max:3},offset:{min:2,max:6},rotate:{min:2,max:3},issimple:{min:1,max:1},simplify:{min:1,max:1},convexhull:{min:1,max:1},centroid:{min:1,max:1},nearestcoordinate:{min:2,max:2},nearestvertex:{min:2,max:2},isselfintersecting:{min:1,max:1},multiparttosinglepart:{min:1,max:1},setgeometry:{min:2,max:2},portal:{min:1,max:1},getuser:{min:0,max:2},subtypes:{min:1,max:1},subtypecode:{min:1,max:1},subtypename:{min:1,max:1},domain:{min:2,max:3},convertdirection:{min:3,max:3},sqltimestamp:{min:1,max:3},schema:{min:1,max:1},measuretocoordinate:{min:2,max:2},distancetocoordinate:{min:2,max:2},pointtocoordinate:{min:2,max:2}},a={functionDefinitions:new Map,constantDefinitions:new Map},t={functionDefinitions:new Map,constantDefinitions:new Map};for(const h of["pi","infinity"])t.constantDefinitions.set(h,{type:"constant"}),a.constantDefinitions.set(h,{type:"constant"});t.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"newline",type:"constant"}]}),a.constantDefinitions.set("textformatting",{type:"namespace",key:"textformatting",members:[{key:"backwardslash",type:"constant"},{key:"tab",type:"constant"},{key:"singlequote",type:"constant"},{key:"doublequote",type:"constant"},{key:"forwardslash",type:"constant"},{key:"newline",type:"constant"}]});for(const h in n){const e=n[h];t.functionDefinitions.set(h,{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]}),a.functionDefinitions.set(h,{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]})}const i=new Set(["attachments","featureset","featuresetbyassociation","featuresetbyid","featuresetbyname","featuresetbyportalitem","featuresetbyrelationshipname","featuresetbyurl","filterbysubtypecode","getfeatureset","getfeaturesetinfo","getuser","knowledgegraphbyportalitem","querygraph"]),m=new Set(["area","areageodetic","buffer","buffergeodetic","centroid","clip","contains","convexhull","crosses","cut","densify","densifygeodetic","difference","disjoint","distance","distancegeodetic","distancetocoordinate","equals","generalize","intersection","intersects","issimple","length","length3d","lengthgeodetic","measuretocoordinate","multiparttosinglepart","nearestcoordinate","nearestvertex","offset","overlaps","pointtocoordinate","relate","rotate","simplify","symmetricdifference","touches","union","within"]);function s(e,n){const i="sync"===n?a:t;i.functionDefinitions.has(e.name.toLowerCase())?i.functionDefinitions.get(e.name.toLowerCase())?.overloads.push({type:"function",parametersInfo:{min:e.min,max:e.max}}):i.functionDefinitions.set(e.name.toLowerCase(),{overloads:[{type:"function",parametersInfo:{min:e.min,max:e.max}}]})}function o(e,n){if(e)for(const a of e)r(a,n)}function r(e,n){if(e&&!1!==n(e))switch(e.type){case"ImportDeclaration":o(e.specifiers,n),r(e.source,n);break;case"ExportNamedDeclaration":r(e.declaration,n);break;case"ArrayExpression":o(e.elements,n);break;case"AssignmentExpression":case"BinaryExpression":case"LogicalExpression":r(e.left,n),r(e.right,n);break;case"BlockStatement":case"Program":o(e.body,n);break;case"BreakStatement":case"ContinueStatement":case"EmptyStatement":case"Identifier":case"Literal":break;case"CallExpression":r(e.callee,n),o(e.arguments,n);break;case"ExpressionStatement":r(e.expression,n);break;case"ForInStatement":case"ForOfStatement":r(e.left,n),r(e.right,n),r(e.body,n);break;case"ForStatement":r(e.init,n),r(e.test,n),r(e.update,n),r(e.body,n);break;case"WhileStatement":r(e.test,n),r(e.body,n);break;case"FunctionDeclaration":r(e.id,n),o(e.params,n),r(e.body,n);break;case"IfStatement":r(e.test,n),r(e.consequent,n),r(e.alternate,n);break;case"MemberExpression":r(e.object,n),r(e.property,n);break;case"ObjectExpression":o(e.properties,n);break;case"Property":r(e.key,n),r(e.value,n);break;case"ReturnStatement":case"UnaryExpression":case"UpdateExpression":r(e.argument,n);break;case"VariableDeclaration":o(e.declarations,n);break;case"VariableDeclarator":r(e.id,n),r(e.init,n);break;case"TemplateLiteral":o(e.expressions,n),o(e.quasis,n)}}function c(e){return"Literal"===e?.type&&"string"==typeof e.value}function x(e,n){let a=!1;const t=n.toLowerCase();return r(e,(e=>!a&&("Identifier"===e.type&&e.name&&e.name.toLowerCase()===t&&(a=!0),!0))),a}function l(n){const a=[];return r(n,(n=>("ImportDeclaration"===n.type&&n.source&&n.source.value&&a.push({libname:e(n.specifiers[0].local),source:n.source.value}),!0))),a}function u(e,n){let a=!1;const t=n.toLowerCase();return r(e,(e=>!a&&("CallExpression"!==e.type||"Identifier"!==e.callee.type||!e.callee.name||e.callee.name.toLowerCase()!==t||(a=!0,!1)))),a}function p(n){const a=[];return r(n,(n=>"MemberExpression"!==n.type||"Identifier"!==n.object.type||(("Identifier"===n.property.type||"Literal"===n.property.type&&"string"==typeof n.property.value)&&a.push({varId:e(n.object),memberId:e(n.property)}),!1))),a}function f(n){const a=[];return r(n,(n=>{if("CallExpression"===n.type&&"Identifier"===n.callee.type)switch(e(n.callee)){case"expects":if(n.arguments.length>1){const[t,...i]=n.arguments;if("Identifier"===t?.type){const n=e(t);for(const e of i)c(e)&&a.push({varId:n,memberNamePattern:e.value})}}return!1;case"domainname":case"domaincode":case"domain":case"haskey":case"hasvalue":if(n.arguments.length>=2){const[t,i]=n.arguments;"Identifier"===t?.type&&c(i)&&a.push({varId:e(t),memberNamePattern:i.value})}return!0;case"defaultvalue":if(n.arguments.length>2){const[t,i]=n.arguments;"Identifier"===t?.type&&c(i)&&a.push({varId:e(t),memberNamePattern:i.value})}return!0;default:return!0}return"MemberExpression"!==n.type||"Identifier"!==n.object.type||(n.computed?!!c(n.property)&&(a.push({varId:e(n.object),memberNamePattern:n.property.value}),!1):(a.push({varId:e(n.object),memberNamePattern:n.property.name}),!1))})),a}function d(e,n){const a=[];if(void 0!==n.params&&null!==n.params)for(let t=0;t<n.params.length;t++)a.push("any");return{name:e,return:"any",params:a}}function y(e){const n=[];return r(e,(e=>("CallExpression"===e.type&&"Identifier"===e.callee.type&&n.push(e.callee.name.toLowerCase()),!0))),n}function b(e,n=[]){let a=null;if(void 0===e.usesFeatureSet){null===a&&(a=y(e)),e.usesFeatureSet=!1;for(let n=0;n<a.length;n++)i.has(a[n])&&(e.usesFeatureSet=!0,e.isAsync=!0);if(!1===e.usesFeatureSet&&n&&n.length>0)for(const a of n)if(x(e,a)){e.usesFeatureSet=!0,e.isAsync=!0;break}}if(void 0===e.usesModules){e.usesModules=!1;l(e).length>0&&(e.usesModules=!0)}if(void 0===e.usesGeometry){e.usesGeometry=!1,null===a&&(a=y(e));for(let n=0;n<a.length;n++)m.has(a[n])&&(e.usesGeometry=!0)}}function g(e){const n=y(e);for(let a=0;a<n.length;a++)if(i.has(n[a]))return!0;return!1}export{s as addFunctionDeclaration,d as extractFunctionDeclaration,f as findExpectedFieldLiterals,y as findFunctionCalls,p as findLiteralMemberAccesses,l as findModuleImports,b as findScriptDependencies,t as fullArcadeApiAsync,a as fullArcadeApiSync,u as referencesFunction,x as referencesMember,g as scriptUsesFeatureSet,r as walk};
