/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.32/esri/copyright.txt for details.
*/
var e;!function(e){e.AlreadyDefined="AlreadyDefined",e.ApiConflict="ApiConflict",e.AssignedNeverUsed="AssignedNeverUsed",e.DefinedNeverAssigned="DefinedNeverAssigned",e.DefinedNeverUsed="DefinedNeverUsed",e.EmptyBlockStatement="EmptyBlockStatement",e.ExecutionError="ExecutionError",e.InvalidCallIdentifier="InvalidCallIdentifier",e.InvalidConstantIdentifier="InvalidConstantIdentifier",e.InvalidPropertyIdentifier="InvalidPropertyIdentifier",e.NoArgumentExpected="NoArgumentExpected",e.NotDefined="NotDefined",e.NotADictionary="NotADictionary",e.NotEnoughArguments="NotEnoughArguments",e.ProfileVariablesConflict="ProfileVariablesConflict",e.ProfileVariablesAreImmutable="ProfileVariablesAreImmutable",e.ReservedKeyword="ReservedKeyword",e.TooManyArguments="TooManyArguments",e.UnexpectedEmptyFunction="UnexpectedEmptyFunction",e.UnexpectedPropertyIdentifier="UnexpectedPropertyIdentifier",e.UnknownPropertyIdentifier="UnknownPropertyIdentifier"}(e||(e={}));const i={[e.AlreadyDefined]:"'${identifier}' is already defined.",[e.ApiConflict]:"'${identifier}' is already defined as an Arcade constant or function.",[e.AssignedNeverUsed]:"'${identifier}' is assigned but never used.",[e.DefinedNeverAssigned]:"'${identifier}' is defined but never assigned.",[e.DefinedNeverUsed]:"'${identifier}' is defined but never used.",[e.EmptyBlockStatement]:"Empty block statement.",[e.ExecutionError]:"Execution Error: '${stack}'",[e.InvalidConstantIdentifier]:"Invalid constant identifier, expecting ${list}.",[e.InvalidPropertyIdentifier]:"Invalid property identifier, expecting ${list}.",[e.NoArgumentExpected]:"Expecting no argument.",[e.NotADictionary]:"'${identifier}' doesn't have properties.",[e.NotDefined]:"'${identifier}' is not defined.",[e.NotEnoughArguments]:"Expecting at least ${min} argument(s).",[e.ProfileVariablesAreImmutable]:"Profile variables cannot be modified.",[e.ProfileVariablesConflict]:"'${identifier}' is already defined as a profile variable.",[e.ReservedKeyword]:"'${identifier}' is a reserved keyword.",[e.TooManyArguments]:"Too many arguments, expecting ${max}.",[e.UnexpectedEmptyFunction]:"Unexpected empty function '${identifier}'.",[e.UnexpectedPropertyIdentifier]:"Unexpected property identifier.",[e.UnknownPropertyIdentifier]:"Unknown property identifier '${identifier}'."};export{e as DiagnosticCodes,i as diagnosticMessages};
