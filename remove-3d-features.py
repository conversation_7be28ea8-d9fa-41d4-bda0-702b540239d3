#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إزالة جميع المميزات ثلاثية الأبعاد وإعادة النسخة الأصلية
"""

import re

def remove_3d_features():
    print("🗑️ إزالة جميع المميزات ثلاثية الأبعاد...")
    
    # قراءة الملف الحالي
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إزالة مكتبات ثلاثية الأبعاد
    content = re.sub(r'    <!-- مكتبات ثلاثية الأبعاد -->.*?<script src="https://unpkg\.com/deck\.gl.*?"></script>', '', content, flags=re.DOTALL)
    
    # إزالة CSS الخاص بالمميزات ثلاثية الأبعاد
    content = re.sub(r'        /\* عناصر التحكم ثلاثية الأبعاد المحسنة \*/.*?(?=        /\*|$)', '', content, flags=re.DOTALL)
    content = re.sub(r'        /\* مميزات ثلاثية الأبعاد \*/.*?(?=        /\*|$)', '', content, flags=re.DOTALL)
    
    # إزالة عناصر HTML الخاصة بالعرض ثلاثي الأبعاد
    content = re.sub(r'    <!-- شريط التحكم العلوي -->.*?    </div>\n\n    <!-- مؤشر الارتفاع -->', '', content, flags=re.DOTALL)
    content = re.sub(r'    <!-- عناصر التحكم ثلاثية الأبعاد -->.*?    <!-- مؤشر الارتفاع -->', '', content, flags=re.DOTALL)
    
    # إزالة مؤشر الارتفاع
    content = re.sub(r'    <!-- مؤشر الارتفاع -->.*?    </div>', '', content, flags=re.DOTALL)
    
    # إزالة JavaScript الخاص بالمميزات ثلاثية الأبعاد
    content = re.sub(r'        // ==================== مميزات ثلاثية الأبعاد ====================.*?(?=        // ====================|$)', '', content, flags=re.DOTALL)
    content = re.sub(r'        // ==================== التحكم في طي وفتح اللوحات ====================.*?(?=</script>)', '', content, flags=re.DOTALL)
    
    # إزالة دوال JavaScript المتعلقة بالعرض ثلاثي الأبعاد
    functions_to_remove = [
        'init3DFeatures', 'setViewMode', 'enable3DView', 'disable3DView',
        'addBuildingsLayer', 'addStreetsLayer', 'addTerrainLayer',
        'toggleBuildings', 'toggleStreets', 'toggleStreetNames',
        'setBuildingHeight', 'setMapLayer', 'setMapTilt', 'setMapRotation',
        'setLayerOpacity', 'toggleLayer', 'updateElevationIndicator',
        'updateLayersVisibility', 'generateBuildingsData', 'generateStreetsData',
        'getBuildingColor', 'getStreetColor', 'getStreetWidth',
        'toggle3DPanel', 'toggleLayersPanel', 'checkScreenSize',
        'togglePlacesLayer', 'toggleMarkersLayer'
    ]
    
    for func in functions_to_remove:
        content = re.sub(rf'        function {func}\(.*?\{{.*?\}}\s*', '', content, flags=re.DOTALL)
        content = re.sub(rf'        async function {func}\(.*?\{{.*?\}}\s*', '', content, flags=re.DOTALL)
    
    # إزالة متغيرات ثلاثية الأبعاد
    content = re.sub(r'        let currentViewMode.*?;', '', content)
    content = re.sub(r'        let buildingsLayer.*?;', '', content)
    content = re.sub(r'        let streetsLayer.*?;', '', content)
    content = re.sub(r'        let terrainLayer.*?;', '', content)
    content = re.sub(r'        let mapboxMap.*?;', '', content)
    
    # إزالة event listeners المتعلقة بالعرض ثلاثي الأبعاد
    content = re.sub(r'        window\.addEventListener\(\'load\', checkScreenSize\);.*?\n', '', content)
    content = re.sub(r'        window\.addEventListener\(\'resize\', checkScreenSize\);.*?\n', '', content)
    
    # إزالة استدعاءات تهيئة المميزات ثلاثية الأبعاد
    content = re.sub(r'            setTimeout\(\(\) => \{.*?init3DFeatures\(\);.*?\}, 2000\);', '', content, flags=re.DOTALL)
    
    # تنظيف الأسطر الفارغة المتتالية
    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
    
    # إزالة أي مراجع متبقية للمميزات ثلاثية الأبعاد
    content = re.sub(r'.*3[dD].*\n', '', content)
    content = re.sub(r'.*ثلاثي.*الأبعاد.*\n', '', content)
    content = re.sub(r'.*building.*layer.*\n', '', content, flags=re.IGNORECASE)
    content = re.sub(r'.*street.*layer.*\n', '', content, flags=re.IGNORECASE)
    
    # حفظ الملف المنظف
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إزالة جميع المميزات ثلاثية الأبعاد بنجاح!")
    print("🔄 تم إعادة النسخة إلى حالتها الأصلية")
    print("🗑️ تم إزالة:")
    print("   - مكتبات ثلاثية الأبعاد")
    print("   - CSS الخاص بالعرض ثلاثي الأبعاد")
    print("   - عناصر HTML للتحكم")
    print("   - JavaScript للمميزات ثلاثية الأبعاد")
    print("   - متغيرات ودوال العرض ثلاثي الأبعاد")

if __name__ == "__main__":
    remove_3d_features()
