#!/usr/bin/env node
/**
 * خادم خارجي بسيط لاختبار الوصول
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;
const HOST = '0.0.0.0';

const server = http.createServer((req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // إعداد CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // الصفحة الرئيسية
    if (req.url === '/') {
        const indexPath = path.join(__dirname, 'templates', 'index.html');
        if (fs.existsSync(indexPath)) {
            const content = fs.readFileSync(indexPath);
            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(content);
        } else {
            res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end('<h1>404 - File not found</h1>');
        }
        return;
    }
    
    // API للحالة
    if (req.url === '/api/status') {
        const status = {
            server: 'Simple External Server',
            status: 'running',
            port: PORT,
            timestamp: new Date().toISOString()
        };
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(status, null, 2));
        return;
    }
    
    // 404 للباقي
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end('<h1>404 - Page not found</h1>');
});

server.listen(PORT, HOST, () => {
    console.log(`🌐 Simple External Server running on http://${HOST}:${PORT}`);
    console.log(`📊 Status: http://localhost:${PORT}/api/status`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});
