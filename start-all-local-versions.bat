@echo off
title Yemen Maps - All Local Versions
color 0A

echo.
echo ========================================
echo    Yemen Maps - All Local Versions
echo    Choose Your Preferred Version
echo    Developer: Mohammed Al-Hashedi
echo ========================================
echo.

echo Stopping any running servers...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM node.exe >nul 2>&1

echo Waiting 3 seconds...
timeout /t 3 >nul

echo.
echo Checking database connection...
python -c "import psycopg2; conn = psycopg2.connect(host='localhost', port=5432, database='yemen_gps', user='yemen', password='admin'); cur = conn.cursor(); cur.execute('SELECT COUNT(*) FROM places'); print('Database OK - Places:', cur.fetchone()[0]); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Database connection failed
    echo Please ensure PostgreSQL is running
    pause
    exit /b 1
)

echo.
echo Starting Local Server...
start "Yemen Maps Local Server" python local-complete-server.py

echo Waiting for server to start...
timeout /t 5 >nul

echo.
echo Starting External HTTPS Server...
start "Yemen Maps External HTTPS" node complete-external-server.js

echo Waiting for external server...
timeout /t 3 >nul

echo.
echo ========================================
echo ALL LOCAL VERSIONS ARE READY!
echo ========================================
echo.
echo Choose your preferred version:
echo.
echo 1. WORKING LOCAL VERSION (RECOMMENDED)
echo    - Local data + Online maps (guaranteed to work)
echo    - Link: http://localhost:5000/index-working-local.html
echo.
echo 2. PREMIUM LOCAL VERSION
echo    - Complete copy of online version
echo    - Link: http://localhost:5000/index-premium-local.html
echo.
echo 3. CUSTOM LOCAL VERSION
echo    - Custom built for local use
echo    - Link: http://localhost:5000/index-local.html
echo.
echo 4. SIMPLE LOCAL VERSION
echo    - Basic local map
echo    - Link: http://localhost:5000/local-map.html
echo.
echo 5. ORIGINAL VERSION
echo    - Standard online version
echo    - Link: http://localhost:5000
echo.
echo 6. ADMIN PANEL
echo    - Management interface
echo    - Link: http://localhost:5000/admin
echo.
echo External Access:
echo    - Secure Domain: https://yemenmaps.com:8443
echo.
echo ========================================
echo.

:menu
echo Please choose a version to open:
echo [1] Working Local (Recommended)
echo [2] Premium Local
echo [3] Custom Local
echo [4] Simple Local
echo [5] Original
echo [6] Admin Panel
echo [7] Open All Versions
echo [0] Exit
echo.
set /p choice="Enter your choice (0-7): "

if "%choice%"=="1" (
    echo Opening Working Local Version...
    start http://localhost:5000/index-working-local.html
    goto menu
)
if "%choice%"=="2" (
    echo Opening Premium Local Version...
    start http://localhost:5000/index-premium-local.html
    goto menu
)
if "%choice%"=="3" (
    echo Opening Custom Local Version...
    start http://localhost:5000/index-local.html
    goto menu
)
if "%choice%"=="4" (
    echo Opening Simple Local Version...
    start http://localhost:5000/local-map.html
    goto menu
)
if "%choice%"=="5" (
    echo Opening Original Version...
    start http://localhost:5000
    goto menu
)
if "%choice%"=="6" (
    echo Opening Admin Panel...
    start http://localhost:5000/admin
    goto menu
)
if "%choice%"=="7" (
    echo Opening All Versions...
    start http://localhost:5000/index-working-local.html
    timeout /t 2 >nul
    start http://localhost:5000/index-premium-local.html
    timeout /t 2 >nul
    start http://localhost:5000/admin
    goto menu
)
if "%choice%"=="0" (
    echo Goodbye!
    exit /b 0
)

echo Invalid choice. Please try again.
goto menu
