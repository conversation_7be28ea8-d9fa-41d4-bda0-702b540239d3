@echo off
title Yemen Maps - Final Premium System
color 0A

echo.
echo ========================================
echo    Yemen Maps - Final Premium System
echo    Premium Local Version is Now Default
echo    Developer: Mohammed Al-Hashedi
echo ========================================
echo.

echo Stopping any running servers...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM node.exe >nul 2>&1

echo Waiting 3 seconds...
timeout /t 3 >nul

echo.
echo Checking database connection...
python -c "import psycopg2; conn = psycopg2.connect(host='localhost', port=5432, database='yemen_gps', user='yemen', password='admin'); cur = conn.cursor(); cur.execute('SELECT COUNT(*) FROM places'); print('Database OK - Places:', cur.fetchone()[0]); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Database connection failed
    echo Please ensure PostgreSQL is running
    pause
    exit /b 1
)

echo.
echo Starting Premium Local Server...
echo Default Page: http://localhost:5000 (Premium Version)
echo Admin Panel: http://localhost:5000/admin
echo API Status: http://localhost:5000/api/status
echo.

start "Yemen Maps Premium Local Server" python local-complete-server.py

echo Waiting for local server to start...
timeout /t 5 >nul

echo.
echo Starting External HTTPS Server...
echo External Domain: https://yemenmaps.com:8443 (Same Premium Version)
echo.

start "Yemen Maps External HTTPS Server" node complete-external-server.js

echo Waiting for external server...
timeout /t 5 >nul

echo.
echo ========================================
echo PREMIUM SYSTEM IS NOW READY!
echo ========================================
echo.
echo DEFAULT ACCESS (Premium Version):
echo   Local: http://localhost:5000
echo   External: https://yemenmaps.com:8443
echo.
echo ADMIN PANEL:
echo   Local: http://localhost:5000/admin
echo   External: https://yemenmaps.com:8443/admin
echo.
echo ALTERNATIVE VERSIONS:
echo   Working Local: http://localhost:5000/index-working-local.html
echo   Custom Local: http://localhost:5000/index-local.html
echo   Simple Local: http://localhost:5000/local-map.html
echo.
echo PREMIUM FEATURES (Now Default):
echo   - Same quality as online version
echo   - 7445+ locations with full details
echo   - Advanced search and filtering
echo   - Marker clustering for performance
echo   - Real-time statistics
echo   - Professional UI/UX
echo   - Enhanced location permissions
echo   - Local tiles with online fallback
echo   - Arabic interface
echo   - Responsive design
echo   - Works on both local and external domains
echo.
echo Opening Premium Default Version...
timeout /t 3 >nul
start http://localhost:5000

echo.
echo System is running with Premium Version as Default...
echo Both local and external servers use the same premium version
echo Do not close this window
echo.
pause
