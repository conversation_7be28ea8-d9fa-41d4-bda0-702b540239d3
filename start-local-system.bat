@echo off
title Yemen Maps Complete Local System
color 0A

echo.
echo ========================================
echo    Yemen Maps Complete Local System
echo    Developer: Mohammed Al-Hashedi
echo ========================================
echo.

echo Stopping any running servers...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM node.exe >nul 2>&1

echo Waiting 3 seconds...
timeout /t 3 >nul

echo.
echo Starting Local Server (Python Flask)...
echo Local Server: http://localhost:5000
echo Local Map: http://localhost:5000/local-map.html
echo Admin Panel: http://localhost:5000/admin
echo API Places: http://localhost:5000/api/places
echo.

start "Yemen Maps Local Server" python local-complete-server.py

echo Waiting for local server to start...
timeout /t 5 >nul

echo.
echo Starting External HTTPS Server (Node.js)...
echo External Domain: https://yemenmaps.com:8443
echo.

start "Yemen Maps External HTTPS" node complete-external-server.js

echo Waiting for external server to start...
timeout /t 5 >nul

echo.
echo ========================================
echo System Ready!
echo ========================================
echo Local Access:
echo   Complete Map: http://localhost:5000
echo   Local Enhanced Map: http://localhost:5000/local-map.html
echo   Admin Panel: http://localhost:5000/admin
echo.
echo External Access:
echo   Secure Domain: https://yemenmaps.com:8443
echo.
echo Features Available:
echo   - 7445+ locations in database
echo   - Advanced search functionality
echo   - Category filtering
echo   - Local offline maps
echo   - Arabic interface
echo   - Secure HTTPS connection
echo.
echo To stop: Press Ctrl+C in the server windows
echo ========================================
echo.

echo Opening local enhanced map...
timeout /t 3 >nul
start http://localhost:5000/local-map.html

echo.
echo System is now running...
echo Do not close this window
echo.
pause
