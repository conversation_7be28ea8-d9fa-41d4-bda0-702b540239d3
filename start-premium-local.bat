@echo off
title Yemen Maps Premium Local Edition
color 0A

echo.
echo ========================================
echo    Yemen Maps Premium Local Edition
echo    High-Quality Offline Version
echo    Developer: Mohammed Al-Hashedi
echo ========================================
echo.

echo Stopping any running servers...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM node.exe >nul 2>&1

echo Waiting 3 seconds...
timeout /t 3 >nul

echo.
echo Checking database connection...
python -c "import psycopg2; conn = psycopg2.connect(host='localhost', port=5432, database='yemen_gps', user='yemen', password='admin'); cur = conn.cursor(); cur.execute('SELECT COUNT(*) FROM places'); print('Database connected - Places:', cur.fetchone()[0]); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Database connection failed
    echo Please ensure PostgreSQL is running
    pause
    exit /b 1
)

echo.
echo Starting Premium Local Server...
echo Premium Local Map: http://localhost:5000/index-local.html
echo Original Local Map: http://localhost:5000/local-map.html
echo Standard Map: http://localhost:5000
echo Admin Panel: http://localhost:5000/admin
echo API Status: http://localhost:5000/api/status
echo.

start "Yemen Maps Premium Local" python local-complete-server.py

echo Waiting for server to start...
timeout /t 5 >nul

echo.
echo Starting External HTTPS Server...
echo External Domain: https://yemenmaps.com:8443
echo.

start "Yemen Maps External HTTPS" node complete-external-server.js

echo Waiting for external server...
timeout /t 5 >nul

echo.
echo ========================================
echo PREMIUM LOCAL SYSTEM READY!
echo ========================================
echo.
echo Local Access (Premium Quality):
echo   Premium Local Map: http://localhost:5000/index-local.html
echo   Enhanced Local Map: http://localhost:5000/local-map.html
echo   Standard Map: http://localhost:5000
echo   Admin Panel: http://localhost:5000/admin
echo.
echo External Access:
echo   Secure Domain: https://yemenmaps.com:8443
echo.
echo Premium Features:
echo   - Same quality as online version
echo   - 7445+ locations with full details
echo   - Advanced search and filtering
echo   - Marker clustering for performance
echo   - Real-time statistics
echo   - Professional UI/UX
echo   - Offline tile support
echo   - Arabic interface
echo   - Responsive design
echo.
echo Opening Premium Local Map...
timeout /t 3 >nul
start http://localhost:5000/index-local.html

echo.
echo System is running...
echo Do not close this window
echo.
pause
