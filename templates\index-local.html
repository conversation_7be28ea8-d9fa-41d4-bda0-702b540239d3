<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 خرائط اليمن - النسخة المحلية</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" href="/favicon.ico">

    <!-- Core CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- خطوط محلية -->
    <link rel="stylesheet" href="/css/local-fonts.css">

    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --danger-color: #ea4335;
            --warning-color: #fbbc04;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --route-color: #9c27b0;
            --route-color-light: #ba68c8;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
            box-sizing: border-box;
        }

        body {
            margin: 0; padding: 0;
            background: var(--light-color);
            overflow: hidden;
            font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
        }

        #map {
            height: 100vh;
            width: 100%;
            position: relative;
            /* تحسينات عالية الدقة */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            /* تحسين الأداء */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* تحسين جودة العرض */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* تحسينات للطبقات عالية الدقة */
        .leaflet-tile {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            /* تحسين جودة الصورة */
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            /* تحسين التحميل */
            transition: opacity 0.2s ease-in-out;
        }

        /* تحسين مؤشر الإحداثيات */
        .coords-control {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.1) !important;
            font-weight: 500;
            color: #333;
            font-family: 'Courier New', monospace;
        }

        /* تحسين مقياس المسافة */
        .leaflet-control-scale {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
            border-radius: 5px !important;
            padding: 2px 5px !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            border: 1px solid rgba(0,0,0,0.1) !important;
        }

        /* تحسين أزرار التحكم */
        .leaflet-control-zoom a {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0,0,0,0.1) !important;
            color: #333 !important;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .leaflet-control-zoom a:hover {
            background: rgba(255, 255, 255, 1) !important;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* تحسين العلامات */
        .leaflet-marker-icon {
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        /* تحسين النوافذ المنبثقة */
        .leaflet-popup-content-wrapper {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        /* Advanced Sidebar */
        .sidebar {
            position: fixed; top: 0; right: 0;
            width: 380px; height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000; overflow-y: auto;
            transform: translateX(100%);
            transition: var(--transition);
            border-left: 1px solid #e8eaed;
        }
        .sidebar.open { transform: translateX(0); }

        .sidebar::-webkit-scrollbar { width: 6px; }
        .sidebar::-webkit-scrollbar-track { background: #f1f3f4; }
        .sidebar::-webkit-scrollbar-thumb {
            background: #dadce0; border-radius: 3px;
        }

        /* Enhanced Header */
        .sidebar-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4285f4 100%);
            color: white; padding: 25px 20px; text-align: center;
            position: relative; overflow: hidden;
        }

        .sidebar-header::before {
            content: ''; position: absolute; top: 0; left: 0;
            width: 100%; height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .sidebar-header h4 {
            margin: 0; font-size: 1.4rem; font-weight: 700;
            position: relative; z-index: 1;
        }

        .sidebar-header p {
            margin: 5px 0 0; opacity: 0.9; font-size: 0.9rem;
            position: relative; z-index: 1;
        }

        /* Advanced Search */
        .search-container {
            padding: 20px; background: #f8f9fa;
            border-bottom: 1px solid #e8eaed;
        }

        .search-box {
            position: relative; margin-bottom: 15px;
        }

        .search-box input {
            width: 100%; padding: 14px 45px 14px 15px;
            border: 2px solid #e8eaed; border-radius: var(--border-radius);
            font-size: 14px; transition: var(--transition);
            background: white;
        }

        .search-box input:focus {
            outline: none; border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }

        .search-box .search-icon {
            position: absolute; right: 15px; top: 50%;
            transform: translateY(-50%); color: #5f6368;
            pointer-events: none;
        }

        .search-box .clear-search {
            position: absolute; left: 15px; top: 50%;
            transform: translateY(-50%); color: #5f6368;
            cursor: pointer; display: none;
        }

        /* Advanced Filters */
        .filters-section {
            margin-top: 15px;
        }

        .filter-group {
            margin-bottom: 15px;
        }

        .filter-label {
            display: block; font-size: 12px; font-weight: 600;
            color: #5f6368; margin-bottom: 8px;
            text-transform: uppercase; letter-spacing: 0.5px;
        }

        .category-filters { margin: 0 20px 20px; }
        .category-btn {
            display: inline-block; padding: 8px 15px; margin: 5px;
            background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 20px;
            color: #495057; text-decoration: none; font-size: 12px;
            transition: all 0.3s; cursor: pointer;
        }
        .category-btn:hover, .category-btn.active {
            background: #667eea; color: white; border-color: #667eea;
        }

        .place-card {
            background: white; border-radius: 10px; padding: 15px; margin: 0 20px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); cursor: pointer;
            transition: transform 0.3s;
        }
        .place-card:hover { transform: translateY(-2px); }

        .place-image {
            width: 100%; height: 120px; object-fit: cover;
            border-radius: 8px; margin-bottom: 10px;
        }
        .place-name { font-weight: 600; color: #2c3e50; margin-bottom: 5px; }
        .place-category { color: #6c757d; font-size: 12px; margin-bottom: 5px; }
        .place-rating { color: #ffc107; }

        .toggle-sidebar {
            position: fixed; top: 20px; right: 20px; z-index: 1001;
            background: white; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s;
        }
        .toggle-sidebar:hover { transform: scale(1.1); }

        .loading { text-align: center; padding: 20px; color: #6c757d; }

        .stats-bar {
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(255,255,255,0.95); padding: 10px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 999;
            display: flex; justify-content: space-around; text-align: center;
        }
        .stat-item { flex: 1; }
        .stat-number { font-weight: 700; font-size: 18px; color: #667eea; }
        .stat-label { font-size: 12px; color: #6c757d; }

        .popup-content { text-align: center; min-width: 200px; }
        .popup-image {
            width: 100%; height: 100px; object-fit: cover;
            border-radius: 5px; margin-bottom: 10px;
        }
        .popup-name { font-weight: 600; margin-bottom: 5px; }
        .popup-category { color: #6c757d; font-size: 12px; margin-bottom: 5px; }
        .popup-rating { color: #ffc107; margin-bottom: 10px; }
        .popup-btn {
            background: #667eea; color: white; border: none;
            padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;
        }

        .load-more-btn:hover {
            background: #1557b0 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(26,115,232,0.4) !important;
        }

        /* أيقونة تحديد الموقع */
        .location-btn {
            position: fixed; bottom: 100px; right: 20px; z-index: 1001;
            background: var(--secondary-color); border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .location-btn:hover {
            transform: scale(1.1);
            background: #2e7d32;
        }

        /* شريط الحالة المحلي */
        .local-status {
            position: fixed; top: 0; left: 0; right: 0;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white; padding: 8px 20px; text-align: center;
            font-size: 14px; font-weight: 600; z-index: 1002;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .local-status .status-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* تحسين الاستجابة للهواتف */
        @media (max-width: 768px) {
            .sidebar { width: 100%; }
            .stats-bar { padding: 8px 10px; }
            .stat-number { font-size: 16px; }
            .stat-label { font-size: 11px; }
        }
    </style>
</head>
<body>
    <!-- شريط الحالة المحلي -->
    <div class="local-status">
        <span class="status-icon">🔒</span>
        النسخة المحلية - يعمل بدون إنترنت |
        <span id="places-count">جاري التحميل...</span> موقع متاح |
        <span id="server-status">متصل</span>
    </div>

    <!-- الخريطة الرئيسية -->
    <div id="map"></div>

    <!-- زر فتح/إغلاق الشريط الجانبي -->
    <button class="toggle-sidebar" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- زر تحديد الموقع -->
    <button class="location-btn" onclick="getCurrentLocation()" title="تحديد موقعي">
        <i class="fas fa-location-arrow"></i>
    </button>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <!-- رأس الشريط الجانبي -->
        <div class="sidebar-header">
            <h4>🇾🇪 خرائط اليمن</h4>
            <p>النسخة المحلية المحسنة</p>
        </div>

        <!-- منطقة البحث -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="ابحث عن مكان أو خدمة...">
                <i class="fas fa-search search-icon"></i>
                <i class="fas fa-times clear-search" onclick="clearSearch()"></i>
            </div>

            <!-- مرشحات الفئات -->
            <div class="filters-section">
                <div class="filter-group">
                    <label class="filter-label">الفئات</label>
                    <div id="categoryFilters" class="category-filters">
                        <span class="category-btn active" data-category="">الكل</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الأماكن -->
        <div id="placesList" class="places-list">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                جاري تحميل الأماكن...
            </div>
        </div>
    </div>

    <!-- شريط الإحصائيات -->
    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="totalPlaces">0</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="visibleMarkers">0</div>
            <div class="stat-label">ظاهر على الخريطة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="searchResults">0</div>
            <div class="stat-label">نتائج البحث</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="loadTime">0</div>
            <div class="stat-label">وقت التحميل (ms)</div>
        </div>
    </div>

    <!-- مكتبات JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغيرات عامة
        let map;
        let markersCluster;
        let allPlaces = [];
        let filteredPlaces = [];
        let categories = [];
        let currentSearchTerm = '';
        let currentCategory = '';
        let userLocation = null;

        // إحصائيات
        let stats = {
            totalPlaces: 0,
            visibleMarkers: 0,
            searchResults: 0,
            loadTime: 0
        };

        // تهيئة الخريطة
        function initMap() {
            console.log('🗺️ تهيئة الخريطة المحلية...');

            // إنشاء الخريطة مع التركيز على اليمن
            map = L.map('map', {
                center: [15.3694, 44.1910],
                zoom: 7,
                zoomControl: true,
                attributionControl: true
            });

            // إضافة طبقة البلاطات المحلية مع عدة مصادر احتياطية
            const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
                minZoom: 6,
                subdomains: ['a', 'b', 'c']
            });

            // محاولة استخدام البلاطات المحلية أولاً
            const localTileLayer = L.tileLayer('/tiles/yemen/{z}/{x}/{y}.png', {
                attribution: '© خرائط اليمن المحلية',
                maxZoom: 16,
                minZoom: 6,
                errorTileUrl: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
            });

            // إضافة الطبقة المحلية مع احتياطي أونلاين
            localTileLayer.addTo(map);

            // إضافة طبقة احتياطية في حالة فشل المحلية
            localTileLayer.on('tileerror', function(e) {
                console.log('فشل في تحميل البلاطة المحلية، التبديل للأونلاين');
                map.removeLayer(localTileLayer);
                tileLayer.addTo(map);
            });

            // إضافة مجموعة العلامات مع التجميع
            markersCluster = L.markerClusterGroup({
                chunkedLoading: true,
                maxClusterRadius: 50,
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: false,
                zoomToBoundsOnClick: true
            });
            map.addLayer(markersCluster);

            // إضافة عناصر التحكم
            addMapControls();

            // تحميل البيانات
            loadData();
        }

        // إضافة عناصر التحكم للخريطة
        function addMapControls() {
            // مقياس المسافة
            L.control.scale({
                position: 'bottomleft',
                metric: true,
                imperial: false
            }).addTo(map);

            // مؤشر الإحداثيات
            const coordsControl = L.control({position: 'bottomright'});
            coordsControl.onAdd = function() {
                const div = L.DomUtil.create('div', 'coords-control');
                div.innerHTML = 'انقر على الخريطة لعرض الإحداثيات';
                return div;
            };
            coordsControl.addTo(map);

            // تحديث الإحداثيات عند النقر
            map.on('click', function(e) {
                const lat = e.latlng.lat.toFixed(6);
                const lng = e.latlng.lng.toFixed(6);
                document.querySelector('.coords-control').innerHTML =
                    `خط العرض: ${lat}<br>خط الطول: ${lng}`;
            });
        }

        // تحميل البيانات
        async function loadData() {
            const startTime = Date.now();

            try {
                // تحميل الأماكن
                await loadPlaces();

                // تحميل الفئات
                await loadCategories();

                // حساب وقت التحميل
                stats.loadTime = Date.now() - startTime;
                updateStats();

                console.log('✅ تم تحميل البيانات بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
                showError('فشل في تحميل البيانات');
            }
        }

        // تحميل الأماكن
        async function loadPlaces() {
            try {
                const response = await fetch('/api/places');
                const data = await response.json();

                if (data.success) {
                    allPlaces = data.places;
                    filteredPlaces = [...allPlaces];
                    stats.totalPlaces = allPlaces.length;

                    displayPlaces(filteredPlaces);
                    updatePlacesList(filteredPlaces);
                    updateStats();

                    document.getElementById('places-count').textContent =
                        stats.totalPlaces.toLocaleString('ar');

                    console.log(`📍 تم تحميل ${stats.totalPlaces} مكان`);
                } else {
                    throw new Error(data.error || 'فشل في تحميل الأماكن');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل الأماكن:', error);
                throw error;
            }
        }

        // تحميل الفئات
        async function loadCategories() {
            try {
                const response = await fetch('/api/places/categories');
                const data = await response.json();

                if (data.success) {
                    categories = data.categories;
                    displayCategories();
                    console.log(`📂 تم تحميل ${categories.length} فئة`);
                } else {
                    console.warn('تحذير: فشل في تحميل الفئات');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل الفئات:', error);
            }
        }

        // عرض الأماكن على الخريطة
        function displayPlaces(places) {
            markersCluster.clearLayers();
            stats.visibleMarkers = 0;

            places.forEach(place => {
                if (place.lat && place.lng) {
                    const marker = createMarker(place);
                    markersCluster.addLayer(marker);
                    stats.visibleMarkers++;
                }
            });

            updateStats();
        }

        // إنشاء علامة للمكان
        function createMarker(place) {
            const marker = L.marker([place.lat, place.lng]);

            // محتوى النافذة المنبثقة
            const popupContent = createPopupContent(place);
            marker.bindPopup(popupContent);

            return marker;
        }

        // إنشاء محتوى النافذة المنبثقة
        function createPopupContent(place) {
            return `
                <div class="popup-content">
                    <div class="popup-name">${place.name}</div>
                    ${place.category ? `<div class="popup-category">${place.category}</div>` : ''}
                    ${place.address ? `<div style="margin: 5px 0;"><i class="fas fa-map-marker-alt"></i> ${place.address}</div>` : ''}
                    ${place.phone ? `<div style="margin: 5px 0;"><i class="fas fa-phone"></i> ${place.phone}</div>` : ''}
                    ${place.rating ? `<div class="popup-rating"><i class="fas fa-star"></i> ${place.rating} (${place.review_count || 0} تقييم)</div>` : ''}
                    <button class="popup-btn" onclick="showDirections(${place.lat}, ${place.lng}, '${place.name}')">
                        <i class="fas fa-directions"></i> الاتجاهات
                    </button>
                </div>
            `;
        }

        // عرض الفئات
        function displayCategories() {
            const container = document.getElementById('categoryFilters');

            categories.forEach(category => {
                const btn = document.createElement('span');
                btn.className = 'category-btn';
                btn.textContent = `${category.name} (${category.count})`;
                btn.dataset.category = category.name;
                btn.onclick = () => filterByCategory(category.name);
                container.appendChild(btn);
            });
        }

        // تحديث قائمة الأماكن
        function updatePlacesList(places) {
            const container = document.getElementById('placesList');

            if (places.length === 0) {
                container.innerHTML = '<div class="loading">لا توجد أماكن متطابقة</div>';
                return;
            }

            container.innerHTML = '';

            places.slice(0, 50).forEach(place => {
                const card = createPlaceCard(place);
                container.appendChild(card);
            });

            if (places.length > 50) {
                const loadMoreBtn = document.createElement('button');
                loadMoreBtn.className = 'btn btn-primary load-more-btn';
                loadMoreBtn.style.cssText = 'width: 90%; margin: 10px 5%; padding: 10px;';
                loadMoreBtn.innerHTML = `<i class="fas fa-plus"></i> عرض المزيد (${places.length - 50} متبقي)`;
                loadMoreBtn.onclick = () => showAllPlaces(places);
                container.appendChild(loadMoreBtn);
            }
        }

        // إنشاء بطاقة المكان
        function createPlaceCard(place) {
            const card = document.createElement('div');
            card.className = 'place-card';
            card.innerHTML = `
                <div class="place-name">${place.name}</div>
                ${place.category ? `<div class="place-category">${place.category}</div>` : ''}
                ${place.address ? `<div style="font-size: 12px; color: #666; margin: 5px 0;"><i class="fas fa-map-marker-alt"></i> ${place.address}</div>` : ''}
                ${place.rating ? `<div class="place-rating"><i class="fas fa-star"></i> ${place.rating}</div>` : ''}
            `;

            card.onclick = () => {
                map.setView([place.lat, place.lng], 15);
                toggleSidebar(false);
            };

            return card;
        }

        // البحث في الأماكن
        async function searchPlaces(query) {
            if (!query.trim()) {
                filteredPlaces = [...allPlaces];
                stats.searchResults = 0;
            } else {
                try {
                    const response = await fetch(`/api/places/search?q=${encodeURIComponent(query)}`);
                    const data = await response.json();

                    if (data.success) {
                        filteredPlaces = data.results;
                        stats.searchResults = filteredPlaces.length;
                    }
                } catch (error) {
                    console.error('خطأ في البحث:', error);
                    filteredPlaces = allPlaces.filter(place =>
                        place.name.toLowerCase().includes(query.toLowerCase()) ||
                        (place.address && place.address.toLowerCase().includes(query.toLowerCase())) ||
                        (place.category && place.category.toLowerCase().includes(query.toLowerCase()))
                    );
                    stats.searchResults = filteredPlaces.length;
                }
            }

            displayPlaces(filteredPlaces);
            updatePlacesList(filteredPlaces);
            updateStats();
        }

        // تصفية حسب الفئة
        async function filterByCategory(category) {
            currentCategory = category;

            // تحديث أزرار الفئات
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.category === category);
            });

            if (!category) {
                filteredPlaces = [...allPlaces];
            } else {
                try {
                    const response = await fetch(`/api/places/by-category/${encodeURIComponent(category)}`);
                    const data = await response.json();

                    if (data.success) {
                        filteredPlaces = data.places;
                    }
                } catch (error) {
                    console.error('خطأ في التصفية:', error);
                    filteredPlaces = allPlaces.filter(place => place.category === category);
                }
            }

            displayPlaces(filteredPlaces);
            updatePlacesList(filteredPlaces);
            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalPlaces').textContent = stats.totalPlaces.toLocaleString('ar');
            document.getElementById('visibleMarkers').textContent = stats.visibleMarkers.toLocaleString('ar');
            document.getElementById('searchResults').textContent = stats.searchResults.toLocaleString('ar');
            document.getElementById('loadTime').textContent = stats.loadTime.toLocaleString('ar');
        }

        // فتح/إغلاق الشريط الجانبي
        function toggleSidebar(force = null) {
            const sidebar = document.getElementById('sidebar');
            if (force !== null) {
                sidebar.classList.toggle('open', force);
            } else {
                sidebar.classList.toggle('open');
            }
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            searchPlaces('');
            document.querySelector('.clear-search').style.display = 'none';
        }

        // تحديد الموقع الحالي
        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    position => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;

                        userLocation = [lat, lng];
                        map.setView([lat, lng], 15);

                        // إضافة علامة للموقع الحالي
                        if (window.userLocationMarker) {
                            map.removeLayer(window.userLocationMarker);
                        }

                        window.userLocationMarker = L.marker([lat, lng], {
                            icon: L.divIcon({
                                className: 'user-location-marker',
                                html: '<i class="fas fa-location-arrow" style="color: #007bff; font-size: 20px;"></i>',
                                iconSize: [20, 20]
                            })
                        }).addTo(map);

                        showNotification('تم تحديد موقعك بنجاح', 'success');
                    },
                    error => {
                        console.error('خطأ في تحديد الموقع:', error);
                        showNotification('فشل في تحديد الموقع', 'error');
                    }
                );
            } else {
                showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
            }
        }

        // عرض الاتجاهات
        function showDirections(lat, lng, name) {
            if (userLocation) {
                // يمكن إضافة نظام توجيه محلي هنا
                showNotification(`الاتجاهات إلى ${name} - قيد التطوير`, 'info');
            } else {
                showNotification('يرجى تحديد موقعك أولاً', 'warning');
            }
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // يمكن إضافة نظام إشعارات محسن هنا
            console.log(`${type.toUpperCase()}: ${message}`);
        }

        // عرض خطأ
        function showError(message) {
            document.getElementById('placesList').innerHTML =
                `<div class="loading" style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
        }

        // الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة الخريطة
            initMap();

            // البحث
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                const query = this.value;

                // إظهار/إخفاء زر المسح
                document.querySelector('.clear-search').style.display =
                    query ? 'block' : 'none';

                // البحث مع تأخير
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchPlaces(query);
                }, 300);
            });

            // فحص حالة الخادم
            checkServerStatus();
            setInterval(checkServerStatus, 30000); // كل 30 ثانية
        });

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                document.getElementById('server-status').textContent = 'متصل';
                document.getElementById('server-status').style.color = '#27ae60';

            } catch (error) {
                document.getElementById('server-status').textContent = 'غير متصل';
                document.getElementById('server-status').style.color = '#e74c3c';
            }
        }
    </script>
</body>
</html>
