<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 خرائط اليمن - النسخة المحلية المتميزة</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" href="/favicon.ico">

    <!-- Core CSS Libraries -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- خطوط محلية - بديل عن Google Fonts -->
    <link rel="stylesheet" href="/css/local-fonts.css">

    <!-- تحسينات التوجيه والمسارات -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/routing-improvements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/smart-routing.css') }}">

    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --danger-color: #ea4335;
            --warning-color: #fbbc04;
            --dark-color: #202124;
            --light-color: #f8f9fa;
            --route-color: #9c27b0;
            --route-color-light: #ba68c8;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
            box-sizing: border-box;
        }

        body {
            margin: 0; padding: 0;
            background: var(--light-color);
            overflow: hidden;
            font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
        }

        #map {
            height: 100vh;
            width: 100%;
            position: relative;
            /* تحسينات عالية الدقة */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            /* تحسين الأداء */
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
            will-change: transform;
            /* تحسين جودة العرض */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* تحسينات للطبقات عالية الدقة */
        .leaflet-tile {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            /* تحسين جودة الصورة */
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            /* تحسين التحميل */
            transition: opacity 0.2s ease-in-out;
        }

        /* تحسين مؤشر الإحداثيات */
        .coords-control {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.1) !important;
            font-weight: 500;
            color: #333;
            font-family: 'Courier New', monospace;
        }

        /* تحسين مقياس المسافة */
        .leaflet-control-scale {
            background: rgba(255, 255, 255, 0.9) !important;
            backdrop-filter: blur(10px);
            border-radius: 5px !important;
            padding: 2px 5px !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
            border: 1px solid rgba(0,0,0,0.1) !important;
        }

        /* تحسين أزرار التحكم */
        .leaflet-control-zoom a {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0,0,0,0.1) !important;
            color: #333 !important;
            font-weight: bold;
            transition: all 0.2s ease;
        }

        .leaflet-control-zoom a:hover {
            background: rgba(255, 255, 255, 1) !important;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* تحسين العلامات */
        .leaflet-marker-icon {
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        /* تحسين النوافذ المنبثقة */
        .leaflet-popup-content-wrapper {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        /* Advanced Sidebar */
        .sidebar {
            position: fixed; top: 0; right: 0;
            width: 380px; height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000; overflow-y: auto;
            transform: translateX(100%);
            transition: var(--transition);
            border-left: 1px solid #e8eaed;
        }
        .sidebar.open { transform: translateX(0); }

        .sidebar::-webkit-scrollbar { width: 6px; }
        .sidebar::-webkit-scrollbar-track { background: #f1f3f4; }
        .sidebar::-webkit-scrollbar-thumb {
            background: #dadce0; border-radius: 3px;
        }

        /* Enhanced Header */
        .sidebar-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4285f4 100%);
            color: white; padding: 25px 20px; text-align: center;
            position: relative; overflow: hidden;
        }

        .sidebar-header::before {
            content: ''; position: absolute; top: 0; left: 0;
            width: 100%; height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .sidebar-header h4 {
            margin: 0; font-size: 1.4rem; font-weight: 700;
            position: relative; z-index: 1;
        }

        .sidebar-header p {
            margin: 5px 0 0; opacity: 0.9; font-size: 0.9rem;
            position: relative; z-index: 1;
        }

        /* Advanced Search */
        .search-container {
            padding: 20px; background: #f8f9fa;
            border-bottom: 1px solid #e8eaed;
        }

        .search-box {
            position: relative; margin-bottom: 15px;
        }

        .search-box input {
            width: 100%; padding: 14px 45px 14px 15px;
            border: 2px solid #e8eaed; border-radius: var(--border-radius);
            font-size: 14px; transition: var(--transition);
            background: white;
        }

        .search-box input:focus {
            outline: none; border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }

        .search-box .search-icon {
            position: absolute; right: 15px; top: 50%;
            transform: translateY(-50%); color: #5f6368;
            pointer-events: none;
        }

        .search-box .clear-search {
            position: absolute; left: 15px; top: 50%;
            transform: translateY(-50%); color: #5f6368;
            cursor: pointer; display: none;
        }

        /* Advanced Filters */
        .filters-section {
            margin-top: 15px;
        }

        .filter-group {
            margin-bottom: 15px;
        }

        .filter-label {
            display: block; font-size: 12px; font-weight: 600;
            color: #5f6368; margin-bottom: 8px;
            text-transform: uppercase; letter-spacing: 0.5px;
        }

        /* تم حذف CSS المكرر - استخدام التعريف الأول فقط */

        .category-filters { margin: 0 20px 20px; }
        .category-btn {
            display: inline-block; padding: 8px 15px; margin: 5px;
            background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 20px;
            color: #495057; text-decoration: none; font-size: 12px;
            transition: all 0.3s; cursor: pointer;
        }
        .category-btn:hover, .category-btn.active {
            background: #667eea; color: white; border-color: #667eea;
        }

        .place-card {
            background: white; border-radius: 10px; padding: 15px; margin: 0 20px 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); cursor: pointer;
            transition: transform 0.3s;
        }
        .place-card:hover { transform: translateY(-2px); }

        .place-image {
            width: 100%; height: 120px; object-fit: cover;
            border-radius: 8px; margin-bottom: 10px;
        }
        .place-name { font-weight: 600; color: #2c3e50; margin-bottom: 5px; }
        .place-category { color: #6c757d; font-size: 12px; margin-bottom: 5px; }
        .place-rating { color: #ffc107; }

        .toggle-sidebar {
            position: fixed; top: 20px; right: 20px; z-index: 1001;
            background: white; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s;
        }
        .toggle-sidebar:hover { transform: scale(1.1); }

        .loading { text-align: center; padding: 20px; color: #6c757d; }

        .stats-bar {
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(255,255,255,0.95); padding: 10px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 999;
            display: flex; justify-content: space-around; text-align: center;
        }
        .stat-item { flex: 1; }
        .stat-number { font-weight: 700; font-size: 18px; color: #667eea; }
        .stat-label { font-size: 12px; color: #6c757d; }

        .popup-content { text-align: center; min-width: 200px; }
        .popup-image {
            width: 100%; height: 100px; object-fit: cover;
            border-radius: 5px; margin-bottom: 10px;
        }
        .popup-name { font-weight: 600; margin-bottom: 5px; }
        .popup-category { color: #6c757d; font-size: 12px; margin-bottom: 5px; }
        .popup-rating { color: #ffc107; margin-bottom: 10px; }
        .popup-btn {
            background: #667eea; color: white; border: none;
            padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;
        }

        .load-more-btn:hover {
            background: #1557b0 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(26,115,232,0.4) !important;
        }

        /* أيقونة تحديد الموقع */
        .location-btn {
            position: fixed; bottom: 100px; right: 20px; z-index: 1001;
            background: var(--secondary-color); border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .location-btn:hover {
            transform: scale(1.1);
            background: #2d8f47;
        }
        .location-btn i { font-size: 18px; }

        /* زر إلغاء المسار */
        .clear-route-btn {
            position: fixed; bottom: 100px; right: 80px; z-index: 1001;
            background: #ea4335; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .clear-route-btn:hover {
            transform: scale(1.1);
            background: #d33b2c;
        }
        .clear-route-btn i { font-size: 18px; }

        /* زر التحكم الصوتي */
        .voice-control-btn {
            position: fixed; bottom: 160px; right: 20px; z-index: 1001;
            background: #ff9800; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .voice-control-btn:hover {
            transform: scale(1.1);
            background: #f57c00;
        }
        .voice-control-btn.active {
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        .voice-control-btn.muted {
            background: #9e9e9e;
        }
        .voice-control-btn i { font-size: 18px; }

        /* زر اختبار التنبيهات الصوتية */
        .test-voice-btn {
            position: fixed; bottom: 220px; right: 20px; z-index: 1001;
            background: #2196f3; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .test-voice-btn:hover {
            transform: scale(1.1);
            background: #1976d2;
        }
        .test-voice-btn i { font-size: 18px; }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }

        /* زر دوران الخريطة */
        .rotation-btn {
            position: fixed; bottom: 280px; right: 20px; z-index: 1001;
            background: #6c757d; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .rotation-btn:hover {
            transform: scale(1.1);
            background: #5a6268;
        }
        .rotation-btn.active {
            background: #007bff;
            animation: rotateIcon 2s infinite linear;
        }
        .rotation-btn i { font-size: 18px; }

        @keyframes rotateIcon {
            from { transform: rotate(0deg) scale(1.1); }
            to { transform: rotate(360deg) scale(1.1); }
        }

        /* زر تتبع المسار */
        .follow-btn {
            position: fixed; bottom: 340px; right: 20px; z-index: 1001;
            background: #6c757d; border: none; border-radius: 50%;
            width: 50px; height: 50px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer; transition: all 0.3s; color: white;
        }
        .follow-btn:hover {
            transform: scale(1.1);
            background: #5a6268;
        }
        .follow-btn.active {
            background: #28a745;
        }
        .follow-btn i { font-size: 18px; }

        /* تأثيرات الإشعارات */
        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutLeft {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(-100%); opacity: 0; }
        }

        /* أنماط أزرار التحكم */
        .control-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            margin: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #1976d2;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .control-btn.show-all {
            background: #4caf50;
        }

        .control-btn.show-all {
            background: #ff5722;
        }

        .control-btn.show-all:hover {
            background: #e64a19;
        }

        .control-btn.location-btn-main {
            background: #17a2b8;
        }

        .control-btn.location-btn-main:hover {
            background: #138496;
        }

        /* أنماط أزرار التحكم في العرض */
        .view-controls {
            margin-top: 15px;
            text-align: center;
        }

        /* إظهار الأزرار العائمة عند الحاجة */
        .clear-route-btn,
        .voice-control-btn,
        .rotation-btn,
        .follow-btn {
            display: none; /* مخفية افتراضياً، تظهر عند إنشاء مسار */
        }

        /* تأثير النبضة لعلامة الموقع الحالي */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .current-location-marker-navigation {
            z-index: 1000;
        }

        /* تنسيق المسارات البديلة */
        .alternative-route {
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .alternative-route:hover {
            opacity: 1 !important;
            filter: drop-shadow(0 0 8px #ff9800);
        }

        /* تنسيق tooltip المسارات البديلة */
        .leaflet-tooltip.alternative-route-tooltip {
            background: rgba(255, 152, 0, 0.9);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .leaflet-tooltip.alternative-route-tooltip::before {
            border-top-color: rgba(255, 152, 0, 0.9);
        }

        /* عنوان الفئات */
        .category-title {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        /* مربع البحث في الخريطة */
        .map-search-container {
            position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
            z-index: 1001; width: 400px; max-width: 90%;
        }

        .map-search-box {
            display: flex; background: white; border-radius: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2); overflow: hidden;
        }

        .map-search-box input {
            flex: 1; padding: 12px 20px; border: none; outline: none;
            font-size: 14px; font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
        }

        .map-search-btn {
            background: var(--primary-color); color: white; border: none;
            padding: 12px 20px; cursor: pointer; transition: all 0.3s;
        }

        .map-search-btn:hover {
            background: #1557b0;
        }

        .map-search-results {
            background: white; border-radius: 10px; margin-top: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2); max-height: 300px;
            overflow-y: auto; display: none;
        }

        .map-search-result {
            padding: 12px 20px; border-bottom: 1px solid #f0f0f0;
            cursor: pointer; transition: background 0.3s;
        }

        .map-search-result:hover {
            background: #f8f9fa;
        }

        .map-search-result:last-child {
            border-bottom: none;
        }

        .result-name {
            font-weight: 600; color: #333; margin-bottom: 4px;
        }

        .result-address {
            font-size: 12px; color: #666;
        }

        /* رسائل متحركة */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* قائمة طبقات الخريطة المخصصة */
        .custom-layers-control {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1002;
            font-family: 'Khalid-Art-Bold', 'Cairo', sans-serif;
        }

        .layers-toggle-btn {
            background: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .layers-toggle-btn:hover {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .layers-toggle-btn i {
            font-size: 16px;
            color: var(--primary-color);
        }

        .layers-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            margin-top: 5px;
            padding: 15px;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .layers-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .layers-dropdown h6 {
            margin: 0 0 12px;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .layer-option {
            display: flex;
            align-items: center;
            padding: 8px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 2px 0;
        }

        .layer-option:hover {
            background: #f8f9fa;
            padding-left: 8px;
            padding-right: 8px;
        }

        .layer-option input[type="radio"] {
            margin-left: 10px;
            accent-color: var(--primary-color);
            transform: scale(1.1);
        }

        .layer-option label {
            flex: 1;
            font-size: 13px;
            font-weight: 500;
            color: #333;
            cursor: pointer;
            margin: 0;
        }

        .layer-option.active label {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* إخفاء التحكم الافتراضي في الطبقات */
        .leaflet-control-layers {
            display: none !important;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .custom-layers-control {
                top: 15px;
                left: 15px;
            }

            .layers-toggle-btn {
                padding: 10px 12px;
                font-size: 13px;
            }

            .layers-dropdown {
                min-width: 180px;
                padding: 12px;
            }

            .layer-option {
                padding: 6px 0;
            }

            .layer-option label {
                font-size: 12px;
            }
        }

        /* تأثيرات الحركة */
        .layers-toggle-btn i:last-child {
            transition: transform 0.3s ease;
        }

        .layer-option {
            position: relative;
            overflow: hidden;
        }

        .layer-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .layer-option:hover::before {
            left: 100%;
        }

        /* تخصيص مقياس المسافة */
        .leaflet-control-scale {
            background: rgba(255, 255, 255, 0.9) !important;
            border-radius: 6px !important;
            padding: 5px 8px !important;
            font-family: 'Cairo', sans-serif !important;
            font-size: 11px !important;
            font-weight: 600 !important;
            color: #333 !important;
            border: 2px solid rgba(26,115,232,0.3) !important;
        }

        .leaflet-control-scale-line {
            border: 2px solid var(--primary-color) !important;
            border-top: none !important;
            color: var(--primary-color) !important;
        }

        /* تخصيص العلامات المخصصة */
        .custom-marker-with-label {
            background: transparent !important;
            border: none !important;
            transition: all 0.3s ease !important;
        }

        .custom-marker-with-label:hover {
            transform: scale(1.1) !important;
            z-index: 1000 !important;
        }

        /* تحسين ظهور الأسماء على الخرائط المختلفة */
        .leaflet-marker-icon .place-name-label {
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8), -1px -1px 2px rgba(255,255,255,0.3) !important;
            font-weight: 700 !important;
        }

        /* تحسين الرؤية على طبقة الأقمار الصناعية */
        .satellite-mode .place-name-label {
            background: rgba(0,0,0,0.9) !important;
            border: 1px solid rgba(255,255,255,0.3) !important;
        }

        /* تحسين الرؤية على الطبقات الفاتحة */
        .light-mode .place-name-label {
            background: rgba(255,255,255,0.95) !important;
            color: #333 !important;
            border: 1px solid rgba(0,0,0,0.2) !important;
        }

        /* تأثير الحركة للسهم */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <!-- شريط الحالة المحلي -->
    <div style="position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 8px 20px; text-align: center; font-size: 14px; font-weight: 600; z-index: 1002; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <span style="animation: pulse 2s infinite;">🔒</span>
        النسخة المحلية المتميزة - يعمل بدون إنترنت | 
        <span id="local-places-count">جاري التحميل...</span> موقع متاح |
        <span id="local-server-status" style="color: #fff;">متصل</span>
    </div>
    <style>
        @keyframes pulse { 0% { opacity: 1; } 50% { opacity: 0.7; } 100% { opacity: 1; } }
        body { padding-top: 40px !important; }
        #map { height: calc(100vh - 40px) !important; }
    </style>
    <button class="toggle-sidebar" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <button class="location-btn" onclick="getCurrentLocationFixed()" title="تحديد موقعي الحالي">
        <i class="fas fa-crosshairs"></i>
    </button>

    <!-- زر إلغاء المسار -->
    <button class="clear-route-btn" onclick="clearRoute()" title="إلغاء المسار">
        <i class="fas fa-times"></i>
    </button>

    <!-- زر التحكم الصوتي -->
    <button class="voice-control-btn" onclick="toggleVoiceNavigation()" title="تفعيل/إيقاف التنبيهات الصوتية">
        <i class="fas fa-volume-up"></i>
    </button>

    <!-- زر اختبار التنبيهات الصوتية -->
    <button class="test-voice-btn" onclick="testVoiceNavigation()" title="اختبار التنبيهات الصوتية">
        <i class="fas fa-microphone-alt"></i>
    </button>

    <!-- زر دوران الخريطة -->
    <button class="rotation-btn" onclick="toggleAdvancedRotation()" title="تفعيل/إلغاء دوران الخريطة المتقدم">
        <i class="fas fa-compass"></i>
    </button>

    <!-- زر تتبع المسار -->
    <button class="follow-btn" onclick="toggleFollowMode()" title="تفعيل/إلغاء وضع التتبع">
        <i class="fas fa-location-arrow"></i>
    </button>

    <!-- قائمة طبقات الخريطة المخصصة -->
    <div class="custom-layers-control">
        <button class="layers-toggle-btn" onclick="toggleLayersDropdown()">
            <i class="fas fa-layer-group"></i>
            <span id="currentLayerName">الشوارع التفصيلية المحسنة</span>
            <i class="fas fa-chevron-down" id="layersChevron"></i>
        </button>
        <div class="layers-dropdown" id="layersDropdown">
            <h6><i class="fas fa-map"></i> طبقات الخريطة</h6>
            <div class="layer-option active" data-layer="detailed">
                <input type="radio" name="mapLayer" id="layer-detailed" value="detailed" checked>
                <label for="layer-detailed">الشوارع التفصيلية المحسنة</label>
            </div>
            <div class="layer-option" data-layer="satellite">
                <input type="radio" name="mapLayer" id="layer-satellite" value="satellite">
                <label for="layer-satellite">الأقمار الصناعية</label>
            </div>
            <div class="layer-option" data-layer="satellite-hd">
                <input type="radio" name="mapLayer" id="layer-satellite-hd" value="satellite-hd">
                <label for="layer-satellite-hd">أقمار صناعية HD</label>
            </div>
            <div class="layer-option" data-layer="hybrid">
                <input type="radio" name="mapLayer" id="layer-hybrid" value="hybrid">
                <label for="layer-hybrid">هجينة محسنة</label>
            </div>
            <div class="layer-option" data-layer="terrain">
                <input type="radio" name="mapLayer" id="layer-terrain" value="terrain">
                <label for="layer-terrain">التضاريس</label>
            </div>
        </div>
    </div>

    <!-- مربع البحث في الخريطة -->
    <div class="map-search-container">
        <div class="map-search-box">
            <input type="text" id="mapSearchInput" placeholder="ابحث عن مكان في الخريطة...">
            <button onclick="searchInMap()" class="map-search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div id="mapSearchResults" class="map-search-results"></div>
    </div>

    <div id="map"></div>

    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-map-marked-alt"></i> خرائط اليمن</h4>
            <p class="mb-0">اكتشف الأماكن من حولك</p>
        </div>

        <div class="search-box">
            <input type="text" id="searchInput" placeholder="ابحث عن مكان...">
            <i class="fas fa-search"></i>
        </div>

        <div class="category-filters">
            <h6 class="category-title">الفئات</h6>
            <div id="categoryButtons">
                <span class="category-btn active" data-category="">الكل</span>
                <span class="category-btn" data-category="restaurant">مطاعم</span>
                <span class="category-btn" data-category="hospital">مستشفيات</span>
                <span class="category-btn" data-category="school">مدارس</span>
                <span class="category-btn" data-category="mosque">مساجد</span>
                <span class="category-btn" data-category="bank">بنوك</span>
                <span class="category-btn" data-category="gas_station">محطات وقود</span>
            </div>

            <!-- أزرار التحكم في العرض -->
            <div class="view-controls">
                <button class="control-btn location-btn-main" onclick="getCurrentLocationMain()" id="locationBtnMain" title="تحديد موقعي الحالي">
                    <i class="fas fa-crosshairs"></i> موقعي الحالي
                </button>
                <button class="control-btn show-all" onclick="showAllPlaces()">
                    <i class="fas fa-eye-slash"></i> إخفاء جميع المواقع
                </button>
                <button class="control-btn" onclick="fitMapToPlaces()">
                    <i class="fas fa-expand-arrows-alt"></i> توسيط الخريطة
                </button>
            </div>
        </div>

        <div id="placesList">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري تحميل الأماكن...</p>
            </div>
        </div>
    </div>

    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="totalPlaces">0</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="visiblePlaces">0</div>
            <div class="stat-label">الأماكن المعروضة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="currentGovernorate">صنعاء</div>
            <div class="stat-label">المحافظة الحالية</div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>

    <!-- نظام التوجيه الذكي -->
    <script src="{{ url_for('static', filename='js/smart-routing.js') }}">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>

    <script>
        let map, placesLayer, currentPlaces = [], currentFilter = '', currentSearch = '';
        let routingControl = null; // للتحكم في المسارات
        let userLocation = null; // موقع المستخدم
        let isRoutingInProgress = false; // لتتبع حالة إنشاء المسار

        // متغيرات النظام الصوتي
        let voiceNavigationEnabled = false;
        let speechSynthesis = window.speechSynthesis;
        let currentRoute = null;
        let lastAnnouncedStep = -1;
        let navigationInterval = null;
        let userPosition = null;

        // متغيرات الدوران التلقائي وتتبع المسار
        let isNavigating = false;
        let useAdvancedRotation = false;
        let navigationWatcher = null;
        let currentLocationMarker = null;
        let routeCoordinates = [];
        let currentStepIndex = 0;
        let lastBearing = 0;

        // متغيرات المسارات البديلة
        let alternativeRoutes = [];
        let alternativeRouteLayers = [];
        let mainRouteLayer = null;
        let isOffRouteRecalculating = false;
        let offRouteThreshold = 0.05; // 50 متر

        // متغيرات إدارة صلاحيات الموقع
        let locationPermissionStatus = 'unknown'; // 'granted', 'denied', 'prompt', 'unknown'
        let locationPermissionChecked = false;
        let cachedUserLocation = null;

        function initMap() {
            // إنشاء الخريطة مع إعدادات عالية الدقة
            map = L.map('map', {
                center: [15.3694, 44.1910],
                zoom: 12,
                zoomControl: true,
                preferCanvas: true,
                renderer: L.canvas({ padding: 0.5 }),
                maxZoom: 20,
                minZoom: 5,
                zoomSnap: 0.25,
                zoomDelta: 0.25
            });

            // طبقات الخرائط عالية الدقة مع دعم العربية
            const baseLayers = {
                // خريطة عالية الدقة - CartoDB Voyager مع أسماء عربية
                'عالية الدقة': L.layerGroup([
                    L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                        attribution: '© CARTO, © OpenStreetMap contributors',
                        maxZoom: 20,
                        subdomains: 'abcd',
                        tileSize: 256,
                        zoomOffset: 0,
                        detectRetina: true
                    }),
                    // طبقة الأسماء العربية
                    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        opacity: 0.3,
                        maxZoom: 19
                    })
                ]),

                // خريطة الأقمار الصناعية فائقة الدقة
                'أقمار صناعية فائقة الدقة': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                    maxZoom: 20,
                    tileSize: 256,
                    zoomOffset: 0,
                    detectRetina: true
                }),

                // خريطة الشوارع التفصيلية مع أسماء عربية محسنة
                'الشوارع التفصيلية المحسنة': L.layerGroup([
                    // الطبقة الأساسية (خارجية مع fallback محلي)
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors',
                        maxZoom: 19,
                        tileSize: 256,
                        zoomOffset: 0,
                        detectRetina: true
                    }),
                    // طبقة التسميات (خارجية مع fallback محلي)
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.8
                    })
                ]),

                // خريطة التضاريس عالية الدقة
                'التضاريس عالية الدقة': L.layerGroup([
                    L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenTopoMap contributors, © OpenStreetMap contributors',
                        maxZoom: 17,
                        tileSize: 256,
                        zoomOffset: 0,
                        detectRetina: true
                    }),
                    // طبقة التسميات من أقمار صناعية HD
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.8
                    })
                ]),

                // خريطة الأقمار الصناعية مع الأسماء
                'الأقمار الصناعية': L.layerGroup([
                    // طبقة الأقمار الصناعية الأساسية
                    L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '© Esri, DigitalGlobe, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                        maxZoom: 19
                    }),
                    // طبقة التسميات من أقمار صناعية HD
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.8
                    })
                ]),

                // خريطة الأقمار الصناعية عالية الدقة مع الأسماء
                'أقمار صناعية HD': L.layerGroup([
                    // طبقة الأقمار الصناعية عالية الدقة
                    L.tileLayer('https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20
                    }),
                    // طبقة الأسماء والطرق
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.8
                    })
                ]),

                // خريطة هجينة محسنة (أقمار صناعية + شوارع + أسماء)
                'هجينة محسنة': L.layerGroup([
                    // طبقة الأقمار الصناعية
                    L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '© Esri'
                    }),
                    // طبقة الشوارع الشفافة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        opacity: 0.4
                    }),
                    // طبقة التسميات من أقمار صناعية HD
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.9
                    })
                ]),

                // خريطة التضاريس مع الأسماء
                'التضاريس': L.layerGroup([
                    L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenTopoMap contributors',
                        maxZoom: 17
                    }),
                    // طبقة التسميات من أقمار صناعية HD
                    L.tileLayer('https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}', {
                        attribution: '© Google',
                        maxZoom: 20,
                        opacity: 0.8
                    })
                ]),

                // خريطة الوضع الليلي عالية الدقة
                'الوضع الليلي HD': L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                    attribution: '© CARTO, © OpenStreetMap contributors',
                    maxZoom: 20,
                    subdomains: 'abcd',
                    detectRetina: true
                }),

                // خريطة مونوكروم عالية الدقة
                'أبيض وأسود HD': L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}{r}.png', {
                    attribution: '© Stamen Design, © OpenStreetMap contributors',
                    maxZoom: 18,
                    subdomains: 'abcd',
                    detectRetina: true
                }),

                // خريطة الألوان المائية عالية الدقة
                'ألوان مائية HD': L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.png', {
                    attribution: '© Stamen Design, © OpenStreetMap contributors',
                    maxZoom: 16,
                    subdomains: 'abcd',
                    detectRetina: true
                })
            };

            // إضافة الطبقة الافتراضية (الشوارع التفصيلية بالعربية)
            baseLayers['الشوارع التفصيلية المحسنة'].addTo(map);

            // حفظ الطبقات للاستخدام في القائمة المخصصة
            window.mapBaseLayers = baseLayers;
            window.currentBaseLayer = baseLayers['الشوارع التفصيلية المحسنة'];

            // إضافة مقياس المسافة محسن
            L.control.scale({
                position: 'bottomleft',
                metric: true,
                imperial: false,
                maxWidth: 200,
                updateWhenIdle: false
            }).addTo(map);

            // إضافة مؤشر الإحداثيات
            const coordsControl = L.control({ position: 'bottomright' });
            coordsControl.onAdd = function(map) {
                const div = L.DomUtil.create('div', 'coords-control');
                div.style.background = 'rgba(255, 255, 255, 0.9)';
                div.style.padding = '5px 10px';
                div.style.borderRadius = '5px';
                div.style.fontSize = '12px';
                div.style.fontFamily = 'monospace';
                div.style.border = '1px solid #ccc';
                div.innerHTML = 'انقر على الخريطة لرؤية الإحداثيات';
                return div;
            };
            coordsControl.addTo(map);

            // تحديث الإحداثيات عند النقر
            map.on('click', function(e) {
                const lat = e.latlng.lat.toFixed(6);
                const lng = e.latlng.lng.toFixed(6);
                document.querySelector('.coords-control').innerHTML = `${lat}, ${lng}`;
            });

            // تحسين أداء الخريطة
            map.on('zoomstart', function() {
                map.getContainer().style.cursor = 'wait';
            });

            map.on('zoomend', function() {
                map.getContainer().style.cursor = '';
            });

            // إضافة مؤشر التحميل للطبقات
            map.on('layeradd', function() {
                // تم تحميل طبقة جديدة
            });

            placesLayer = L.layerGroup().addTo(map);

            // إضافة وظيفة النقر على الخريطة
            map.on('click', onMapClick);

            // إضافة مستمع لتغيير مستوى التكبير لإظهار/إخفاء الأسماء
            map.on('zoomend', function() {
                updateMarkersVisibility();
            });

            // إضافة مستمع لتغيير طبقة الخريطة
            map.on('baselayerchange', function(e) {
                updateMarkersStyle(e.name);
            });

            loadPlaces();
            loadStats();

            // فحص صلاحيات الموقع عند التحميل
            checkLocationPermission().then(status => {
                console.log(`🔐 حالة صلاحية الموقع عند التحميل: ${status}`);
            }).catch(error => {
                console.warn('⚠️ لا يمكن فحص صلاحية الموقع:', error);
            });

            console.log('✅ تم تهيئة الخريطة بنجاح');
        }

        // دالة التعامل مع النقر على الخريطة
        async function onMapClick(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;

            // إنشاء نافذة منبثقة للموقع المنقور عليه
            const popup = L.popup()
                .setLatLng(e.latlng)
                .setContent(`
                    <div class="popup-content" style="min-width: 250px; text-align: center;">
                        <div style="margin-bottom: 10px;">
                            <i class="fas fa-map-marker-alt" style="color: #ea4335; font-size: 20px;"></i>
                        </div>
                        <div class="popup-name" style="font-weight: bold; margin-bottom: 8px;">موقع مختار</div>
                        <div class="popup-coordinates" style="font-size: 12px; color: #666; margin-bottom: 10px;">
                            ${lat.toFixed(6)}, ${lng.toFixed(6)}
                        </div>
                        <div id="location-info-${lat.toFixed(6)}-${lng.toFixed(6)}" style="margin-bottom: 10px; font-size: 12px; color: #666;">
                            <i class="fas fa-spinner fa-spin"></i> جاري البحث عن معلومات الموقع...
                        </div>
                        <div class="popup-actions" style="margin-top: 10px;">
                            <button class="popup-btn" onclick="getDirectionsToPoint(${lat}, ${lng})" style="margin: 2px; background: #34a853;">
                                <i class="fas fa-directions"></i> الاتجاهات إلى هنا
                            </button>
                            <button class="popup-btn" onclick="getDirectionsFromPoint(${lat}, ${lng})" style="margin: 2px; background: #1976d2;">
                                <i class="fas fa-route"></i> الاتجاهات من هنا
                            </button>
                            <button class="popup-btn" onclick="shareLocation(${lat}, ${lng})" style="margin: 2px; background: #fbbc04;">
                                <i class="fas fa-share"></i> مشاركة الموقع
                            </button>
                            <button class="popup-btn" onclick="addToFavorites(${lat}, ${lng})" style="margin: 2px; background: #ff5722;">
                                <i class="fas fa-heart"></i> إضافة للمفضلة
                            </button>
                        </div>
                    </div>
                `)
                .openOn(map);

            // البحث عن معلومات الموقع باستخدام Reverse Geocoding
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`);
                const data = await response.json();

                if (data && data.display_name) {
                    const locationInfo = document.getElementById(`location-info-${lat.toFixed(6)}-${lng.toFixed(6)}`);
                    if (locationInfo) {
                        const address = data.display_name.split(',').slice(0, 3).join(', ');
                        locationInfo.innerHTML = `<i class="fas fa-map-marker-alt"></i> ${address}`;
                    }
                }
            } catch (error) {
                const locationInfo = document.getElementById(`location-info-${lat.toFixed(6)}-${lng.toFixed(6)}`);
                if (locationInfo) {
                    locationInfo.innerHTML = '<i class="fas fa-map-marker-alt"></i> موقع غير محدد';
                }
            }
        }

        async function loadPlaces() {
            try {
                const response = await fetch('/api/locations');
                const places = await response.json();
                if (Array.isArray(places)) {
                    currentPlaces = places.map(place => ({
                        ...place,
                        place_id: place.place_id || place.id,
                        name: place.name || place.name_ar,
                        category: place.category_id || 'general',
                        primary_photo: place.images && place.images.length > 0 ? place.images[0] : null,
                        all_images: place.images || []
                    }));

                    // فحص حالة الخادم المحلي
        async function checkLocalServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'متصل';
                    document.getElementById('local-server-status').style.color = '#fff';
                }
            } catch (error) {
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'غير متصل';
                    document.getElementById('local-server-status').style.color = '#ffeb3b';
                }
            }
        }
        
        // فحص دوري لحالة الخادم
        setInterval(checkLocalServerStatus, 30000);
        
        // تحديث الإحصائيات فقط
                    updateStats();
            // تحديث الشريط المحلي
            if (document.getElementById('local-places-count')) {
                document.getElementById('local-places-count').textContent = 
                    allPlaces.length.toLocaleString('ar') + ' مكان';
            }

                    // عرض رسالة ترحيبية بدلاً من الأماكن
                    document.getElementById('placesList').innerHTML = `
                        <div style="text-align: center; padding: 30px 20px; color: #666;">
                            <i class="fas fa-map-marked-alt" style="font-size: 48px; color: #1a73e8; margin-bottom: 15px;"></i>
                            <h5 style="margin-bottom: 10px; color: #333;">مرحباً بك في خرائط اليمن</h5>
                            <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                                تم تحميل <strong>${currentPlaces.length}</strong> موقع بنجاح
                            </p>
                            <p style="font-size: 13px; color: #888;">
                                اختر فئة من الأعلى لعرض المواقع المطلوبة
                            </p>
                            <div style="margin-top: 20px;">
                                <i class="fas fa-arrow-up" style="color: #1a73e8; animation: bounce 2s infinite;"></i>
                            </div>
                        </div>
                    `;

                    // لا نعرض أي نقاط في الخريطة عند التحميل الأولي
                    placesLayer.clearLayers();

                } else {
                    document.getElementById('placesList').innerHTML =
                        '<div class="loading"><i class="fas fa-exclamation-triangle"></i><p>خطأ في تحميل البيانات</p></div>';
                }
            } catch (error) {
                document.getElementById('placesList').innerHTML =
                    '<div class="loading"><i class="fas fa-exclamation-triangle"></i><p>خطأ في تحميل البيانات</p></div>';
            }
        }

        function displayPlaces(places) {
            // عرض الأماكن في القائمة الجانبية فقط
            const placesList = document.getElementById('placesList');
            placesList.innerHTML = '';

            if (places.length === 0) {
                placesList.innerHTML = '<div class="loading"><p>لا توجد أماكن مطابقة للبحث</p></div>';
                return;
            }

            // عرض الأماكن في القائمة فقط (بدون تعديل العلامات في الخريطة)
            places.forEach(place => {
                const placeCard = createPlaceCard(place);
                placesList.appendChild(placeCard);
            });

            document.getElementById('visiblePlaces').textContent = places.length;
        }

        // دالة منفصلة لتحديث العلامات في الخريطة مع الأسماء
        function updateMapMarkers(places) {
            placesLayer.clearLayers();

            places.forEach(place => {
                const categoryIcon = getCategoryIcon(place.category);
                const placeName = place.name || place.name_ar || 'موقع';

                const marker = L.marker([place.latitude, place.longitude], {
                    icon: L.divIcon({
                        className: 'custom-marker-with-label',
                        html: `
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <!-- الأيقونة -->
                                <div style="background: ${categoryIcon.color}; width: 35px; height: 35px; border-radius: 50%; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; position: relative; z-index: 2;">
                                    <i class="${categoryIcon.icon}"></i>
                                </div>
                                <!-- اسم المكان -->
                                <div style="background: rgba(0,0,0,0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; margin-top: 2px; white-space: nowrap; max-width: 120px; overflow: hidden; text-overflow: ellipsis; box-shadow: 0 2px 6px rgba(0,0,0,0.3); font-family: 'Cairo', sans-serif;">
                                    ${placeName.length > 15 ? placeName.substring(0, 15) + '...' : placeName}
                                </div>
                            </div>
                        `,
                        iconSize: [120, 60],
                        iconAnchor: [60, 35]
                    })
                }).bindPopup(createPopupContent(place));

                // إضافة تأثير hover للعلامة
                marker.on('mouseover', function() {
                    this.getElement().style.transform = 'scale(1.1)';
                    this.getElement().style.zIndex = '1000';
                });

                marker.on('mouseout', function() {
                    this.getElement().style.transform = 'scale(1)';
                    this.getElement().style.zIndex = '100';
                });

                placesLayer.addLayer(marker);
            });
        }

        function createPopupContent(place) {
            const images = place.all_images || [];
            const imageGallery = images.length > 0 ?
                `<div class="popup-images">
                    ${images.slice(0, 3).map((img, index) =>
                        `<img src="${img}" class="popup-image-thumb" alt="${place.name}"
                              onclick="openImageModal('${img}', '${(place.name || '').replace(/'/g, '&apos;')}')"
                              style="width: 60px; height: 60px; object-fit: cover; margin: 2px; border-radius: 4px; cursor: pointer;">`
                    ).join('')}
                    ${images.length > 3 ? `<div class="more-images">+${images.length - 3} صور أخرى</div>` : ''}
                </div>` : '';

            return `
                <div class="popup-content" style="min-width: 250px;">
                    ${place.primary_photo ? `<img src="${place.primary_photo}" class="popup-image" alt="${place.name}" onclick="openImageModal('${place.primary_photo}', '${(place.name || '').replace(/'/g, '&apos;')}')" style="cursor: pointer;">` : ''}
                    <div class="popup-name">${place.name || place.name_ar}</div>
                    <div class="popup-category">${getCategoryName(place.category)}</div>
                    ${place.address ? `<div class="popup-address" style="font-size: 12px; color: #666; margin: 5px 0;"><i class="fas fa-map-marker-alt"></i> ${place.address}</div>` : ''}
                    ${place.contact ? `<div class="popup-phone" style="font-size: 12px; color: #666; margin: 5px 0;"><i class="fas fa-phone"></i> ${place.contact}</div>` : ''}
                    ${place.rating ? `<div class="popup-rating">${'★'.repeat(Math.floor(place.rating))} ${place.rating}</div>` : ''}
                    ${imageGallery}
                    <div class="popup-actions" style="margin-top: 10px;">
                        <button class="popup-btn" onclick="showPlaceDetails('${place.place_id}')" style="margin: 2px;">
                            <i class="fas fa-info-circle"></i> التفاصيل
                        </button>
                        <button class="popup-btn" onclick="getDirections(${place.latitude}, ${place.longitude}, '${(place.name || place.name_ar || '').replace(/'/g, '\\\'')}')" style="margin: 2px; background: #34a853;">
                            <i class="fas fa-directions"></i> الاتجاهات
                        </button>
                        <button class="popup-btn" onclick="sharePlace('${place.place_id}', '${(place.name || '').replace(/'/g, '&apos;')}')" style="margin: 2px; background: #fbbc04;">
                            <i class="fas fa-share"></i> مشاركة
                        </button>
                    </div>
                </div>
            `;
        }

        function createPlaceCard(place) {
            const card = document.createElement('div');
            card.className = 'place-card';
            card.onclick = () => {
                map.setView([place.latitude, place.longitude], 16);
                // فتح النافذة المنبثقة للمكان
                const marker = placesLayer.getLayers().find(layer =>
                    layer.getLatLng().lat === place.latitude && layer.getLatLng().lng === place.longitude
                );
                if (marker) {
                    marker.openPopup();
                }
            };

            const images = place.all_images || [];
            const imageCount = images.length;

            card.innerHTML = `
                ${place.primary_photo ?
                    `<div class="place-image-container" style="position: relative;">
                        <img src="${place.primary_photo}" class="place-image" alt="${place.name}">
                        ${imageCount > 1 ? `<div class="image-count" style="position: absolute; top: 5px; right: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 10px; font-size: 11px;"><i class="fas fa-images"></i> ${imageCount}</div>` : ''}
                    </div>` :
                    `<div class="place-image-placeholder" style="height: 120px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border-radius: 8px; margin-bottom: 10px; color: #6c757d;">
                        <i class="fas fa-image" style="font-size: 24px;"></i>
                    </div>`
                }
                <div class="place-name">${place.name || place.name_ar}</div>
                <div class="place-category">${getCategoryName(place.category)}</div>
                ${place.rating ? `<div class="place-rating">${'★'.repeat(Math.floor(place.rating))} ${place.rating}</div>` : ''}
                ${place.address ? `<div class="text-muted small"><i class="fas fa-map-marker-alt"></i> ${place.address}</div>` : ''}
                ${place.contact ? `<div class="text-muted small"><i class="fas fa-phone"></i> ${place.contact}</div>` : ''}
            `;

            return card;
        }

        function getCategoryName(category) {
            const categories = {
                'restaurant': 'مطعم', 'hospital': 'مستشفى', 'school': 'مدرسة',
                'mosque': 'مسجد', 'bank': 'بنك', 'gas_station': 'محطة وقود',
                'shopping_mall': 'مركز تجاري', 'hotel': 'فندق',
                'pharmacy': 'صيدلية', 'university': 'جامعة', 'general': 'عام'
            };
            return categories[category] || category;
        }

        async function showPlaceDetails(placeId) {
            try {
                const response = await fetch(`/api/place/${placeId}`);
                const data = await response.json();
                if (data.success) {
                    alert(`تفاصيل ${data.place.name}\nالعنوان: ${data.place.address || 'غير محدد'}\nالهاتف: ${data.place.phone || 'غير محدد'}`);
                }
            } catch (error) {
                alert('خطأ في تحميل تفاصيل المكان');
            }
        }

        function filterByCategory(category) {
            currentFilter = category;

            // إذا كانت الفئة فارغة (الكل)، إخفاء جميع النقاط وعرض الرسالة الترحيبية
            if (!category) {
                placesLayer.clearLayers();
                document.getElementById('placesList').innerHTML = `
                    <div style="text-align: center; padding: 30px 20px; color: #666;">
                        <i class="fas fa-map-marked-alt" style="font-size: 48px; color: #1a73e8; margin-bottom: 15px;"></i>
                        <h5 style="margin-bottom: 10px; color: #333;">مرحباً بك في خرائط اليمن</h5>
                        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                            تم تحميل <strong>${currentPlaces.length}</strong> موقع بنجاح
                        </p>
                        <p style="font-size: 13px; color: #888;">
                            اختر فئة من الأعلى لعرض المواقع المطلوبة
                        </p>
                        <div style="margin-top: 20px;">
                            <i class="fas fa-arrow-up" style="color: #1a73e8; animation: bounce 2s infinite;"></i>
                        </div>
                    </div>
                `;
                document.getElementById('visiblePlaces').textContent = '0';
                return;
            }

            let filtered = updateDisplayByCategory(category);

            // إعادة تعيين العداد وعرض النتائج
            displayedPlacesCount = Math.min(20, filtered.length);
            const limitedResults = filtered.slice(0, displayedPlacesCount);
            displayPlaces(limitedResults);

            // إضافة زر "عرض المزيد" إذا كان هناك نتائج أكثر
            if (filtered.length > displayedPlacesCount) {
                addLoadMoreButtonForFiltered(filtered);
            }

            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');

            // رسالة توضيحية
            const categoryName = getCategoryName(category);
            showSuccessMessage(`تم عرض ${filtered.length} موقع من فئة: ${categoryName}`);
        }

        function searchPlaces(query) {
            currentSearch = query.toLowerCase();

            // إذا كان البحث فارغ، إخفاء جميع النقاط وعرض الرسالة الترحيبية
            if (!currentSearch) {
                placesLayer.clearLayers();
                document.getElementById('placesList').innerHTML = `
                    <div style="text-align: center; padding: 30px 20px; color: #666;">
                        <i class="fas fa-map-marked-alt" style="font-size: 48px; color: #1a73e8; margin-bottom: 15px;"></i>
                        <h5 style="margin-bottom: 10px; color: #333;">مرحباً بك في خرائط اليمن</h5>
                        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                            تم تحميل <strong>${currentPlaces.length}</strong> موقع بنجاح
                        </p>
                        <p style="font-size: 13px; color: #888;">
                            اختر فئة من الأعلى لعرض المواقع المطلوبة
                        </p>
                        <div style="margin-top: 20px;">
                            <i class="fas fa-arrow-up" style="color: #1a73e8; animation: bounce 2s infinite;"></i>
                        </div>
                    </div>
                `;
                document.getElementById('visiblePlaces').textContent = '0';

                // إعادة تعيين زر الفئة النشط إلى "الكل"
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector('[data-category=""]').classList.add('active');
                return;
            }

            let filtered = currentPlaces.filter(place =>
                (place.name && place.name.toLowerCase().includes(currentSearch)) ||
                (place.name_ar && place.name_ar.includes(currentSearch)) ||
                (place.address && place.address.toLowerCase().includes(currentSearch))
            );

            // تحديث العلامات في الخريطة لتظهر النتائج المفلترة فقط
            updateMapMarkers(filtered);

            // إعادة تعيين العداد وعرض النتائج
            displayedPlacesCount = Math.min(20, filtered.length);
            const limitedResults = filtered.slice(0, displayedPlacesCount);
            displayPlaces(limitedResults);

            // إضافة زر "عرض المزيد" إذا كان هناك نتائج أكثر
            if (filtered.length > displayedPlacesCount) {
                addLoadMoreButtonForFiltered(filtered);
            }
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                if (data.success) {
                    document.getElementById('totalPlaces').textContent = data.stats.total_places;
                }
            } catch (error) {
                // خطأ في تحميل الإحصائيات
            }
        }

        function updateStats() {
            document.getElementById('totalPlaces').textContent = currentPlaces.length;
            document.getElementById('visiblePlaces').textContent = currentPlaces.length;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // دالة فتح نافذة عرض الصور
        function openImageModal(imageSrc, placeName) {
            const safePlaceName = (placeName || '').replace(/'/g, '&apos;').replace(/"/g, '&quot;');

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.9); z-index: 10000; display: flex;
                align-items: center; justify-content: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="position: relative; max-width: 90%; max-height: 90%;">
                    <img src="${imageSrc}" alt="${safePlaceName}" style="max-width: 100%; max-height: 100%; border-radius: 8px;">
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9);
                        border: none; border-radius: 50%; width: 40px; height: 40px; cursor: pointer;
                        font-size: 18px; color: #333;
                    ">×</button>
                    <div style="
                        position: absolute; bottom: 10px; left: 10px; right: 10px;
                        background: rgba(0,0,0,0.7); color: white; padding: 10px;
                        border-radius: 6px; text-align: center;
                    ">${safePlaceName}</div>
                </div>
            `;

            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };

            document.body.appendChild(modal);
        }

        // دالة الحصول على الاتجاهات المحلية مع إدارة ذكية للصلاحيات
        async function getDirections(lat, lng, placeName = 'الوجهة') {
            // إلغاء المسار السابق إن وجد
            clearRoute();

            const locationBtn = document.querySelector('.location-btn');
            const originalContent = locationBtn.innerHTML;
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            try {
                // استخدام النظام الجديد لطلب الموقع
                const position = await requestUserLocation({
                    maximumAge: 300000 // استخدام موقع محفوظ لمدة 5 دقائق للمسارات
                });

                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;
                userLocation = [userLat, userLng];

                console.log(`📍 موقعك الحالي: ${userLat}, ${userLng}`);
                console.log(`🎯 الوجهة: ${lat}, ${lng} - ${placeName}`);

                // إنشاء المسار المحلي
                createLocalRoute(userLat, userLng, lat, lng, placeName);

            } catch (error) {
                console.error('❌ خطأ في تحديد الموقع للمسار:', error);

                let errorMessage = 'لم نتمكن من تحديد موقعك لإنشاء المسار. ';

                if (error.message.includes('رفض صلاحية')) {
                    errorMessage = 'تم رفض صلاحية الوصول للموقع. يرجى تفعيلها من إعدادات المتصفح أو ';
                } else {
                    switch(error.code) {
                        case 1: // PERMISSION_DENIED
                            errorMessage += 'يرجى السماح بالوصول للموقع في إعدادات المتصفح أو ';
                            break;
                        case 2: // POSITION_UNAVAILABLE
                            errorMessage += 'معلومات الموقع غير متاحة. تأكد من تفعيل GPS أو ';
                            break;
                        case 3: // TIMEOUT
                            errorMessage += 'انتهت مهلة تحديد الموقع. حاول مرة أخرى أو ';
                            break;
                        default:
                            errorMessage += 'حدث خطأ في تحديد الموقع. ';
                            break;
                    }
                }

                errorMessage += 'انقر على موقعك الحالي على الخريطة لتحديد المسار يدوياً.';

                alert(errorMessage);

                // تفعيل وضع اختيار نقطة البداية
                enableStartPointSelection(lat, lng, placeName);

            } finally {
                // إعادة تعيين زر الموقع
                locationBtn.innerHTML = originalContent;
            }
        }

        // دالة إنشاء المسار المحلي
        function createLocalRoute(startLat, startLng, endLat, endLng, placeName) {
            // منع إنشاء مسارات متعددة في نفس الوقت
            if (isRoutingInProgress) {
                return;
            }

            // تعيين حالة إنشاء المسار
            isRoutingInProgress = true;

            // حذف المسار السابق أولاً
            clearRoute();

            // إظهار مؤشر التحميل
            showLoadingIndicator('جاري حساب المسار...');

            // التحقق من وجود مكتبة Routing
            if (typeof L.Routing === 'undefined') {
                console.error('❌ مكتبة Leaflet Routing غير محملة!');
                alert('خطأ: مكتبة المسارات غير متاحة. يرجى إعادة تحميل الصفحة.');
                return;
            }

            console.log('✅ مكتبة Routing متاحة');

            try {
                // اختبار سريع للخدمة المحلية
                console.log('🏠 اختبار الخدمة المحلية...');

                // محاولة اختبار الخدمة المحلية أولاً مع timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 2000);

                fetch('http://localhost:5001/health', {
                    method: 'GET',
                    mode: 'cors',
                    signal: controller.signal
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    if (response.ok) {
                        console.log('✅ الخدمة المحلية متاحة - المتابعة مع الخدمة المحلية');
                        // المتابعة مع إنشاء المسار المحلي
                        createLocalRouteControl(startLat, startLng, endLat, endLng, placeName);
                    } else {
                        console.log('⚠️ الخدمة المحلية غير متاحة، التحويل للخدمة الخارجية');
                        createFallbackRoute(startLat, startLng, endLat, endLng, placeName);
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.log('🌐 فشل الاتصال بالخدمة المحلية، التحويل للخدمة الخارجية:', error.message);
                    createFallbackRoute(startLat, startLng, endLat, endLng, placeName);
                });

                // إنهاء الدالة هنا لتجنب تنفيذ الكود أدناه
                return;

            } catch (error) {
                console.error('❌ خطأ في إنشاء المسار:', error);
                hideLoadingIndicator(); // إخفاء مؤشر التحميل عند الخطأ
                isRoutingInProgress = false; // إعادة تعيين حالة إنشاء المسار
                alert('حدث خطأ أثناء إنشاء المسار: ' + error.message);
            }
        }

        // دالة إنشاء المسار المحلي الفعلية
        function createLocalRouteControl(startLat, startLng, endLat, endLng, placeName) {
            try {
                console.log('🏠 إنشاء المسار باستخدام الخدمة المحلية...');

                routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(startLat, startLng),
                    L.latLng(endLat, endLng)
                ],
                routeWhileDragging: true,
                addWaypoints: false,
                createMarker: function(i, waypoint, n) {
                    const isStart = i === 0;
                    return L.marker(waypoint.latLng, {
                        icon: L.divIcon({
                            className: isStart ? 'route-start-marker' : 'route-end-marker',
                            html: `<div style="background: ${isStart ? '#4285f4' : '#ea4335'}; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>`,
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        })
                    }).bindPopup(isStart ? 'نقطة البداية' : placeName);
                },
                router: L.Routing.osrmv1({
                    serviceUrl: 'http://localhost:5001/route/v1',
                    profile: 'driving',
                    // إعدادات محسنة لأقصر المسارات
                    options: {
                        alternatives: true,        // تفعيل المسارات البديلة للمقارنة
                        steps: true,              // تفعيل الخطوات للحصول على تعليمات
                        geometries: 'geojson',    // استخدام geojson للخدمة المحلية
                        overview: 'full',         // نظرة عامة كاملة
                        annotations: false,       // إلغاء المعلومات الإضافية لتسريع الاستجابة
                        continue_straight: true,  // تفضيل المسار المستقيم لأقصر مسافة
                        exclude: [],              // عدم استبعاد أي طرق للحصول على أقصر مسار
                        radiuses: [1000, 1000],   // زيادة نطاق البحث للحصول على خيارات أكثر
                        hints: [],                // تلميحات فارغة
                        bearings: []              // اتجاهات فارغة
                    }
                }),
                formatter: new L.Routing.Formatter({
                    units: 'metric'
                }),
                lineOptions: {
                    styles: [
                        {color: '#9c27b0', opacity: 0.9, weight: 6},
                        {color: '#ba68c8', opacity: 1, weight: 4}
                    ]
                },
                show: false, // إخفاء لوحة التحكم الافتراضية
                collapsible: false
            });

            console.log('✅ تم إنشاء كائن Routing Control');

            // إضافة معالجة أخطاء OSRM
            routingControl.on('routingerror', function(e) {
                console.error('❌ خطأ في خدمة OSRM:', e.error);
                console.log('🔄 التبديل للمسار المباشر الاحتياطي');

                // إزالة المسار الفاشل
                if (routingControl) {
                    map.removeControl(routingControl);
                    routingControl = null;
                }

                // إنشاء مسار مباشر احتياطي
                createDirectRoute(startLat, startLng, endLat, endLng, placeName);
            });

            // إضافة المسار للخريطة
            routingControl.addTo(map);
            console.log('✅ تم إضافة المسار للخريطة');

            // إظهار أزرار التحكم في المسار
            const clearBtn = document.querySelector('.clear-route-btn');
            const voiceBtn = document.querySelector('.voice-control-btn');
            const rotationBtn = document.querySelector('.rotation-btn');
            const followBtn = document.querySelector('.follow-btn');

            if (clearBtn) {
                clearBtn.style.display = 'block';
                console.log('✅ تم إظهار زر إلغاء المسار');
            } else {
                console.error('❌ لم يتم العثور على زر إلغاء المسار');
            }

            if (voiceBtn) {
                voiceBtn.style.display = 'block';
                console.log('✅ تم إظهار زر التحكم الصوتي');
            }

            if (rotationBtn) {
                rotationBtn.style.display = 'block';
                console.log('✅ تم إظهار زر الدوران');
            } else {
                console.error('❌ لم يتم العثور على زر الدوران');
            }

            if (followBtn) {
                followBtn.style.display = 'block';
                console.log('✅ تم إظهار زر التتبع');

                // تفعيل تلقائي للدوران والتتبع (اختياري)
                // يمكن إلغاء التعليق لتفعيل الميزات تلقائياً عند إنشاء مسار
                /*
                if (!useAdvancedRotation) {
                    toggleAdvancedRotation();
                    console.log('🔄 تم تفعيل الدوران التلقائي');
                }
                */
            } else {
                console.error('❌ لم يتم العثور على زر التتبع');
            }

            // إضافة معلومات المسار
            routingControl.on('routesfound', function(e) {
                const routes = e.routes;

                // حفظ بيانات المسار للنظام المتقدم
                if (routes && routes.length > 0) {
                    const route = routes[0];
                    currentRoute = {
                        coordinates: route.coordinates || [],
                        instructions: route.instructions || [],
                        summary: route.summary || {}
                    };

                    // تحويل الإحداثيات إلى التنسيق المطلوب
                    if (route.coordinates) {
                        routeCoordinates = route.coordinates.map(coord => ({
                            lat: coord.lat || coord[1],
                            lng: coord.lng || coord[0]
                        }));
                    }

                    // حفظ جميع المسارات للبدائل
                    alternativeRoutes = routes;
                    mainRouteLayer = routingControl;

                    console.log('💾 تم حفظ بيانات المسار للنظام المتقدم:', routeCoordinates.length, 'نقطة');
                    console.log(`📊 عدد المسارات المتاحة: ${routes.length}`);

                    // إنشاء المسارات البديلة إذا كانت متوفرة
                    if (routes.length > 1) {
                        createAlternativeRoutes(routes.slice(1), startLat, startLng, endLat, endLng);
                    }
                }

                // اختيار أقصر وأفضل مسار
                let selectedRoute = routes[0];
                if (routes.length > 1) {
                    console.log(`📊 تم العثور على ${routes.length} مسار بديل`);

                    // عرض معلومات جميع المسارات
                    routes.forEach((route, index) => {
                        const distance = (route.summary.totalDistance / 1000).toFixed(1);
                        const time = Math.round(route.summary.totalTime / 60);
                        console.log(`📍 المسار ${index + 1}: ${distance} كم، ${time} دقيقة`);
                    });

                    // تقييم المسارات واختيار الأفضل
                    selectedRoute = routes.reduce((best, current, index) => {
                        const bestScore = calculateRouteScore(best);
                        const currentScore = calculateRouteScore(current);

                        return currentScore > bestScore ? current : best;
                    });

                    const selectedDistance = (selectedRoute.summary.totalDistance / 1000).toFixed(1);
                    const selectedTime = Math.round(selectedRoute.summary.totalTime / 60);
                    console.log(`✅ تم اختيار أقصر مسار: ${selectedDistance} كم، ${selectedTime} دقيقة`);

                    // تحديث المسار المعروض إذا لم يكن الأول
                    if (selectedRoute !== routes[0]) {
                        console.log('🔄 تم اختيار مسار بديل أقصر من المسار الأول');
                    }
                } else {
                    const distance = (selectedRoute.summary.totalDistance / 1000).toFixed(1);
                    const time = Math.round(selectedRoute.summary.totalTime / 60);
                    console.log(`📍 تم العثور على مسار واحد: ${distance} كم، ${time} دقيقة`);
                }

                const summary = selectedRoute.summary;
                const distance = (summary.totalDistance / 1000).toFixed(1);
                const time = Math.round(summary.totalTime / 60);

                // حفظ المسار للتنبيهات الصوتية
                currentRoute = selectedRoute;
                lastAnnouncedStep = -1;

                // حفظ جميع المسارات للمسارات البديلة
                window.allRoutes = routes;

                console.log(`🛣️ المسار المختار: ${distance} كم، ${time} دقيقة`);

                // إنشاء نافذة معلومات المسار
                const routeInfo = L.popup()
                    .setLatLng([endLat, endLng])
                    .setContent(`
                        <div style="text-align: center;">
                            <h6 style="margin: 10px 0 5px; color: #1976d2;">
                                <i class="fas fa-route"></i> مسار محلي سريع
                            </h6>
                            <p style="margin: 5px 0; font-size: 14px;">
                                <i class="fas fa-map-marker-alt"></i> ${placeName}
                            </p>
                            <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin: 8px 0;">
                                <div style="font-size: 13px; color: #666;">
                                    <i class="fas fa-road"></i> المسافة: <strong>${distance} كم</strong>
                                </div>
                                <div style="font-size: 13px; color: #666;">
                                    <i class="fas fa-clock"></i> الوقت المتوقع: <strong>${time} دقيقة</strong>
                                </div>
                            </div>
                            <button onclick="clearRoute()" style="background: #ea4335; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin: 2px;">
                                <i class="fas fa-times"></i> إلغاء المسار
                            </button>
                            <button onclick="showAlternativeRoutes()" style="background: #ff9800; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin: 2px;">
                                <i class="fas fa-route"></i> مسارات بديلة
                            </button>
                        </div>
                    `)
                    .openOn(map);

                // إضافة مستمع لإغلاق النافذة لإلغاء المسار
                routeInfo.on('remove', function() {
                    console.log('🗑️ تم إغلاق نافذة المسار - إلغاء المسار تلقائياً');
                    // إلغاء المسار بدون إغلاق النافذة مرة أخرى لتجنب التكرار
                    if (routingControl) {
                        map.removeControl(routingControl);
                        routingControl = null;

                        // إخفاء زر إلغاء المسار
                        const clearBtn = document.querySelector('.clear-route-btn');
                        if (clearBtn) {
                            clearBtn.style.display = 'none';
                        }

                        console.log('✅ تم إلغاء المسار تلقائياً');
                    }
                });



                // إخفاء مؤشر التحميل
                hideLoadingIndicator();

                // إعادة تعيين حالة إنشاء المسار
                isRoutingInProgress = false;

                // إعلان صوتي عند إنشاء المسار
                if (voiceNavigationEnabled) {
                    announceRouteStart(distance, time, placeName);
                }
            });

            // معالجة أخطاء المسار مع نظام Fallback
            routingControl.on('routingerror', function(e) {
                console.error('❌ خطأ في الخدمة المحلية:', e);
                console.log('🔄 محاولة استخدام الخدمة الخارجية...');

                // محاولة استخدام الخدمة الخارجية كبديل
                createFallbackRoute(startLat, startLng, endLat, endLng, placeName);
            });

            } catch (error) {
                console.error('❌ خطأ في إنشاء المسار المحلي:', error);
                hideLoadingIndicator();
                isRoutingInProgress = false;
                // التحويل للخدمة الخارجية عند الخطأ
                createFallbackRoute(startLat, startLng, endLat, endLng, placeName);
            }
    }

    // دوال مؤشر التحميل
    function showLoadingIndicator(message = 'جاري التحميل...') {
        // إزالة أي مؤشر تحميل موجود
        hideLoadingIndicator();

        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'routingLoadingIndicator';
        loadingDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white; padding: 20px 30px;
            border-radius: 10px; z-index: 10000; text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        loadingDiv.innerHTML = `
            <div style="margin-bottom: 10px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #4285f4;"></i>
            </div>
            <div style="font-size: 14px;">${message}</div>
        `;
        document.body.appendChild(loadingDiv);
    }

    function hideLoadingIndicator() {
        const loadingDiv = document.getElementById('routingLoadingIndicator');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // دالة Fallback للخدمة الخارجية
    function createFallbackRoute(startLat, startLng, endLat, endLng, placeName) {
        console.log('🌐 استخدام الخدمة الخارجية كبديل...');

        // حذف أي مسار سابق أولاً
        clearRoute();

        try {
            // إنشاء مسار باستخدام الخدمة الخارجية
            const fallbackRoutingControl = L.Routing.control({
                waypoints: [
                    L.latLng(startLat, startLng),
                    L.latLng(endLat, endLng)
                ],
                routeWhileDragging: false,
                addWaypoints: false,
                createMarker: function(i, waypoint, n) {
                    const isStart = i === 0;
                    return L.marker(waypoint.latLng, {
                        icon: L.divIcon({
                            className: isStart ? 'route-start-marker' : 'route-end-marker',
                            html: `<div style="background: ${isStart ? '#4285f4' : '#ea4335'}; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>`,
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        })
                    }).bindPopup(isStart ? 'نقطة البداية' : placeName);
                },
                router: L.Routing.osrmv1({
                    serviceUrl: 'https://router.project-osrm.org/route/v1',
                    profile: 'driving',
                    options: {
                        alternatives: false,
                        steps: false,
                        geometries: 'polyline',
                        overview: 'simplified',
                        annotations: false
                    }
                }),
                formatter: new L.Routing.Formatter({
                    units: 'metric'
                }),
                lineOptions: {
                    styles: [
                        {color: '#9c27b0', opacity: 0.9, weight: 6},
                        {color: '#ba68c8', opacity: 1, weight: 4}
                    ]
                },
                show: false,
                collapsible: false
            });

            // إضافة المسار للخريطة
            fallbackRoutingControl.addTo(map);
            routingControl = fallbackRoutingControl; // حفظ المرجع

            // معالجة نجاح المسار البديل
            fallbackRoutingControl.on('routesfound', function(e) {
                const routes = e.routes;
                const selectedRoute = routes[0];
                const summary = selectedRoute.summary;
                const distance = (summary.totalDistance / 1000).toFixed(1);
                const time = Math.round(summary.totalTime / 60);

                hideLoadingIndicator();



                // إظهار رسالة للمستخدم
                const routeInfo = L.popup()
                    .setLatLng([endLat, endLng])
                    .setContent(`
                        <div style="text-align: center;">
                            <h6 style="margin: 10px 0 5px; color: #ff9800;">
                                <i class="fas fa-route"></i> مسار بديل (خدمة خارجية)
                            </h6>
                            <p style="margin: 5px 0; font-size: 14px;">
                                <i class="fas fa-map-marker-alt"></i> ${placeName}
                            </p>
                            <div style="background: #fff3e0; padding: 8px; border-radius: 6px; margin: 8px 0;">
                                <div style="font-size: 13px; color: #666;">
                                    <i class="fas fa-road"></i> المسافة: <strong>${distance} كم</strong>
                                </div>
                                <div style="font-size: 13px; color: #666;">
                                    <i class="fas fa-clock"></i> الوقت المتوقع: <strong>${time} دقيقة</strong>
                                </div>
                            </div>
                            <button onclick="clearRoute()" style="background: #ea4335; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-times"></i> إلغاء المسار
                            </button>
                        </div>
                    `)
                    .openOn(map);
            });

            // معالجة فشل المسار البديل
            fallbackRoutingControl.on('routingerror', function(e) {
                console.error('❌ فشل في الخدمة البديلة أيض<|im_start|>:', e);
                hideLoadingIndicator();
                alert('لم نتمكن من إنشاء المسار. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.');
                clearRoute();
            });

        } catch (error) {
            console.error('❌ خطأ في إنشاء المسار البديل:', error);
            hideLoadingIndicator();
            alert('حدث خطأ أثناء إنشاء المسار البديل: ' + error.message);
        }
    }

        // دالة تفعيل اختيار نقطة البداية
        function enableStartPointSelection(endLat, endLng, placeName) {
            alert('انقر على موقعك الحالي على الخريطة');

            const clickHandler = function(e) {
                const startLat = e.latlng.lat;
                const startLng = e.latlng.lng;

                createLocalRoute(startLat, startLng, endLat, endLng, placeName);
                map.off('click', clickHandler);
            };

            map.on('click', clickHandler);
        }

        // دالة إلغاء المسار
        function clearRoute() {
            console.log('🧹 مسح جميع المسارات...');

            // إزالة المسار الحالي إن وجد
            if (routingControl) {
                try {
                    map.removeControl(routingControl);
                    console.log('🗑️ تم حذف المسار الحالي');
                } catch (e) {
                    console.log('⚠️ خطأ في حذف المسار:', e);
                }
                routingControl = null;
            }

            // إزالة المسار الرئيسي البديل
            if (mainRouteLayer) {
                try {
                    map.removeControl(mainRouteLayer);
                    console.log('🗑️ تم حذف المسار الرئيسي البديل');
                } catch (e) {
                    console.log('⚠️ خطأ في حذف المسار الرئيسي البديل:', e);
                }
                mainRouteLayer = null;
            }

            // مسح المسارات البديلة
            clearAlternativeRoutes();

            // إزالة جميع المسارات المحتملة من الخريطة
            map.eachLayer(function(layer) {
                if (layer instanceof L.Routing.Control) {
                    try {
                        map.removeLayer(layer);
                        console.log('🗑️ تم حذف مسار إضافي');
                    } catch (e) {
                        console.log('⚠️ خطأ في حذف مسار إضافي:', e);
                    }
                }
            });

            // إغلاق أي نوافذ منبثقة مفتوحة (نافذة معلومات المسار)
            map.closePopup();

            // إخفاء أزرار التحكم في المسار
            const clearBtn = document.querySelector('.clear-route-btn');
            const voiceBtn = document.querySelector('.voice-control-btn');
            const rotationBtn = document.querySelector('.rotation-btn');
            const followBtn = document.querySelector('.follow-btn');

            if (clearBtn) clearBtn.style.display = 'none';
            if (voiceBtn) voiceBtn.style.display = 'none';
            if (rotationBtn) rotationBtn.style.display = 'none';
            if (followBtn) followBtn.style.display = 'none';

            // إيقاف الملاحة المتقدمة
            stopNavigation();

            // إيقاف التنبيهات الصوتية
            stopVoiceNavigation();

            // إخفاء مؤشر التحميل إن وجد
            hideLoadingIndicator();

            // إعادة تعيين المتغيرات
            currentRoute = null;
            routeCoordinates = [];
            alternativeRoutes = [];
            isRoutingInProgress = false;
            isOffRouteRecalculating = false;

            console.log('✅ تم إلغاء جميع المسارات والمسارات البديلة');
        }

        // دالة مشاركة المكان
        function sharePlace(placeId, placeName) {
            const url = `${window.location.origin}?place=${placeId}`;

            if (navigator.share) {
                navigator.share({
                    title: `خرائط اليمن - ${placeName}`,
                    text: `اكتشف ${placeName} على خرائط اليمن`,
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    alert('تم نسخ رابط المكان إلى الحافظة!');
                }).catch(() => {
                    prompt('انسخ هذا الرابط:', url);
                });
            }
        }

        // دالة الاتجاهات إلى نقطة محددة
        function getDirectionsToPoint(lat, lng) {
            getDirections(lat, lng, 'الموقع المختار');
        }

        // دالة الاتجاهات من نقطة محددة
        function getDirectionsFromPoint(startLat, startLng) {
            alert('انقر على الوجهة المطلوبة على الخريطة');

            const clickHandler = function(e) {
                const endLat = e.latlng.lat;
                const endLng = e.latlng.lng;

                createLocalRoute(startLat, startLng, endLat, endLng, 'الوجهة المختارة');
                map.off('click', clickHandler);
            };

            map.on('click', clickHandler);
        }

        // دالة مشاركة موقع محدد
        function shareLocation(lat, lng) {
            const url = `${window.location.origin}?lat=${lat}&lng=${lng}`;

            if (navigator.share) {
                navigator.share({
                    title: 'خرائط اليمن - موقع مشارك',
                    text: `موقع على خرائط اليمن: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
                    url: url
                });
            } else {
                navigator.clipboard.writeText(url).then(() => {
                    showSuccessMessage('تم نسخ رابط الموقع إلى الحافظة!');
                }).catch(() => {
                    prompt('انسخ هذا الرابط:', url);
                });
            }
        }

        // دالة إضافة موقع للمفضلة
        function addToFavorites(lat, lng) {
            const favorites = JSON.parse(localStorage.getItem('yemenMapsFavorites') || '[]');
            const newFavorite = {
                id: Date.now(),
                lat: lat,
                lng: lng,
                name: `موقع مفضل ${favorites.length + 1}`,
                dateAdded: new Date().toLocaleDateString('ar')
            };

            favorites.push(newFavorite);
            localStorage.setItem('yemenMapsFavorites', JSON.stringify(favorites));

            showSuccessMessage('تم إضافة الموقع للمفضلة!');

            // إضافة علامة للموقع المفضل
            const favoriteMarker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'favorite-marker',
                    html: '<div style="background: #ff5722; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                })
            }).addTo(map);

            favoriteMarker.bindPopup(`
                <div style="text-align: center;">
                    <i class="fas fa-heart" style="color: #ff5722; font-size: 16px;"></i>
                    <h6 style="margin: 8px 0 4px;">موقع مفضل</h6>
                    <p style="margin: 0; font-size: 11px; color: #666;">
                        ${lat.toFixed(6)}, ${lng.toFixed(6)}
                    </p>
                </div>
            `);
        }

        // متغير لتتبع عدد الأماكن المعروضة
        let displayedPlacesCount = 20;

        // دالة إضافة زر "عرض المزيد"
        function addLoadMoreButton() {
            const placesList = document.getElementById('placesList');
            const loadMoreBtn = document.createElement('div');
            loadMoreBtn.className = 'load-more-container';
            loadMoreBtn.style.cssText = 'text-align: center; padding: 20px; margin: 10px;';
            loadMoreBtn.innerHTML = `
                <button class="load-more-btn" onclick="loadMorePlaces()" style="
                    background: var(--primary-color); color: white; border: none;
                    padding: 12px 24px; border-radius: 25px; cursor: pointer;
                    font-size: 14px; transition: all 0.3s; box-shadow: 0 2px 8px rgba(26,115,232,0.3);
                ">
                    <i class="fas fa-plus"></i> عرض المزيد (${currentPlaces.length - displayedPlacesCount} مكان متبقي)
                </button>
            `;
            placesList.appendChild(loadMoreBtn);
        }

        // دالة إضافة زر "عرض المزيد" للنتائج المفلترة
        function addLoadMoreButtonForFiltered(filteredPlaces) {
            const placesList = document.getElementById('placesList');
            const loadMoreBtn = document.createElement('div');
            loadMoreBtn.className = 'load-more-container';
            loadMoreBtn.style.cssText = 'text-align: center; padding: 20px; margin: 10px;';
            loadMoreBtn.innerHTML = `
                <button class="load-more-btn" onclick="loadMoreFilteredPlaces()" style="
                    background: var(--primary-color); color: white; border: none;
                    padding: 12px 24px; border-radius: 25px; cursor: pointer;
                    font-size: 14px; transition: all 0.3s; box-shadow: 0 2px 8px rgba(26,115,232,0.3);
                ">
                    <i class="fas fa-plus"></i> عرض المزيد (${filteredPlaces.length - displayedPlacesCount} نتيجة متبقية)
                </button>
            `;
            placesList.appendChild(loadMoreBtn);

            // حفظ النتائج المفلترة للاستخدام لاحقاً
            window.currentFilteredPlaces = filteredPlaces;
        }

        // دالة تحميل المزيد من الأماكن
        function loadMorePlaces() {
            const nextBatch = currentPlaces.slice(displayedPlacesCount, displayedPlacesCount + 20);
            displayedPlacesCount += 20;

            // إزالة زر "عرض المزيد" الحالي
            const loadMoreContainer = document.querySelector('.load-more-container');
            if (loadMoreContainer) {
                loadMoreContainer.remove();
            }

            // إضافة الأماكن الجديدة
            const placesList = document.getElementById('placesList');
            nextBatch.forEach(place => {
                const marker = L.marker([place.latitude, place.longitude])
                    .bindPopup(createPopupContent(place))
                    .addTo(placesLayer);

                const card = createPlaceCard(place);
                placesList.appendChild(card);
            });

            // إضافة زر "عرض المزيد" مرة أخرى إذا كان هناك أماكن أكثر
            if (displayedPlacesCount < currentPlaces.length) {
                addLoadMoreButton();
            }

            // فحص حالة الخادم المحلي
        async function checkLocalServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'متصل';
                    document.getElementById('local-server-status').style.color = '#fff';
                }
            } catch (error) {
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'غير متصل';
                    document.getElementById('local-server-status').style.color = '#ffeb3b';
                }
            }
        }
        
        // فحص دوري لحالة الخادم
        setInterval(checkLocalServerStatus, 30000);
        
        // تحديث الإحصائيات
            document.getElementById('visiblePlaces').textContent = Math.min(displayedPlacesCount, currentPlaces.length);

            console.log(`✅ تم عرض ${Math.min(displayedPlacesCount, currentPlaces.length)} مكان من أصل ${currentPlaces.length}`);
        }

        // دالة تحميل المزيد من النتائج المفلترة
        function loadMoreFilteredPlaces() {
            if (!window.currentFilteredPlaces) return;

            const nextBatch = window.currentFilteredPlaces.slice(displayedPlacesCount, displayedPlacesCount + 20);
            displayedPlacesCount += 20;

            // إزالة زر "عرض المزيد" الحالي
            const loadMoreContainer = document.querySelector('.load-more-container');
            if (loadMoreContainer) {
                loadMoreContainer.remove();
            }

            // إضافة الأماكن الجديدة
            const placesList = document.getElementById('placesList');
            nextBatch.forEach(place => {
                const marker = L.marker([place.latitude, place.longitude])
                    .bindPopup(createPopupContent(place))
                    .addTo(placesLayer);

                const card = createPlaceCard(place);
                placesList.appendChild(card);
            });

            // إضافة زر "عرض المزيد" مرة أخرى إذا كان هناك نتائج أكثر
            if (displayedPlacesCount < window.currentFilteredPlaces.length) {
                addLoadMoreButtonForFiltered(window.currentFilteredPlaces);
            }

            // فحص حالة الخادم المحلي
        async function checkLocalServerStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'متصل';
                    document.getElementById('local-server-status').style.color = '#fff';
                }
            } catch (error) {
                if (document.getElementById('local-server-status')) {
                    document.getElementById('local-server-status').textContent = 'غير متصل';
                    document.getElementById('local-server-status').style.color = '#ffeb3b';
                }
            }
        }
        
        // فحص دوري لحالة الخادم
        setInterval(checkLocalServerStatus, 30000);
        
        // تحديث الإحصائيات
            document.getElementById('visiblePlaces').textContent = Math.min(displayedPlacesCount, window.currentFilteredPlaces.length);

            console.log(`✅ تم عرض ${Math.min(displayedPlacesCount, window.currentFilteredPlaces.length)} نتيجة من أصل ${window.currentFilteredPlaces.length}`);
        }

        // دالة تحديد الموقع الحالي مع إدارة ذكية للصلاحيات
        async function getCurrentLocation() {
            const locationBtn = document.querySelector('.location-btn');
            const originalContent = locationBtn.innerHTML;
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            try {
                // فحص الصلاحية أولاً
                const permissionStatus = await checkLocationPermission();

                if (permissionStatus === 'denied') {
                    showLocationPermissionDialog(
                        'تم رفض صلاحية الموقع',
                        'لقد رفضت السماح بالوصول لموقعك سابقاً. يمكنك تفعيل الصلاحية من إعدادات المتصفح.',
                        false
                    );
                    return;
                }

                // إذا كانت الصلاحية غير محددة، اطلبها مع رسالة توضيحية
                if (permissionStatus === 'prompt') {
                    showLocationPermissionDialog(
                        'طلب الوصول لموقعك الجغرافي',
                        'نحتاج للوصول لموقعك الحالي لتحديده على الخريطة وتوفير خدمات التوجيه. هذا الطلب سيظهر مرة واحدة فقط.',
                        true,
                        async () => {
                            // عند الموافقة، طلب الموقع
                            await performLocationRequest();
                        },
                        () => {
                            // عند الرفض
                            console.log('تم رفض طلب الوصول للموقع من قبل المستخدم');
                            locationBtn.innerHTML = originalContent;
                        }
                    );
                    return;
                }

                // إذا كانت الصلاحية ممنوحة، طلب الموقع مباشرة
                await performLocationRequest();

            } catch (error) {
                console.error('خطأ في getCurrentLocation:', error);
                locationBtn.innerHTML = originalContent;
                alert('حدث خطأ أثناء تحديد الموقع: ' + error.message);
            }

            async function performLocationRequest() {
                try {
                    // استخدام النظام الجديد لطلب الموقع
                    const position = await requestUserLocation({
                        maximumAge: 60000 // استخدام موقع محفوظ لمدة دقيقة واحدة
                    });

                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    const accuracy = position.coords.accuracy;

                    // تحريك الخريطة للموقع الحالي
                    map.setView([lat, lng], 16);

                    // إضافة علامة للموقع الحالي
                    if (window.currentLocationMarker) {
                        map.removeLayer(window.currentLocationMarker);
                    }

                    window.currentLocationMarker = L.marker([lat, lng], {
                        icon: L.divIcon({
                            className: 'current-location-marker',
                            html: '<div style="background: #4285f4; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                            iconSize: [20, 20],
                            iconAnchor: [10, 10]
                        })
                    }).addTo(map);

                    // إضافة نافذة منبثقة مع معلومات الموقع
                    window.currentLocationMarker.bindPopup(`
                        <div style="text-align: center; padding: 10px;">
                            <h6 style="margin-bottom: 10px; color: #4285f4;">
                                <i class="fas fa-location-arrow"></i> موقعك الحالي
                            </h6>
                            <p style="margin: 5px 0; color: #666; font-size: 12px;">
                                <strong>الإحداثيات:</strong><br>
                                ${lat.toFixed(6)}, ${lng.toFixed(6)}
                            </p>
                            <p style="margin: 5px 0; color: #666; font-size: 12px;">
                                <strong>دقة التحديد:</strong> ${Math.round(accuracy)} متر
                            </p>
                        </div>
                    `).openPopup();

                    // إظهار رسالة نجاح
                    showSuccessMessage('تم تحديد موقعك بنجاح!');

                    console.log(`✅ تم تحديد الموقع: ${lat}, ${lng} (دقة: ${accuracy}م)`);

                } catch (error) {
                    let errorTitle = 'فشل في تحديد الموقع';
                    let errorMessage = '';

                    if (error.message.includes('رفض صلاحية')) {
                        errorTitle = 'تم رفض الصلاحية';
                        errorMessage = error.message;
                    } else {
                        switch(error.code) {
                            case 1: // PERMISSION_DENIED
                                errorTitle = 'تم رفض الإذن';
                                errorMessage = 'لقد رفضت السماح بالوصول لموقعك. يمكنك تفعيل الإذن من إعدادات المتصفح.';
                                break;
                            case 2: // POSITION_UNAVAILABLE
                                errorTitle = 'الموقع غير متاح';
                                errorMessage = 'لا يمكن تحديد موقعك حالياً. تأكد من تفعيل خدمات الموقع.';
                                break;
                            case 3: // TIMEOUT
                                errorTitle = 'انتهت مهلة التحديد';
                                errorMessage = 'استغرق تحديد الموقع وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى.';
                                break;
                            default:
                                errorMessage = 'حدث خطأ غير متوقع أثناء تحديد الموقع.';
                        }
                    }

                    showLocationPermissionDialog(errorTitle, errorMessage, false);
                    console.error('خطأ في تحديد الموقع:', error);
                } finally {
                    locationBtn.innerHTML = originalContent;
                }
            }
        }



        // دالة عرض نافذة طلب الموافقة
        function showLocationPermissionDialog(title, message, showButtons = true, onAccept = null, onReject = null, acceptText = 'السماح', rejectText = 'رفض') {
            // إزالة أي نافذة موجودة
            const existingDialog = document.getElementById('locationPermissionDialog');
            if (existingDialog) {
                existingDialog.remove();
            }

            const dialog = document.createElement('div');
            dialog.id = 'locationPermissionDialog';
            dialog.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.7); z-index: 10001; display: flex;
                align-items: center; justify-content: center; font-family: 'Cairo', sans-serif;
            `;

            dialog.innerHTML = `
                <div style="
                    background: white; border-radius: 15px; padding: 30px; max-width: 400px; width: 90%;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; position: relative;
                ">
                    <div style="
                        width: 60px; height: 60px; background: #4285f4; border-radius: 50%;
                        margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;
                    ">
                        <i class="fas fa-map-marker-alt" style="color: white; font-size: 24px;"></i>
                    </div>

                    <h3 style="margin: 0 0 15px; color: #333; font-size: 18px; font-weight: 600;">
                        ${title}
                    </h3>

                    <p style="margin: 0 0 25px; color: #666; font-size: 14px; line-height: 1.5;">
                        ${message}
                    </p>

                    ${showButtons ? `
                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button onclick="handleLocationPermission(false)" style="
                                background: #f1f3f4; color: #5f6368; border: none; padding: 12px 24px;
                                border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500;
                                transition: all 0.3s; font-family: 'Cairo', sans-serif;
                            ">
                                ${rejectText}
                            </button>
                            <button onclick="handleLocationPermission(true)" style="
                                background: #4285f4; color: white; border: none; padding: 12px 24px;
                                border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500;
                                transition: all 0.3s; font-family: 'Cairo', sans-serif;
                            ">
                                ${acceptText}
                            </button>
                        </div>
                    ` : `
                        <button onclick="closeLocationDialog()" style="
                            background: #4285f4; color: white; border: none; padding: 12px 24px;
                            border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500;
                            transition: all 0.3s; font-family: 'Cairo', sans-serif;
                        ">
                            حسناً
                        </button>
                    `}
                </div>
            `;

            document.body.appendChild(dialog);

            // حفظ callbacks
            window.locationPermissionCallbacks = {
                onAccept: onAccept,
                onReject: onReject
            };
        }

        // دالة معالجة رد المستخدم
        function handleLocationPermission(accepted) {
            const dialog = document.getElementById('locationPermissionDialog');
            if (dialog) {
                dialog.remove();
            }

            if (accepted && window.locationPermissionCallbacks?.onAccept) {
                window.locationPermissionCallbacks.onAccept();
            } else if (!accepted && window.locationPermissionCallbacks?.onReject) {
                window.locationPermissionCallbacks.onReject();
            }

            // تنظيف callbacks
            window.locationPermissionCallbacks = null;
        }

        // دالة إغلاق النافذة
        function closeLocationDialog() {
            const dialog = document.getElementById('locationPermissionDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // دالة عرض رسالة النجاح
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10002;
                background: #4caf50; color: white; padding: 15px 20px;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                font-family: 'Cairo', sans-serif; font-size: 14px;
                animation: slideIn 0.3s ease-out;
            `;
            successDiv.innerHTML = `
                <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
                ${message}
            `;

            document.body.appendChild(successDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                successDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (successDiv.parentNode) {
                        successDiv.parentNode.removeChild(successDiv);
                    }
                }, 300);
            }, 3000);
        }

        // دالة البحث في الخريطة باستخدام Nominatim
        async function searchInMap() {
            const query = document.getElementById('mapSearchInput').value.trim();
            if (!query) return;

            const resultsContainer = document.getElementById('mapSearchResults');
            resultsContainer.innerHTML = '<div style="padding: 20px; text-align: center;"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>';
            resultsContainer.style.display = 'block';

            try {
                // البحث في اليمن أولاً
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query + ' Yemen')}&limit=5&accept-language=ar`);
                const results = await response.json();

                if (results.length === 0) {
                    resultsContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">لا توجد نتائج</div>';
                    return;
                }

                resultsContainer.innerHTML = '';
                results.forEach(result => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'map-search-result';
                    resultDiv.innerHTML = `
                        <div class="result-name">${result.display_name.split(',')[0]}</div>
                        <div class="result-address">${result.display_name}</div>
                    `;

                    resultDiv.onclick = () => {
                        const lat = parseFloat(result.lat);
                        const lng = parseFloat(result.lon);

                        // تحريك الخريطة للموقع
                        map.setView([lat, lng], 16);

                        // إضافة علامة مؤقتة
                        if (window.tempSearchMarker) {
                            map.removeLayer(window.tempSearchMarker);
                        }

                        window.tempSearchMarker = L.marker([lat, lng], {
                            icon: L.divIcon({
                                className: 'temp-search-marker',
                                html: '<div style="background: #ea4335; width: 25px; height: 25px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                                iconSize: [25, 25],
                                iconAnchor: [12, 12]
                            })
                        }).addTo(map);

                        window.tempSearchMarker.bindPopup(`
                            <div style="text-align: center;">
                                <h6 style="margin: 10px 0 5px;">${result.display_name.split(',')[0]}</h6>
                                <p style="margin: 0; font-size: 12px; color: #666;">${result.display_name}</p>
                                <div style="margin-top: 10px;">
                                    <button onclick="getDirections(${lat}, ${lng}, '${result.display_name.split(',')[0].replace(/'/g, '\\\'')}')"
                                            style="background: #1976d2; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin: 2px;">
                                        <i class="fas fa-directions"></i> الاتجاهات
                                    </button>
                                    <button onclick="addToFavorites(${lat}, ${lng}, '${result.display_name.split(',')[0].replace(/'/g, '\\\'')}')"
                                            style="background: #34a853; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin: 2px;">
                                        <i class="fas fa-heart"></i> إضافة للمفضلة
                                    </button>
                                </div>
                            </div>
                        `).openPopup();

                        // إخفاء نتائج البحث
                        resultsContainer.style.display = 'none';
                        document.getElementById('mapSearchInput').value = '';

                        console.log(`✅ تم العثور على: ${result.display_name.split(',')[0]} في ${lat}, ${lng}`);
                    };

                    resultsContainer.appendChild(resultDiv);
                });

            } catch (error) {
                console.error('خطأ في البحث:', error);
                resultsContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #ea4335;">خطأ في البحث</div>';
            }
        }

        // دالة إضافة للمفضلة (يمكن تطويرها لاحقاً)
        function addToFavorites(lat, lng, name) {
            alert(`سيتم إضافة "${name}" للمفضلة\nالإحداثيات: ${lat}, ${lng}`);
            // هنا يمكن إضافة كود لحفظ المكان في قاعدة البيانات
        }

        // إخفاء نتائج البحث عند النقر خارجها
        document.addEventListener('click', function(e) {
            const searchContainer = document.querySelector('.map-search-container');
            if (searchContainer && !searchContainer.contains(e.target)) {
                document.getElementById('mapSearchResults').style.display = 'none';
            }
        });

        // البحث عند الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.target.id === 'mapSearchInput' && e.key === 'Enter') {
                searchInMap();
            }
        });

        // ==================== نظام التنبيهات الصوتية ====================

        // دالة تفعيل/إيقاف التنبيهات الصوتية
        function toggleVoiceNavigation() {
            const voiceBtn = document.querySelector('.voice-control-btn');

            if (voiceNavigationEnabled) {
                stopVoiceNavigation();
                voiceBtn.classList.remove('active');
                voiceBtn.classList.add('muted');
                voiceBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                voiceBtn.title = 'تفعيل التنبيهات الصوتية';
                showSuccessMessage('تم إيقاف التنبيهات الصوتية');
            } else {
                startVoiceNavigation();
                voiceBtn.classList.remove('muted');
                voiceBtn.classList.add('active');
                voiceBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                voiceBtn.title = 'إيقاف التنبيهات الصوتية';
                showSuccessMessage('تم تفعيل التنبيهات الصوتية');
            }
        }

        // دالة بدء التنبيهات الصوتية
        function startVoiceNavigation() {
            voiceNavigationEnabled = true;

            // بدء مراقبة الموقع
            if (navigator.geolocation) {
                navigationInterval = setInterval(checkNavigationProgress, 5000); // كل 5 ثوان
                console.log('🔊 تم تفعيل التنبيهات الصوتية');
            }
        }

        // دالة إيقاف التنبيهات الصوتية
        function stopVoiceNavigation() {
            voiceNavigationEnabled = false;

            if (navigationInterval) {
                clearInterval(navigationInterval);
                navigationInterval = null;
            }

            // إيقاف أي تشغيل صوتي حالي
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
            }

            console.log('🔇 تم إيقاف التنبيهات الصوتية');
        }

        // دالة فحص تقدم الملاحة
        function checkNavigationProgress() {
            if (!currentRoute || !voiceNavigationEnabled) return;

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    userPosition = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    // فحص المسافة من كل خطوة في المسار
                    const instructions = currentRoute.instructions;
                    for (let i = 0; i < instructions.length; i++) {
                        const instruction = instructions[i];
                        const distance = calculateDistance(
                            userPosition.lat, userPosition.lng,
                            instruction.latLng.lat, instruction.latLng.lng
                        );

                        // إذا كان المستخدم قريب من خطوة جديدة (أقل من 100 متر)
                        if (distance < 0.1 && i > lastAnnouncedStep) {
                            announceInstruction(instruction, i);
                            lastAnnouncedStep = i;
                            break;
                        }
                    }
                },
                (error) => {
                    console.log('لا يمكن تحديد الموقع للتنبيهات الصوتية');
                }
            );
        }

        // دالة حساب المسافة بين نقطتين
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // نصف قطر الأرض بالكيلومتر
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // دالة الإعلان الصوتي لبداية المسار
        function announceRouteStart(distance, time, destination) {
            const message = `تم إنشاء المسار إلى ${destination}. المسافة ${distance} كيلومتر، الوقت المتوقع ${time} دقيقة. ابدأ القيادة.`;
            speak(message);
        }

        // دالة الإعلان الصوتي للتوجيهات
        function announceInstruction(instruction, stepNumber) {
            let message = translateInstruction(instruction.text);

            if (instruction.distance) {
                const distanceText = instruction.distance > 1000 ?
                    `خلال ${Math.round(instruction.distance/1000)} كيلومتر` :
                    `خلال ${Math.round(instruction.distance)} متر`;
                message = `${distanceText}، ${message}`;
            }

            speak(message);
            console.log(`🔊 تنبيه صوتي: ${message}`);
        }

        // دالة ترجمة التوجيهات للعربية
        function translateInstruction(englishText) {
            const translations = {
                'turn right': 'انعطف يميناً',
                'turn left': 'انعطف يساراً',
                'go straight': 'استمر مستقيماً',
                'continue': 'استمر',
                'arrive': 'وصلت إلى الوجهة',
                'head': 'اتجه',
                'north': 'شمالاً',
                'south': 'جنوباً',
                'east': 'شرقاً',
                'west': 'غرباً',
                'roundabout': 'دوار',
                'exit': 'اخرج',
                'merge': 'اندمج',
                'fork': 'تفرع'
            };

            let arabicText = englishText.toLowerCase();
            for (const [english, arabic] of Object.entries(translations)) {
                arabicText = arabicText.replace(new RegExp(english, 'gi'), arabic);
            }

            return arabicText;
        }

        // دالة التحدث
        function speak(text) {
            if (!speechSynthesis) return;

            // إيقاف أي تشغيل سابق
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'ar-SA'; // العربية السعودية
            utterance.rate = 0.9; // سرعة متوسطة
            utterance.pitch = 1; // نبرة عادية
            utterance.volume = 0.8; // مستوى صوت مناسب

            // في حالة عدم توفر الصوت العربي، استخدم الإنجليزية
            utterance.onerror = function() {
                utterance.lang = 'en-US';
                speechSynthesis.speak(utterance);
            };

            speechSynthesis.speak(utterance);
        }

        // دالة عرض رسالة نجاح
        function showSuccessMessage(message) {
            // إنشاء عنصر الرسالة
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed; top: 80px; right: 20px; z-index: 10000;
                background: #4caf50; color: white; padding: 12px 20px;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-family: 'Cairo', sans-serif; font-size: 14px;
                animation: slideIn 0.3s ease-out;
            `;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // ==================== دوال التحكم في عرض المواقع ====================

        // دالة إضافة جميع العلامات للخريطة مع الأسماء
        function addAllMarkersToMap() {
            // إزالة العلامات الحالية
            placesLayer.clearLayers();

            // إضافة جميع المواقع كعلامات
            currentPlaces.forEach(place => {
                // إنشاء أيقونة مخصصة حسب الفئة
                const categoryIcon = getCategoryIcon(place.category);
                const placeName = place.name || place.name_ar || 'موقع';

                // إنشاء علامة مع اسم المكان
                const marker = L.marker([place.latitude, place.longitude], {
                    icon: L.divIcon({
                        className: 'custom-marker-with-label',
                        html: `
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <!-- الأيقونة -->
                                <div style="background: ${categoryIcon.color}; width: 35px; height: 35px; border-radius: 50%; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; position: relative; z-index: 2;">
                                    <i class="${categoryIcon.icon}"></i>
                                </div>
                                <!-- اسم المكان -->
                                <div style="background: rgba(0,0,0,0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; margin-top: 2px; white-space: nowrap; max-width: 120px; overflow: hidden; text-overflow: ellipsis; box-shadow: 0 2px 6px rgba(0,0,0,0.3); font-family: 'Cairo', sans-serif;">
                                    ${placeName.length > 15 ? placeName.substring(0, 15) + '...' : placeName}
                                </div>
                            </div>
                        `,
                        iconSize: [120, 60],
                        iconAnchor: [60, 35]
                    })
                }).bindPopup(createPopupContent(place));

                // إضافة تأثير hover للعلامة
                marker.on('mouseover', function() {
                    this.getElement().style.transform = 'scale(1.1)';
                    this.getElement().style.zIndex = '1000';
                });

                marker.on('mouseout', function() {
                    this.getElement().style.transform = 'scale(1)';
                    this.getElement().style.zIndex = '100';
                });

                placesLayer.addLayer(marker);
            });

            console.log(`✅ تم إضافة ${currentPlaces.length} علامة مع الأسماء للخريطة`);
        }

        // دالة الحصول على أيقونة الفئة
        function getCategoryIcon(category) {
            const categoryIcons = {
                'restaurant': { icon: 'fas fa-utensils', color: '#ff5722' },
                'hospital': { icon: 'fas fa-hospital', color: '#f44336' },
                'school': { icon: 'fas fa-graduation-cap', color: '#2196f3' },
                'mosque': { icon: 'fas fa-mosque', color: '#4caf50' },
                'bank': { icon: 'fas fa-university', color: '#9c27b0' },
                'gas_station': { icon: 'fas fa-gas-pump', color: '#ff9800' },
                'shopping_mall': { icon: 'fas fa-shopping-cart', color: '#e91e63' },
                'hotel': { icon: 'fas fa-bed', color: '#3f51b5' },
                'pharmacy': { icon: 'fas fa-pills', color: '#00bcd4' },
                'university': { icon: 'fas fa-university', color: '#673ab7' },
                'general': { icon: 'fas fa-map-marker-alt', color: '#607d8b' }
            };

            return categoryIcons[category] || categoryIcons['general'];
        }

        // دالة الحصول على اسم الفئة بالعربية
        function getCategoryName(category) {
            const categoryNames = {
                'restaurant': 'مطاعم',
                'hospital': 'مستشفيات',
                'school': 'مدارس',
                'mosque': 'مساجد',
                'bank': 'بنوك',
                'gas_station': 'محطات وقود',
                'shopping_mall': 'مراكز تسوق',
                'hotel': 'فنادق',
                'pharmacy': 'صيدليات',
                'university': 'جامعات',
                'general': 'عام'
            };

            return categoryNames[category] || 'غير محدد';
        }

        // دالة إخفاء جميع المواقع وعرض الرسالة الترحيبية
        function showAllPlaces() {
            // إعادة تعيين الفلاتر
            currentFilter = '';
            currentSearch = '';

            // إخفاء جميع النقاط من الخريطة
            placesLayer.clearLayers();

            // عرض الرسالة الترحيبية
            document.getElementById('placesList').innerHTML = `
                <div style="text-align: center; padding: 30px 20px; color: #666;">
                    <i class="fas fa-map-marked-alt" style="font-size: 48px; color: #1a73e8; margin-bottom: 15px;"></i>
                    <h5 style="margin-bottom: 10px; color: #333;">مرحباً بك في خرائط اليمن</h5>
                    <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                        تم تحميل <strong>${currentPlaces.length}</strong> موقع بنجاح
                    </p>
                    <p style="font-size: 13px; color: #888;">
                        اختر فئة من الأعلى لعرض المواقع المطلوبة
                    </p>
                    <div style="margin-top: 20px;">
                        <i class="fas fa-arrow-up" style="color: #1a73e8; animation: bounce 2s infinite;"></i>
                    </div>
                </div>
            `;

            // تحديث العداد
            document.getElementById('visiblePlaces').textContent = '0';

            // إزالة زر "عرض المزيد" إن وجد
            const loadMoreContainer = document.querySelector('.load-more-container');
            if (loadMoreContainer) {
                loadMoreContainer.remove();
            }

            // إعادة تعيين أزرار الفئات
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('[data-category=""]').classList.add('active');

            // مسح مربع البحث
            document.getElementById('searchInput').value = '';

            showSuccessMessage(`تم إخفاء جميع المواقع - اختر فئة لعرض المواقع`);
            console.log(`✅ تم إخفاء جميع المواقع`);
        }

        // دالة توسيط الخريطة لتشمل جميع المواقع
        function fitMapToPlaces() {
            if (currentPlaces.length === 0) {
                showSuccessMessage('لا توجد مواقع لعرضها');
                return;
            }

            // إنشاء مجموعة من النقاط
            const group = new L.featureGroup();

            currentPlaces.forEach(place => {
                const marker = L.marker([place.latitude, place.longitude]);
                group.addLayer(marker);
            });

            // توسيط الخريطة لتشمل جميع النقاط
            map.fitBounds(group.getBounds().pad(0.1));

            showSuccessMessage(`تم توسيط الخريطة لعرض ${currentPlaces.length} موقع`);
            console.log(`✅ تم توسيط الخريطة لعرض ${currentPlaces.length} موقع`);
        }

        // دالة تحديث العرض حسب الفئة مع الأيقونات المخصصة والأسماء
        function updateDisplayByCategory(category) {
            let filtered = category ?
                currentPlaces.filter(place => place.category === category) :
                currentPlaces;

            // تحديث العلامات في الخريطة
            placesLayer.clearLayers();
            filtered.forEach(place => {
                const categoryIcon = getCategoryIcon(place.category);
                const placeName = place.name || place.name_ar || 'موقع';

                const marker = L.marker([place.latitude, place.longitude], {
                    icon: L.divIcon({
                        className: 'custom-marker-with-label',
                        html: `
                            <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <!-- الأيقونة -->
                                <div style="background: ${categoryIcon.color}; width: 35px; height: 35px; border-radius: 50%; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; position: relative; z-index: 2;">
                                    <i class="${categoryIcon.icon}"></i>
                                </div>
                                <!-- اسم المكان -->
                                <div style="background: rgba(0,0,0,0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; margin-top: 2px; white-space: nowrap; max-width: 120px; overflow: hidden; text-overflow: ellipsis; box-shadow: 0 2px 6px rgba(0,0,0,0.3); font-family: 'Cairo', sans-serif;">
                                    ${placeName.length > 15 ? placeName.substring(0, 15) + '...' : placeName}
                                </div>
                            </div>
                        `,
                        iconSize: [120, 60],
                        iconAnchor: [60, 35]
                    })
                }).bindPopup(createPopupContent(place));

                // إضافة تأثير hover للعلامة
                marker.on('mouseover', function() {
                    this.getElement().style.transform = 'scale(1.1)';
                    this.getElement().style.zIndex = '1000';
                });

                marker.on('mouseout', function() {
                    this.getElement().style.transform = 'scale(1)';
                    this.getElement().style.zIndex = '100';
                });

                placesLayer.addLayer(marker);
            });

            return filtered;
        }

        // دالة تحديد الموقع الحالي في الصفحة الرئيسية
        function getCurrentLocationMain() {
            const locationBtn = document.getElementById('locationBtnMain');

            // تغيير حالة الزر إلى التحميل
            locationBtn.disabled = true;
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديد...';

            // التحقق من دعم المتصفح لـ Geolocation
            if (!navigator.geolocation) {
                showErrorMessage('المتصفح لا يدعم تحديد الموقع الجغرافي');
                resetLocationButtonMain();
                return;
            }

            // خيارات تحديد الموقع
            const options = {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            };

            // طلب الموقع الحالي
            navigator.geolocation.getCurrentPosition(
                onLocationSuccessMain,
                onLocationErrorMain,
                options
            );
        }

        // عند نجاح تحديد الموقع في الصفحة الرئيسية
        function onLocationSuccessMain(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = position.coords.accuracy;

            console.log(`تم تحديد الموقع: ${lat}, ${lng} (دقة: ${accuracy}م)`);

            // إزالة العلامة السابقة إن وجدت
            if (window.currentLocationMarkerMain) {
                map.removeLayer(window.currentLocationMarkerMain);
            }

            // إنشاء أيقونة مخصصة للموقع الحالي
            const currentLocationIcon = L.divIcon({
                className: 'current-location-marker-main',
                html: `
                    <div style="
                        background: #17a2b8;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        border: 3px solid white;
                        box-shadow: 0 0 10px rgba(23,162,184,0.5);
                        position: relative;
                        z-index: 1000;
                    ">
                        <div style="
                            position: absolute;
                            top: -5px;
                            left: -5px;
                            width: 30px;
                            height: 30px;
                            border: 2px solid #17a2b8;
                            border-radius: 50%;
                            opacity: 0.3;
                            animation: pulse 2s infinite;
                        "></div>
                    </div>
                `,
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });

            // إضافة العلامة للموقع الحالي
            window.currentLocationMarkerMain = L.marker([lat, lng], {
                icon: currentLocationIcon
            }).addTo(map);

            // إضافة نافذة معلومات
            window.currentLocationMarkerMain.bindPopup(`
                <div style="text-align: center;">
                    <h6><i class="fas fa-map-marker-alt" style="color: #17a2b8;"></i> موقعك الحالي</h6>
                    <p><strong>الإحداثيات:</strong><br>
                    ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                    <p><strong>دقة التحديد:</strong> ${Math.round(accuracy)} متر</p>
                    <button onclick="findNearbyPlaces(${lat}, ${lng})" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;">
                        <i class="fas fa-search"></i> البحث عن أماكن قريبة
                    </button>
                </div>
            `).openPopup();

            // التحرك إلى الموقع الحالي
            map.setView([lat, lng], 15);

            showSuccessMessage('تم تحديد موقعك الحالي بنجاح!');
            resetLocationButtonMain();
        }

        // عند فشل تحديد الموقع في الصفحة الرئيسية
        function onLocationErrorMain(error) {
            let errorMessage = 'حدث خطأ في تحديد الموقع';

            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = 'تم رفض الإذن لتحديد الموقع. يرجى السماح بالوصول للموقع في إعدادات المتصفح.';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                    break;
                case error.TIMEOUT:
                    errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                    break;
            }

            console.error('خطأ في تحديد الموقع:', error);
            showErrorMessage(errorMessage);
            resetLocationButtonMain();
        }

        // إعادة تعيين زر الموقع في الصفحة الرئيسية
        function resetLocationButtonMain() {
            const locationBtn = document.getElementById('locationBtnMain');
            if (locationBtn) {
                locationBtn.disabled = false;
                locationBtn.innerHTML = '<i class="fas fa-crosshairs"></i> موقعي الحالي';
            }
        }

        // البحث عن أماكن قريبة من الموقع الحالي
        function findNearbyPlaces(lat, lng) {
            // حساب المسافة بين نقطتين
            function calculateDistance(lat1, lng1, lat2, lng2) {
                const R = 6371; // نصف قطر الأرض بالكيلومتر
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLng = (lng2 - lng1) * Math.PI / 180;
                const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                        Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                return R * c;
            }

            // البحث عن الأماكن القريبة (ضمن 5 كم)
            const nearbyPlaces = currentPlaces.filter(place => {
                if (place.latitude && place.longitude) {
                    const distance = calculateDistance(lat, lng, place.latitude, place.longitude);
                    return distance <= 5; // 5 كم
                }
                return false;
            }).sort((a, b) => {
                const distA = calculateDistance(lat, lng, a.latitude, a.longitude);
                const distB = calculateDistance(lat, lng, b.latitude, b.longitude);
                return distA - distB;
            });

            if (nearbyPlaces.length > 0) {
                showSuccessMessage(`تم العثور على ${nearbyPlaces.length} مكان قريب من موقعك`);

                // عرض الأماكن القريبة على الخريطة
                updateMapMarkers(nearbyPlaces);

                // عرض الأماكن في القائمة
                displayPlaces(nearbyPlaces.slice(0, 10)); // أول 10 أماكن

                console.log(`تم العثور على ${nearbyPlaces.length} مكان قريب`);
            } else {
                showErrorMessage('لم يتم العثور على أماكن قريبة من موقعك الحالي');
            }
        }

        // ==================== دوال التحكم في العلامات ====================

        // دالة تحديث رؤية العلامات حسب مستوى التكبير
        function updateMarkersVisibility() {
            const currentZoom = map.getZoom();
            const showLabels = currentZoom >= 12; // إظهار الأسماء عند التكبير 12 أو أكثر

            placesLayer.eachLayer(function(marker) {
                const element = marker.getElement();
                if (element) {
                    const labelElement = element.querySelector('div:last-child');
                    if (labelElement) {
                        labelElement.style.display = showLabels ? 'block' : 'none';
                    }
                }
            });
        }

        // دالة تحديث نمط العلامات حسب طبقة الخريطة
        function updateMarkersStyle(layerName) {
            const isSatellite = layerName.includes('أقمار') || layerName.includes('HD');
            const isLight = layerName.includes('الشوارع') || layerName.includes('تفاصيل');

            placesLayer.eachLayer(function(marker) {
                const element = marker.getElement();
                if (element) {
                    const labelElement = element.querySelector('div:last-child');
                    if (labelElement) {
                        if (isSatellite) {
                            // نمط للأقمار الصناعية - خلفية داكنة
                            labelElement.style.background = 'rgba(0,0,0,0.9)';
                            labelElement.style.color = 'white';
                            labelElement.style.border = '1px solid rgba(255,255,255,0.3)';
                        } else if (isLight) {
                            // نمط للخرائط الفاتحة - خلفية فاتحة
                            labelElement.style.background = 'rgba(255,255,255,0.95)';
                            labelElement.style.color = '#333';
                            labelElement.style.border = '1px solid rgba(0,0,0,0.2)';
                        } else {
                            // النمط الافتراضي
                            labelElement.style.background = 'rgba(0,0,0,0.8)';
                            labelElement.style.color = 'white';
                            labelElement.style.border = 'none';
                        }
                    }
                }
            });
        }

        // دالة إنشاء علامة محسنة
        function createEnhancedMarker(place) {
            const categoryIcon = getCategoryIcon(place.category);
            const placeName = place.name || place.name_ar || 'موقع';
            const currentZoom = map.getZoom();
            const showLabel = currentZoom >= 12;

            return L.marker([place.latitude, place.longitude], {
                icon: L.divIcon({
                    className: 'custom-marker-with-label',
                    html: `
                        <div style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                            <!-- الأيقونة -->
                            <div style="background: ${categoryIcon.color}; width: 35px; height: 35px; border-radius: 50%; border: 3px solid white; box-shadow: 0 3px 8px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; position: relative; z-index: 2;">
                                <i class="${categoryIcon.icon}"></i>
                            </div>
                            <!-- اسم المكان -->
                            <div style="background: rgba(0,0,0,0.8); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; margin-top: 2px; white-space: nowrap; max-width: 120px; overflow: hidden; text-overflow: ellipsis; box-shadow: 0 2px 6px rgba(0,0,0,0.3); font-family: 'Cairo', sans-serif; display: ${showLabel ? 'block' : 'none'};">
                                ${placeName.length > 15 ? placeName.substring(0, 15) + '...' : placeName}
                            </div>
                        </div>
                    `,
                    iconSize: [120, 60],
                    iconAnchor: [60, 35]
                })
            }).bindPopup(createPopupContent(place));
        }

        // ==================== دوال إدارة صلاحيات الموقع ====================

        // فحص صلاحيات الموقع
        async function checkLocationPermission() {
            if (locationPermissionChecked) {
                return locationPermissionStatus;
            }

            try {
                // فحص الصلاحية باستخدام Permissions API إذا كان متاحاً
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    locationPermissionStatus = permission.state;
                    locationPermissionChecked = true;

                    console.log(`🔐 حالة صلاحية الموقع: ${locationPermissionStatus}`);

                    // مراقبة تغييرات الصلاحية
                    permission.onchange = () => {
                        locationPermissionStatus = permission.state;
                        console.log(`🔄 تغيرت صلاحية الموقع إلى: ${locationPermissionStatus}`);
                    };

                    return locationPermissionStatus;
                } else {
                    // إذا لم تكن Permissions API متاحة، نحاول تحديد الحالة من خلال محاولة الوصول
                    console.log('⚠️ Permissions API غير متاحة، سيتم فحص الصلاحية عند الطلب');
                    locationPermissionStatus = 'unknown';
                    locationPermissionChecked = true;
                    return locationPermissionStatus;
                }
            } catch (error) {
                console.error('❌ خطأ في فحص صلاحية الموقع:', error);
                locationPermissionStatus = 'unknown';
                locationPermissionChecked = true;
                return locationPermissionStatus;
            }
        }

        // طلب الموقع مع إدارة ذكية للصلاحيات
        async function requestUserLocation(options = {}) {
            const defaultOptions = {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 300000 // 5 دقائق
            };

            const finalOptions = { ...defaultOptions, ...options };

            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermission();

            if (permissionStatus === 'denied') {
                throw new Error('تم رفض صلاحية الوصول للموقع. يرجى تفعيلها من إعدادات المتصفح.');
            }

            // إذا كان لدينا موقع محفوظ حديث، استخدمه
            if (cachedUserLocation && finalOptions.maximumAge > 0) {
                const now = Date.now();
                const cacheAge = now - cachedUserLocation.timestamp;

                if (cacheAge < finalOptions.maximumAge) {
                    console.log('📍 استخدام الموقع المحفوظ (عمر الكاش: ' + Math.round(cacheAge / 1000) + ' ثانية)');
                    return cachedUserLocation.position;
                }
            }

            return new Promise((resolve, reject) => {
                if (!navigator.geolocation) {
                    reject(new Error('المتصفح لا يدعم تحديد الموقع الجغرافي'));
                    return;
                }

                console.log('📡 طلب الموقع الحالي...');

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');

                        // حفظ الموقع في الكاش
                        cachedUserLocation = {
                            position: position,
                            timestamp: Date.now()
                        };

                        // تحديث حالة الصلاحية
                        if (locationPermissionStatus !== 'granted') {
                            locationPermissionStatus = 'granted';
                            console.log('✅ تم منح صلاحية الموقع');
                        }

                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);

                        // تحديث حالة الصلاحية حسب نوع الخطأ
                        if (error.code === error.PERMISSION_DENIED) {
                            locationPermissionStatus = 'denied';
                            console.log('❌ تم رفض صلاحية الموقع');
                        }

                        reject(error);
                    },
                    finalOptions
                );
            });
        }

        // ==================== دوال المسارات الاحتياطية ====================

        // إنشاء مسار مباشر احتياطي عند فشل OSRM
        function createDirectRoute(startLat, startLng, endLat, endLng, placeName) {
            console.log('🔄 إنشاء مسار مباشر احتياطي...');

            // حساب المسافة المباشرة
            const distance = calculateDistance(startLat, startLng, endLat, endLng);

            // إنشاء خط مباشر
            const directLine = L.polyline([
                [startLat, startLng],
                [endLat, endLng]
            ], {
                color: '#9c27b0',
                weight: 6,
                opacity: 0.8,
                dashArray: '10, 5' // خط متقطع للإشارة أنه مسار مباشر
            }).addTo(map);

            // إضافة علامات البداية والنهاية
            const startMarker = L.marker([startLat, startLng], {
                icon: L.divIcon({
                    className: 'route-start-marker',
                    html: '<div style="background: #4285f4; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                })
            }).addTo(map).bindPopup('نقطة البداية');

            const endMarker = L.marker([endLat, endLng], {
                icon: L.divIcon({
                    className: 'route-end-marker',
                    html: '<div style="background: #ea4335; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3);"></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                })
            }).addTo(map).bindPopup(placeName);

            // حفظ بيانات المسار للنظام المتقدم
            currentRoute = {
                coordinates: [
                    { lat: startLat, lng: startLng },
                    { lat: endLat, lng: endLng }
                ],
                instructions: [
                    { text: 'ابدأ الرحلة', distance: 0 },
                    { text: `توجه مباشرة إلى ${placeName} (${distance.toFixed(1)} كم)`, distance: distance * 1000 },
                    { text: 'وصلت إلى الوجهة', distance: 0 }
                ],
                summary: {
                    totalDistance: distance * 1000,
                    totalTime: Math.round(distance * 60) // تقدير: دقيقة لكل كيلومتر
                }
            };

            routeCoordinates = currentRoute.coordinates;

            // تكبير الخريطة لإظهار المسار
            const bounds = L.latLngBounds([[startLat, startLng], [endLat, endLng]]);
            map.fitBounds(bounds, { padding: [50, 50] });

            // إظهار معلومات المسار
            const time = Math.round(distance * 60);
            L.popup()
                .setLatLng([(startLat + endLat) / 2, (startLng + endLng) / 2])
                .setContent(`
                    <div style="text-align: center; padding: 10px;">
                        <h6 style="margin-bottom: 10px; color: #9c27b0;">
                            <i class="fas fa-route"></i> مسار مباشر إلى ${placeName}
                        </h6>
                        <p style="margin: 5px 0; color: #666;">
                            <strong>المسافة:</strong> ${distance.toFixed(1)} كم
                        </p>
                        <p style="margin: 5px 0; color: #666;">
                            <strong>الوقت المقدر:</strong> ${time} دقيقة
                        </p>
                        <p style="margin: 5px 0; color: #666; font-size: 12px;">
                            مسار احتياطي مباشر
                        </p>
                        <button class="popup-btn" onclick="startNavigation(currentRoute)" style="background: #4CAF50;">
                            <i class="fas fa-play"></i> بدء الملاحة والتتبع
                        </button>
                    </div>
                `)
                .openOn(map);

            console.log('✅ تم إنشاء المسار المباشر الاحتياطي بنجاح');
        }

        // ==================== دوال تقييم المسارات ====================

        // دالة تقييم المسار لتفضيل أقصر المسارات والأكثر مباشرة
        function calculateRouteScore(route) {
            if (!route || !route.summary || !route.coordinates) {
                return 0;
            }

            const summary = route.summary;
            const coordinates = route.coordinates;
            const instructions = route.instructions || [];

            // معايير التقييم - التركيز على أقصر المسارات
            let score = 1000; // نقطة البداية عالية

            // 1. المعيار الأساسي: أقصر مسافة (الأولوية القصوى)
            const routeDistance = summary.totalDistance;
            const distanceScore = Math.max(0, 500 - (routeDistance / 100)); // كلما قل المسار، زادت النقاط
            score += distanceScore;

            // 2. حساب نسبة الانحراف عن المسار المباشر
            const startCoord = coordinates[0];
            const endCoord = coordinates[coordinates.length - 1];
            const directDistance = calculateDistance(
                startCoord.lat || startCoord[1],
                startCoord.lng || startCoord[0],
                endCoord.lat || endCoord[1],
                endCoord.lng || endCoord[0]
            ) * 1000; // تحويل لمتر

            const detourRatio = routeDistance / directDistance;

            // 3. مكافأة كبيرة للمسارات المباشرة
            if (detourRatio < 1.1) {
                score += 200; // مكافأة كبيرة للمسارات المباشرة جداً
            } else if (detourRatio < 1.3) {
                score += 100; // مكافأة جيدة للمسارات المباشرة
            } else if (detourRatio < 1.5) {
                score += 50;  // مكافأة متوسطة
            } else if (detourRatio > 2.0) {
                score -= 200; // معاقبة كبيرة للمسارات الملتوية
            } else if (detourRatio > 1.8) {
                score -= 100; // معاقبة متوسطة
            }

            // 4. مكافأة صغيرة للوقت الأقل
            const routeTime = summary.totalTime;
            const timeScore = Math.max(0, 100 - (routeTime / 60)); // كلما قل الوقت، زادت النقاط
            score += timeScore;

            // 5. تفضيل المسارات مع عدد معقول من التعليمات (ليس قليل جداً وليس كثير جداً)
            if (instructions.length >= 3 && instructions.length <= 10) {
                score += 50; // مكافأة للمسارات المتوازنة
            } else if (instructions.length > 15) {
                score -= 30; // معاقبة للمسارات المعقدة جداً
            }

            console.log(`🔍 تقييم المسار: المسافة=${(routeDistance/1000).toFixed(1)}كم، الوقت=${Math.round(routeTime/60)}دق، النسبة=${detourRatio.toFixed(2)}، التعليمات=${instructions.length}، النقاط=${score.toFixed(1)}`);

            return score;
        }

        // ==================== دوال الدوران التلقائي وتتبع المسار ====================

        // تفعيل/إلغاء الدوران التلقائي
        function toggleAdvancedRotation() {
            const rotationBtn = document.querySelector('.rotation-btn');

            if (useAdvancedRotation) {
                // إلغاء الدوران التلقائي
                useAdvancedRotation = false;
                rotationBtn.classList.remove('active');
                rotationBtn.title = 'تفعيل دوران الخريطة المتقدم';

                // إعادة تعيين دوران الخريطة
                resetMapRotation();

                showNotification('تم إلغاء دوران الخريطة التلقائي', 'info');
                console.log('❌ تم إلغاء الدوران التلقائي');
            } else {
                // تفعيل الدوران التلقائي
                useAdvancedRotation = true;
                rotationBtn.classList.add('active');
                rotationBtn.title = 'إلغاء دوران الخريطة المتقدم';

                showNotification('تم تفعيل دوران الخريطة التلقائي', 'success');
                console.log('✅ تم تفعيل الدوران التلقائي');
            }
        }

        // تفعيل/إلغاء وضع التتبع
        function toggleFollowMode() {
            const followBtn = document.querySelector('.follow-btn');

            if (isNavigating) {
                // إيقاف التتبع
                stopNavigation();
                followBtn.classList.remove('active');
                followBtn.title = 'تفعيل وضع التتبع';

                showNotification('تم إيقاف وضع التتبع', 'info');
                console.log('❌ تم إيقاف وضع التتبع');
            } else {
                // بدء التتبع (يتطلب مسار نشط)
                if (currentRoute) {
                    startNavigation(currentRoute);
                    followBtn.classList.add('active');
                    followBtn.title = 'إيقاف وضع التتبع';

                    showNotification('تم تفعيل وضع التتبع', 'success');
                    console.log('✅ تم تفعيل وضع التتبع');
                } else {
                    showNotification('يرجى تحديد مسار أولاً', 'warning');
                }
            }
        }

        // بدء الملاحة مع الدوران التلقائي
        function startNavigation(route) {
            if (!route) {
                console.error('❌ لا يمكن بدء الملاحة - بيانات المسار غير صحيحة');
                return;
            }

            isNavigating = true;
            currentRoute = route;
            routeCoordinates = route.coordinates || [];
            currentStepIndex = 0;

            console.log('🧭 بدء الملاحة مع', routeCoordinates.length, 'نقطة');

            // تفعيل الدوران التلقائي إذا لم يكن مفعلاً
            if (!useAdvancedRotation) {
                toggleAdvancedRotation();
                console.log('🔄 تم تفعيل الدوران التلقائي تلقائياً');
            }

            // بدء مراقبة الموقع للملاحة
            if (navigator.geolocation) {
                navigationWatcher = navigator.geolocation.watchPosition(
                    updateNavigationPosition,
                    handleNavigationError,
                    {
                        enableHighAccuracy: true,
                        timeout: 15000,        // زيادة المهلة
                        maximumAge: 1000       // تحديث كل ثانية
                    }
                );

                console.log('📍 بدء مراقبة الموقع للملاحة (تحديث كل ثانية)');

                // إظهار رسالة تأكيد للمستخدم
                showNotification('بدء تتبع موقعك... تأكد من تفعيل GPS', 'info');
            } else {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                showNotification('المتصفح لا يدعم تحديد الموقع', 'error');
            }

            // تفعيل أزرار التحكم
            const followBtn = document.querySelector('.follow-btn');
            const rotationBtn = document.querySelector('.rotation-btn');

            if (followBtn) {
                followBtn.classList.add('active');
                followBtn.title = 'إيقاف وضع التتبع';
            }

            if (rotationBtn) {
                rotationBtn.classList.add('active');
                rotationBtn.title = 'إيقاف الدوران التلقائي';
            }

            // إظهار رسالة تأكيد
            showNotification('تم بدء الملاحة مع التتبع التلقائي', 'success');
        }

        // إيقاف الملاحة
        function stopNavigation() {
            isNavigating = false;
            currentRoute = null;
            routeCoordinates = [];
            currentStepIndex = 0;

            // إيقاف مراقبة الموقع
            if (navigationWatcher) {
                navigator.geolocation.clearWatch(navigationWatcher);
                navigationWatcher = null;
            }

            // إزالة علامة الموقع الحالي
            if (currentLocationMarker) {
                map.removeLayer(currentLocationMarker);
                currentLocationMarker = null;
            }

            // إعادة تعيين دوران الخريطة
            resetMapRotation();

            console.log('🛑 تم إيقاف الملاحة');
        }

        // تحديث موقع الملاحة
        function updateNavigationPosition(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = position.coords.accuracy;
            const speed = position.coords.speed || 0;

            console.log(`📍 موقع جديد: ${lat.toFixed(6)}, ${lng.toFixed(6)} (دقة: ${accuracy}م, سرعة: ${speed.toFixed(1)}م/ث)`);

            // تحديث علامة الموقع الحالي
            updateCurrentLocationMarker(lat, lng);

            // تحديث موضع الخريطة لتتبع المستخدم (دائماً أثناء التتبع)
            if (isNavigating) {
                // تحريك الخريطة بسلاسة لتتبع المستخدم
                map.panTo([lat, lng], {
                    animate: true,
                    duration: 1.5,
                    easeLinearity: 0.25
                });

                // الحفاظ على مستوى التكبير المناسب للملاحة
                const currentZoom = map.getZoom();
                if (currentZoom < 16) {
                    map.setZoom(16);
                }
            }

            // تحديث دوران الخريطة إذا كان مفعلاً
            if (useAdvancedRotation && routeCoordinates.length > 0) {
                updateMapRotation(lat, lng);
            }

            // فحص إذا كان المستخدم خارج المسار
            if (routeCoordinates.length > 0) {
                checkIfOffRoute(lat, lng);
            }
        }

        // تحديث علامة الموقع الحالي
        function updateCurrentLocationMarker(lat, lng) {
            // إزالة العلامة السابقة إن وجدت
            if (currentLocationMarker) {
                map.removeLayer(currentLocationMarker);
            }

            // إنشاء علامة جديدة للموقع الحالي
            currentLocationMarker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'current-location-marker-navigation',
                    html: `
                        <div style="
                            background: #4285f4;
                            width: 16px;
                            height: 16px;
                            border-radius: 50%;
                            border: 3px solid white;
                            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                            animation: pulse 2s infinite;
                        "></div>
                    `,
                    iconSize: [22, 22],
                    iconAnchor: [11, 11]
                })
            }).addTo(map);

            // إضافة نافذة معلومات
            currentLocationMarker.bindPopup(`
                <div style="text-align: center; padding: 5px;">
                    <h6 style="margin: 5px 0; color: #4285f4;">
                        <i class="fas fa-location-arrow"></i> موقعك الحالي
                    </h6>
                    <p style="margin: 2px 0; font-size: 12px; color: #666;">
                        ${lat.toFixed(6)}, ${lng.toFixed(6)}
                    </p>
                    ${isNavigating ? '<p style="margin: 2px 0; font-size: 11px; color: #4CAF50;"><i class="fas fa-route"></i> جاري التتبع</p>' : ''}
                </div>
            `);

            console.log(`✅ تم تحديث علامة الموقع: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        }

        // فحص إذا كان المستخدم خارج المسار مع إعادة التوجيه التلقائي
        function checkIfOffRoute(lat, lng) {
            if (routeCoordinates.length === 0) return;

            // العثور على أقرب نقطة في المسار
            let minDistance = Infinity;
            for (let i = 0; i < routeCoordinates.length; i++) {
                const coord = routeCoordinates[i];
                const distance = calculateDistance(lat, lng, coord.lat, coord.lng);
                if (distance < minDistance) {
                    minDistance = distance;
                }
            }

            // إذا كان المستخدم بعيداً عن المسار أكثر من العتبة المحددة
            if (minDistance > offRouteThreshold) {
                console.log(`⚠️ خارج المسار: المسافة ${(minDistance * 1000).toFixed(0)} متر`);

                // إظهار تنبيه وبدء إعادة التوجيه
                if (!window.offRouteWarningShown && !isOffRouteRecalculating) {
                    showNotification('خارج المسار - جاري البحث عن مسار بديل...', 'warning');
                    window.offRouteWarningShown = true;

                    // بدء إعادة التوجيه التلقائي
                    startOffRouteRecalculation(lat, lng);

                    // إعادة تعيين التحذير بعد 30 ثانية
                    setTimeout(() => {
                        window.offRouteWarningShown = false;
                    }, 30000);
                }
            } else {
                // المستخدم على المسار
                window.offRouteWarningShown = false;
                isOffRouteRecalculating = false;
            }
        }

        // إنشاء المسارات البديلة بخطوط منقطة
        function createAlternativeRoutes(routes, startLat, startLng, endLat, endLng) {
            console.log(`🛣️ إنشاء ${routes.length} مسار بديل...`);

            // مسح المسارات البديلة السابقة
            clearAlternativeRoutes();

            routes.forEach((route, index) => {
                const routeCoords = route.coordinates || [];
                if (routeCoords.length === 0) return;

                // تحويل الإحداثيات إلى تنسيق Leaflet
                const latLngs = routeCoords.map(coord => [
                    coord.lat || coord[1],
                    coord.lng || coord[0]
                ]);

                // إنشاء خط منقط للمسار البديل
                const alternativePolyline = L.polyline(latLngs, {
                    color: '#ff9800',           // لون برتقالي
                    weight: 4,
                    opacity: 0.7,
                    dashArray: '10, 10',       // خط منقط
                    className: 'alternative-route'
                }).addTo(map);

                // إضافة معلومات المسار عند النقر
                alternativePolyline.on('click', function() {
                    switchToAlternativeRoute(route, index);
                });

                // إضافة tooltip للمسار البديل
                const distance = (route.summary.totalDistance / 1000).toFixed(1);
                const time = Math.round(route.summary.totalTime / 60);
                alternativePolyline.bindTooltip(`
                    مسار بديل ${index + 1}<br>
                    المسافة: ${distance} كم<br>
                    الوقت: ${time} دقيقة<br>
                    <small>انقر للتبديل</small>
                `, {
                    permanent: false,
                    direction: 'center'
                });

                // حفظ المسار البديل
                alternativeRouteLayers.push({
                    layer: alternativePolyline,
                    route: route,
                    index: index
                });

                console.log(`✅ تم إنشاء مسار بديل ${index + 1}: ${distance} كم`);
            });

            console.log(`🎯 تم إنشاء ${alternativeRouteLayers.length} مسار بديل بنجاح`);
        }

        // مسح المسارات البديلة
        function clearAlternativeRoutes() {
            alternativeRouteLayers.forEach(item => {
                if (item.layer) {
                    map.removeLayer(item.layer);
                }
            });
            alternativeRouteLayers = [];
        }

        // التبديل إلى مسار بديل
        function switchToAlternativeRoute(route, index) {
            console.log(`🔄 التبديل إلى المسار البديل ${index + 1}`);

            // حفظ المسار الجديد
            currentRoute = {
                coordinates: route.coordinates || [],
                instructions: route.instructions || [],
                summary: route.summary || {}
            };

            // تحديث إحداثيات المسار
            if (route.coordinates) {
                routeCoordinates = route.coordinates.map(coord => ({
                    lat: coord.lat || coord[1],
                    lng: coord.lng || coord[0]
                }));
            }

            // تحديث المسار الرئيسي
            if (mainRouteLayer) {
                map.removeControl(mainRouteLayer);
            }

            // إنشاء مسار جديد
            mainRouteLayer = L.Routing.control({
                waypoints: [
                    L.latLng(routeCoordinates[0].lat, routeCoordinates[0].lng),
                    L.latLng(routeCoordinates[routeCoordinates.length - 1].lat, routeCoordinates[routeCoordinates.length - 1].lng)
                ],
                router: L.Routing.osrmv1({
                    serviceUrl: 'http://localhost:5001/route/v1'
                }),
                lineOptions: {
                    styles: [
                        {color: '#9c27b0', opacity: 0.9, weight: 6},
                        {color: '#ba68c8', opacity: 1, weight: 4}
                    ]
                },
                show: false,
                createMarker: function() { return null; }
            }).addTo(map);

            // مسح المسارات البديلة وإعادة إنشائها
            clearAlternativeRoutes();
            const remainingRoutes = alternativeRoutes.filter((_, i) => i !== index);
            if (remainingRoutes.length > 0) {
                createAlternativeRoutes(remainingRoutes,
                    routeCoordinates[0].lat, routeCoordinates[0].lng,
                    routeCoordinates[routeCoordinates.length - 1].lat, routeCoordinates[routeCoordinates.length - 1].lng
                );
            }

            const distance = (route.summary.totalDistance / 1000).toFixed(1);
            const time = Math.round(route.summary.totalTime / 60);
            showNotification(`تم التبديل للمسار البديل: ${distance} كم، ${time} دقيقة`, 'success');
        }

        // بدء إعادة حساب المسار عند الخروج عن المسار
        function startOffRouteRecalculation(currentLat, currentLng) {
            if (isOffRouteRecalculating) return;

            isOffRouteRecalculating = true;
            console.log('🔄 بدء إعادة حساب المسار من الموقع الحالي...');

            // البحث عن أفضل مسار بديل
            if (alternativeRouteLayers.length > 0) {
                // العثور على أقرب مسار بديل
                let closestRoute = null;
                let minDistanceToAlternative = Infinity;

                alternativeRouteLayers.forEach(item => {
                    const route = item.route;
                    const routeCoords = route.coordinates || [];

                    // حساب المسافة إلى هذا المسار البديل
                    let minDistToRoute = Infinity;
                    routeCoords.forEach(coord => {
                        const distance = calculateDistance(
                            currentLat, currentLng,
                            coord.lat || coord[1], coord.lng || coord[0]
                        );
                        if (distance < minDistToRoute) {
                            minDistToRoute = distance;
                        }
                    });

                    if (minDistToRoute < minDistanceToAlternative) {
                        minDistanceToAlternative = minDistToRoute;
                        closestRoute = item;
                    }
                });

                // التبديل إلى أقرب مسار بديل
                if (closestRoute && minDistanceToAlternative < 0.2) { // أقل من 200 متر
                    console.log(`✅ تم العثور على مسار بديل قريب: ${(minDistanceToAlternative * 1000).toFixed(0)} متر`);
                    switchToAlternativeRoute(closestRoute.route, closestRoute.index);
                    return;
                }
            }

            // إذا لم يتم العثور على مسار بديل قريب، إنشاء مسار جديد
            console.log('🆕 إنشاء مسار جديد من الموقع الحالي...');
            const destination = routeCoordinates[routeCoordinates.length - 1];

            // إنشاء مسار جديد من الموقع الحالي إلى الوجهة
            setTimeout(() => {
                createLocalRoute(currentLat, currentLng, destination.lat, destination.lng, 'الوجهة');
                isOffRouteRecalculating = false;
            }, 2000);
        }

        // إيقاف الملاحة
        function stopNavigation() {
            isNavigating = false;
            currentStepIndex = 0;

            // إيقاف مراقبة الموقع
            if (navigationWatcher) {
                navigator.geolocation.clearWatch(navigationWatcher);
                navigationWatcher = null;
            }

            // إزالة علامة الموقع الحالي
            if (currentLocationMarker) {
                map.removeLayer(currentLocationMarker);
                currentLocationMarker = null;
            }

            // إيقاف الدوران التلقائي
            if (useAdvancedRotation) {
                toggleAdvancedRotation();
            }

            // إعادة تعيين أزرار التحكم
            const followBtn = document.querySelector('.follow-btn');
            const rotationBtn = document.querySelector('.rotation-btn');

            if (followBtn) {
                followBtn.classList.remove('active');
                followBtn.title = 'تفعيل وضع التتبع';
            }

            if (rotationBtn) {
                rotationBtn.classList.remove('active');
                rotationBtn.title = 'تفعيل الدوران التلقائي';
            }

            console.log('🛑 تم إيقاف الملاحة');
        }

        // تحديث دوران الخريطة مع تتبع محسن
        function updateMapRotation(currentLat, currentLng) {
            if (routeCoordinates.length < 2) return;

            // العثور على أقرب نقطة في المسار
            let closestIndex = 0;
            let minDistance = Infinity;

            for (let i = 0; i < routeCoordinates.length; i++) {
                const coord = routeCoordinates[i];
                const distance = calculateDistance(currentLat, currentLng, coord.lat, coord.lng);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestIndex = i;
                }
            }

            // تحديث فهرس الخطوة الحالية
            currentStepIndex = closestIndex;

            // الحصول على النقطة التالية في المسار
            const nextIndex = Math.min(closestIndex + 1, routeCoordinates.length - 1);
            const nextPoint = routeCoordinates[nextIndex];

            if (nextPoint) {
                // حساب الاتجاه
                const bearing = calculateBearing(currentLat, currentLng, nextPoint.lat, nextPoint.lng);

                // تطبيق التتبع دائماً (إزالة شرط التغيير الكبير)
                rotateMap(bearing);
                lastBearing = bearing;

                // إظهار معلومات التقدم
                const progress = Math.round((closestIndex / routeCoordinates.length) * 100);
                console.log(`📍 التقدم في المسار: ${progress}% (نقطة ${closestIndex + 1}/${routeCoordinates.length})`);
            }
        }

        // حساب الاتجاه بين نقطتين
        function calculateBearing(lat1, lng1, lat2, lng2) {
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const lat1Rad = lat1 * Math.PI / 180;
            const lat2Rad = lat2 * Math.PI / 180;

            const y = Math.sin(dLng) * Math.cos(lat2Rad);
            const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

            const bearing = Math.atan2(y, x) * 180 / Math.PI;
            return (bearing + 360) % 360;
        }

        // دوران الخريطة باستخدام تحريك الخريطة بدلاً من CSS
        function rotateMap(bearing) {
            if (!isNavigating || !currentLocationMarker) return;

            // بدلاً من دوران الخريطة، نحرك الخريطة لتتبع الاتجاه
            const currentPos = currentLocationMarker.getLatLng();

            // تحديث موضع الخريطة مع تأثير سلس
            map.panTo(currentPos, {
                animate: true,
                duration: 1.0
            });

            console.log(`🧭 تحديث اتجاه الخريطة: ${bearing.toFixed(1)}°`);
        }

        // إعادة تعيين دوران الخريطة
        function resetMapRotation() {
            // لا حاجة لإعادة تعيين CSS، فقط إعادة تعيين المتغيرات
            lastBearing = 0;
            console.log('🔄 تم إعادة تعيين اتجاه الخريطة');
        }

        // معالجة أخطاء الملاحة
        function handleNavigationError(error) {
            console.error('❌ خطأ في الملاحة:', error);

            let errorMessage = 'خطأ في تحديد الموقع: ';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage += 'تم رفض الإذن';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage += 'الموقع غير متاح';
                    break;
                case error.TIMEOUT:
                    errorMessage += 'انتهت مهلة التحديد';
                    break;
                default:
                    errorMessage += 'خطأ غير معروف';
                    break;
            }

            showNotification(errorMessage, 'error');
        }

        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#F44336' : type === 'warning' ? '#FF9800' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                font-size: 14px;
                max-width: 300px;
                animation: slideInLeft 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutLeft 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // جعل الدوال متاحة عالمياً
        window.loadMorePlaces = loadMorePlaces;
        window.loadMoreFilteredPlaces = loadMoreFilteredPlaces;
        window.getCurrentLocation = getCurrentLocation;
        window.filterByCategory = filterByCategory;
        window.searchPlaces = searchPlaces;
        window.searchInMap = searchInMap;
        window.addToFavorites = addToFavorites;
        window.getDirections = getDirections;
        window.clearRoute = clearRoute;
        window.handleLocationPermission = handleLocationPermission;
        window.closeLocationDialog = closeLocationDialog;
        window.toggleVoiceNavigation = toggleVoiceNavigation;
        window.showAllPlaces = showAllPlaces;
        window.fitMapToPlaces = fitMapToPlaces;
        window.updateMarkersVisibility = updateMarkersVisibility;
        window.updateMarkersStyle = updateMarkersStyle;
        window.toggleAdvancedRotation = toggleAdvancedRotation;
        window.toggleFollowMode = toggleFollowMode;
        window.startNavigation = startNavigation;
        window.stopNavigation = stopNavigation;
        window.showAlternativeRoutes = showAlternativeRoutes;
        window.toggleLayersDropdown = toggleLayersDropdown;
        window.getCurrentLocationMain = getCurrentLocationMain;
        window.changeMapLayer = changeMapLayer;

        // دالة عرض المسارات البديلة
        function showAlternativeRoutes() {
            if (!window.allRoutes || window.allRoutes.length <= 1) {
                alert('لا توجد مسارات بديلة متاحة');
                return;
            }

            let routesInfo = '<div style="max-height: 300px; overflow-y: auto;">';
            routesInfo += '<h6 style="margin: 0 0 15px; color: #1976d2;"><i class="fas fa-route"></i> جميع المسارات المتاحة</h6>';

            window.allRoutes.forEach((route, index) => {
                const distance = (route.summary.totalDistance / 1000).toFixed(1);
                const time = Math.round(route.summary.totalTime / 60);
                const isSelected = route === currentRoute;
                const borderColor = isSelected ? '#4caf50' : '#ff9800';
                const title = isSelected ? 'المسار المختار' : `مسار بديل ${index}`;

                routesInfo += `
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 10px; border-left: 3px solid ${borderColor};">
                        <div style="font-weight: 600; color: #333; margin-bottom: 5px;">
                            ${title} ${isSelected ? '✓' : ''}
                        </div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 3px;">
                            <i class="fas fa-road"></i> المسافة: <strong>${distance} كم</strong>
                        </div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 3px;">
                            <i class="fas fa-clock"></i> الوقت: <strong>${time} دقيقة</strong>
                        </div>
                        ${isSelected ? '<div style="font-size: 11px; color: #4caf50; font-weight: 600;">المسار الحالي</div>' : ''}
                    </div>
                `;
            });

            routesInfo += '</div>';

            // إنشاء نافذة منبثقة للمسارات البديلة
            const popup = L.popup({
                maxWidth: 350,
                className: 'alternative-routes-popup'
            })
            .setLatLng(map.getCenter())
            .setContent(`
                <div style="text-align: center;">
                    ${routesInfo}
                    <button onclick="map.closePopup()" style="background: #1976d2; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 12px; margin-top: 10px;">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            `)
            .openOn(map);
        }

        // دالة تبديل قائمة الطبقات المنبثقة
        function toggleLayersDropdown() {
            const dropdown = document.getElementById('layersDropdown');
            const chevron = document.getElementById('layersChevron');

            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
                chevron.style.transform = 'rotate(0deg)';
            } else {
                dropdown.classList.add('show');
                chevron.style.transform = 'rotate(180deg)';
            }
        }

        // دالة تغيير طبقة الخريطة
        function changeMapLayer(layerKey, layerName) {
            if (!window.mapBaseLayers || !window.currentBaseLayer) {
                console.error('الطبقات غير متاحة');
                return;
            }

            // إزالة الطبقة الحالية
            map.removeLayer(window.currentBaseLayer);

            // إضافة الطبقة الجديدة
            const layerMap = {
                'detailed': 'الشوارع التفصيلية المحسنة',
                'satellite': 'الأقمار الصناعية',
                'satellite-hd': 'أقمار صناعية HD',
                'hybrid': 'هجينة محسنة',
                'terrain': 'التضاريس'
            };

            const selectedLayer = window.mapBaseLayers[layerMap[layerKey]];
            if (selectedLayer) {
                selectedLayer.addTo(map);
                window.currentBaseLayer = selectedLayer;

                // تحديث اسم الطبقة في الزر
                document.getElementById('currentLayerName').textContent = layerName;

                // تحديث الخيارات النشطة
                document.querySelectorAll('.layer-option').forEach(option => {
                    option.classList.remove('active');
                });
                document.querySelector(`[data-layer="${layerKey}"]`).classList.add('active');

                // إغلاق القائمة
                toggleLayersDropdown();

                console.log(`✅ تم تغيير الطبقة إلى: ${layerName}`);
            }
        }

        // إضافة مستمعات الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            initMap();

            // مستمعات البحث
            document.getElementById('searchInput').addEventListener('input', function(e) {
                searchPlaces(e.target.value);
            });

            // مستمعات الفئات
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    filterByCategory(this.dataset.category);
                });
            });

            // مستمعات الطبقات
            document.querySelectorAll('.layer-option').forEach(option => {
                option.addEventListener('click', function() {
                    const layerKey = this.dataset.layer;
                    const layerName = this.querySelector('label').textContent;
                    const radio = this.querySelector('input[type="radio"]');

                    radio.checked = true;
                    changeMapLayer(layerKey, layerName);
                });
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                const layersControl = document.querySelector('.custom-layers-control');
                const dropdown = document.getElementById('layersDropdown');

                if (!layersControl.contains(e.target) && dropdown.classList.contains('show')) {
                    toggleLayersDropdown();
                }
            });
        });

        // ==================== دوال المسارات البديلة ====================

        // دالة عرض المسارات البديلة بخط متقطع
        function displayAlternativeRoutes(routes, selectedIndex) {
            clearAlternativeRoutes();
            console.log(`🔄 عرض ${routes.length - 1} مسار بديل بخط متقطع...`);

            routes.forEach((route, index) => {
                if (index === selectedIndex) return;

                const routeCoordinates = route.coordinates.map(coord => [coord[1], coord[0]]);

                const alternativeRouteLine = L.polyline(routeCoordinates, {
                    color: '#ba68c8',           // لون بنفسجي فاتح
                    weight: 4,                  // سمك أقل من المسار الرئيسي
                    opacity: 0.7,               // شفافية أكثر
                    dashArray: '10, 10',        // خط متقطع
                    lineCap: 'round',
                    lineJoin: 'round'
                }).addTo(map);

                const distance = (route.summary.totalDistance / 1000).toFixed(1);
                const time = Math.round(route.summary.totalTime / 60);

                alternativeRouteLine.bindTooltip(`
                    <div style="text-align: center; font-size: 12px;">
                        <strong>مسار بديل ${index + 1}</strong><br>
                        <i class="fas fa-road"></i> ${distance} كم<br>
                        <i class="fas fa-clock"></i> ${time} دقيقة<br>
                        <small style="color: #666;">انقر للتبديل</small>
                    </div>
                `, {
                    permanent: false,
                    direction: 'center',
                    className: 'alternative-route-tooltip'
                });

                alternativeRouteLine.on('click', function() {
                    switchToAlternativeRoute(routes, index);
                });

                alternativeRouteLayers.push(alternativeRouteLine);
                alternativeRoutes.push({
                    route: route,
                    index: index,
                    layer: alternativeRouteLine
                });

                console.log(`✅ تم عرض مسار بديل ${index + 1}: ${distance}كم، ${time}د`);
            });

            console.log(`✅ تم عرض ${alternativeRouteLayers.length} مسار بديل بخط متقطع`);
        }

        // دالة مسح المسارات البديلة
        function clearAlternativeRoutes() {
            alternativeRouteLayers.forEach(layer => {
                if (map.hasLayer(layer)) {
                    map.removeLayer(layer);
                }
            });

            alternativeRouteLayers = [];
            alternativeRoutes = [];

            console.log('🗑️ تم مسح جميع المسارات البديلة');
        }

        // دالة التبديل إلى مسار بديل
        function switchToAlternativeRoute(allRoutes, newRouteIndex) {
            console.log(`🔄 التبديل إلى المسار البديل ${newRouteIndex + 1}...`);

            if (routingControl) {
                map.removeControl(routingControl);
                routingControl = null;
            }

            clearAlternativeRoutes();

            const selectedRoute = allRoutes[newRouteIndex];
            const routeCoordinates = selectedRoute.coordinates.map(coord => [coord[1], coord[0]]);

            const newRouteLine = L.polyline(routeCoordinates, {
                color: '#9c27b0',           // لون بنفسجي للمسار الرئيسي
                weight: 6,
                opacity: 0.9,
                lineCap: 'round',
                lineJoin: 'round'
            }).addTo(map);

            const distance = (selectedRoute.summary.totalDistance / 1000).toFixed(1);
            const time = Math.round(selectedRoute.summary.totalTime / 60);

            const routeInfo = L.popup()
                .setLatLng(routeCoordinates[Math.floor(routeCoordinates.length / 2)])
                .setContent(`
                    <div style="text-align: center;">
                        <h6 style="margin: 10px 0 5px; color: #9c27b0;">
                            <i class="fas fa-route"></i> مسار بديل محدد
                        </h6>
                        <div style="background: #f3e5f5; padding: 8px; border-radius: 6px; margin: 8px 0;">
                            <div style="font-size: 13px; color: #666;">
                                <i class="fas fa-road"></i> المسافة: <strong>${distance} كم</strong>
                            </div>
                            <div style="font-size: 13px; color: #666;">
                                <i class="fas fa-clock"></i> الوقت المتوقع: <strong>${time} دقيقة</strong>
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button onclick="clearRoute()" style="background: #ea4335; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; margin: 2px;">
                                <i class="fas fa-times"></i> إلغاء المسار
                            </button>
                        </div>
                    </div>
                `)
                .openOn(map);

            currentRoute = selectedRoute;
            displayAlternativeRoutes(allRoutes, newRouteIndex);

            document.querySelector('.clear-route-btn').style.display = 'block';
            document.querySelector('.voice-control-btn').style.display = 'block';

            console.log(`✅ تم التبديل إلى المسار البديل ${newRouteIndex + 1}: ${distance}كم، ${time}د`);
        }

        // ==================== تحسينات نظام التوجيه ====================

        // تحسين دالة إنشاء المسار لتشمل المسارات البديلة
        function enhanceRouteCreation() {
            if (routingControl) {
                routingControl.on('routesfound', function(e) {
                    const routes = e.routes;
                    clearAlternativeRoutes();

                    if (routes.length > 1) {
                        console.log(`📊 تم العثور على ${routes.length} مسار، جاري تحليل أفضل مسار...`);

                        const routeScores = routes.map((route, index) => {
                            const distance = route.summary.totalDistance;
                            const time = route.summary.totalTime;
                            const steps = route.instructions ? route.instructions.length : 0;

                            let score = 0;

                            // نقاط المسافة (70% من النقاط)
                            const minDistance = Math.min(...routes.map(r => r.summary.totalDistance));
                            const distanceRatio = minDistance / distance;
                            score += distanceRatio * 70;

                            // نقاط الوقت (20% من النقاط)
                            const minTime = Math.min(...routes.map(r => r.summary.totalTime));
                            const timeRatio = minTime / time;
                            score += timeRatio * 20;

                            // نقاط قلة التعقيد (10% من النقاط)
                            const maxSteps = Math.max(...routes.map(r => r.instructions ? r.instructions.length : 0));
                            const stepsRatio = maxSteps > 0 ? (maxSteps - steps) / maxSteps : 1;
                            score += stepsRatio * 10;

                            console.log(`🛣️ مسار ${index + 1}: ${(distance/1000).toFixed(1)}كم، ${Math.round(time/60)}د، ${steps} خطوة، نقاط: ${score.toFixed(1)}`);

                            return { route, score, index };
                        });

                        const bestRoute = routeScores.reduce((best, current) => {
                            return current.score > best.score ? current : best;
                        });

                        console.log(`✅ تم اختيار المسار ${bestRoute.index + 1} بنقاط ${bestRoute.score.toFixed(1)} - المسافة: ${(bestRoute.route.summary.totalDistance / 1000).toFixed(1)} كم`);

                        displayAlternativeRoutes(routes, bestRoute.index);
                    }

                    currentRoute = routes[0];
                    lastAnnouncedStep = -1;

                    document.querySelector('.clear-route-btn').style.display = 'block';
                    document.querySelector('.voice-control-btn').style.display = 'block';

                    announceRouteStart(
                        (routes[0].summary.totalDistance / 1000).toFixed(1),
                        Math.round(routes[0].summary.totalTime / 60),
                        'الوجهة المحددة'
                    );
                });
            }
        }

        // ==================== تحسينات التنبيهات الصوتية ====================

        // دالة الإعلان الصوتي عند بدء المسار
        function announceRouteStart(distance, time, placeName) {
            const message = `تم إنشاء مسار إلى ${placeName}. المسافة ${distance} كيلومتر، الوقت المتوقع ${time} دقيقة. يمكنك تفعيل التنبيهات الصوتية للحصول على إرشادات أثناء القيادة.`;
            speak(message);
            console.log(`🔊 إعلان بدء المسار: ${message}`);
        }

        // دالة اختبار التنبيهات الصوتية
        function testVoiceNavigation() {
            if (!speechSynthesis) {
                alert('التنبيهات الصوتية غير مدعومة في هذا المتصفح');
                return;
            }

            const testMessage = 'مرحباً، هذا اختبار للتنبيهات الصوتية في خرائط اليمن. النظام يعمل بشكل صحيح.';
            speak(testMessage);
            console.log('🧪 تم تشغيل اختبار التنبيهات الصوتية');
        }

        // تحسين دالة التحدث
        function speak(text) {
            if (!speechSynthesis) {
                console.warn('⚠️ Speech Synthesis غير مدعوم في هذا المتصفح');
                return;
            }

            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.9;
            utterance.pitch = 1;
            utterance.volume = 0.8;

            utterance.onerror = function(event) {
                console.warn('⚠️ خطأ في التشغيل الصوتي:', event.error);
                if (utterance.lang !== 'en-US') {
                    utterance.lang = 'en-US';
                    speechSynthesis.speak(utterance);
                }
            };

            utterance.onstart = function() {
                console.log('🔊 بدء التشغيل الصوتي:', text.substring(0, 50) + '...');
            };

            utterance.onend = function() {
                console.log('✅ انتهى التشغيل الصوتي');
            };

            speechSynthesis.speak(utterance);
        }

        // تحسين دالة clearRoute لتشمل المسارات البديلة
        const originalClearRoute = clearRoute;
        clearRoute = function() {
            clearAlternativeRoutes();
            if (originalClearRoute) {
                originalClearRoute();
            }
        };

        // تطبيق التحسينات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            enhanceRouteCreation();

            // فحص دعم الأصوات
            setTimeout(() => {
                if (speechSynthesis) {
                    const voices = speechSynthesis.getVoices();
                    console.log('🔊 الأصوات المتاحة:', voices.length);

                    const arabicVoices = voices.filter(voice => voice.lang.startsWith('ar'));
                    console.log('🇸🇦 الأصوات العربية:', arabicVoices.length);

                    if (arabicVoices.length > 0) {
                        console.log('✅ يوجد دعم للأصوات العربية');
                    } else {
                        console.log('⚠️ لا يوجد أصوات عربية، سيتم استخدام الإنجليزية');
                    }
                }
            }, 1000);
        });
    
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>

    <!-- نظام التوجيه الذكي المحسن -->
    <script src="{{ url_for('static', filename='js/smart-routing-enhanced.js') }}">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>
    <script src="{{ url_for('static', filename='js/route-preferences.js') }}">
        // ==================== إصلاح صلاحيات الموقع ====================
        
        // فحص محسن لصلاحيات الموقع
        async function checkLocationPermissionEnhanced() {
            console.log('🔍 فحص صلاحيات الموقع المحسن...');
            
            // فحص دعم الموقع الجغرافي
            if (!navigator.geolocation) {
                console.error('❌ المتصفح لا يدعم تحديد الموقع');
                return 'not-supported';
            }
            
            // فحص البروتوكول (HTTPS مطلوب للمواقع الخارجية)
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            if (!isSecure && location.hostname !== 'localhost') {
                console.warn('⚠️ HTTPS مطلوب لصلاحيات الموقع في المواقع الخارجية');
            }
            
            try {
                // محاولة فحص الصلاحية
                if ('permissions' in navigator) {
                    const permission = await navigator.permissions.query({ name: 'geolocation' });
                    console.log(`🔐 حالة الصلاحية: ${permission.state}`);
                    return permission.state;
                } else {
                    console.log('⚠️ Permissions API غير متاحة، سيتم الطلب مباشرة');
                    return 'unknown';
                }
            } catch (error) {
                console.error('❌ خطأ في فحص الصلاحية:', error);
                return 'unknown';
            }
        }
        
        // طلب الموقع مع معالجة محسنة للأخطاء
        async function requestLocationEnhanced() {
            console.log('📍 طلب الموقع المحسن...');
            
            // فحص الصلاحية أولاً
            const permissionStatus = await checkLocationPermissionEnhanced();
            
            if (permissionStatus === 'denied') {
                showNotification('تم رفض صلاحية الموقع. يرجى تفعيلها من إعدادات المتصفح.', 'error');
                return null;
            }
            
            if (permissionStatus === 'not-supported') {
                showNotification('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
                return null;
            }
            
            return new Promise((resolve, reject) => {
                const options = {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 300000 // 5 دقائق
                };
                
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('✅ تم الحصول على الموقع بنجاح');
                        showNotification('تم تحديد موقعك بنجاح', 'success');
                        resolve(position);
                    },
                    (error) => {
                        console.error('❌ خطأ في تحديد الموقع:', error);
                        
                        let errorMessage = 'فشل في تحديد الموقع';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض صلاحية الموقع. يرجى السماح بالوصول للموقع.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                break;
                        }
                        
                        showNotification(errorMessage, 'error');
                        reject(error);
                    },
                    options
                );
            });
        }
        
        // تحديث دالة تحديد الموقع الحالي
        async function getCurrentLocationFixed() {
            console.log('🎯 تحديد الموقع الحالي المحسن...');
            
            try {
                const position = await requestLocationEnhanced();
                if (!position) return;
                
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;
                
                console.log(`📍 الموقع: ${lat}, ${lng} (دقة: ${accuracy}m)`);
                
                // حفظ الموقع
                userLocation = { lat, lng, accuracy };
                
                // الانتقال للموقع
                map.setView([lat, lng], 15);
                
                // إضافة علامة للموقع الحالي
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        className: 'user-location-marker',
                        html: `
                            <div style="width: 20px; height: 20px; background: #007bff; border: 3px solid white; border-radius: 50%; box-shadow: 0 2px 8px rgba(0,123,255,0.4); position: relative;">
                                <div style="width: 40px; height: 40px; background: rgba(0,123,255,0.2); border-radius: 50%; position: absolute; top: -13px; left: -13px; animation: pulse 2s infinite;"></div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); opacity: 1; }
                                    100% { transform: scale(2); opacity: 0; }
                                }
                            </style>
                        `,
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                // إضافة دائرة دقة
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: accuracy,
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    weight: 2
                }).addTo(map);
                
                showNotification(`تم تحديد موقعك (دقة: ${Math.round(accuracy)}m)`, 'success');
                
            } catch (error) {
                console.error('❌ فشل في تحديد الموقع:', error);
            }
        }
    
</script>
</body>
</html>
