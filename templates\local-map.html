<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗺️ خرائط اليمن المحلية - بدون إنترنت</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .status-bar {
            background: #27ae60;
            color: white;
            padding: 8px;
            text-align: center;
            font-size: 14px;
        }
        
        .controls {
            background: white;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-box {
            flex: 1;
            min-width: 250px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            direction: rtl;
        }
        
        .search-box input:focus {
            border-color: #3498db;
            outline: none;
        }
        
        .category-filter select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            min-width: 150px;
        }
        
        .stats {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .stat-item {
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 3px;
        }
        
        #map {
            height: calc(100vh - 200px);
            width: 100%;
        }
        
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
            display: none;
        }
        
        .popup-content {
            direction: rtl;
            text-align: right;
            max-width: 300px;
        }
        
        .popup-content h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .popup-content .info {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .popup-content .category {
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            display: inline-block;
            margin: 5px 0;
        }
        
        .popup-content .rating {
            color: #f39c12;
            font-weight: bold;
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .search-result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            direction: rtl;
        }
        
        .search-result-item:hover {
            background: #f8f9fa;
        }
        
        .search-result-item .name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .search-result-item .details {
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats {
                justify-content: center;
            }
            
            #map {
                height: calc(100vh - 250px);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ خرائط الجمهورية اليمنية - النسخة المحلية</h1>
    </div>
    
    <div class="status-bar">
        🔒 يعمل محلياً بدون إنترنت | 📍 <span id="places-count">جاري التحميل...</span> موقع متاح
    </div>
    
    <div class="controls">
        <div class="search-box">
            <input type="text" id="search-input" placeholder="🔍 ابحث عن مكان، مدينة، أو خدمة...">
            <div class="search-results" id="search-results"></div>
        </div>
        
        <div class="category-filter">
            <select id="category-select">
                <option value="">جميع الفئات</option>
            </select>
        </div>
        
        <div class="stats">
            <div class="stat-item">📍 <span id="visible-markers">0</span> ظاهر</div>
            <div class="stat-item">🔍 <span id="search-results-count">0</span> نتيجة</div>
            <div class="stat-item">⚡ <span id="load-time">0</span>ms</div>
        </div>
    </div>
    
    <div id="map"></div>
    
    <div class="loading" id="loading">
        <div>🔄 جاري تحميل البيانات...</div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // متغيرات عامة
        let map;
        let markersLayer;
        let allPlaces = [];
        let visibleMarkers = 0;
        let searchTimeout;
        
        // إعداد الخريطة
        function initMap() {
            // إنشاء الخريطة مع التركيز على اليمن
            map = L.map('map').setView([15.3694, 44.1910], 7);
            
            // إضافة طبقة البلاطات المحلية
            L.tileLayer('/tiles/yemen_real/{z}/{x}/{y}.png', {
                attribution: '© خرائط اليمن المحلية',
                maxZoom: 16,
                minZoom: 6
            }).addTo(map);
            
            // إنشاء طبقة العلامات
            markersLayer = L.layerGroup().addTo(map);
            
            // تحميل البيانات
            loadPlaces();
            loadCategories();
        }
        
        // تحميل الأماكن من قاعدة البيانات
        async function loadPlaces() {
            showLoading(true);
            const startTime = Date.now();
            
            try {
                const response = await fetch('/api/places');
                const data = await response.json();
                
                if (data.success) {
                    allPlaces = data.places;
                    updatePlacesCount(data.count);
                    displayPlaces(allPlaces);
                    
                    const loadTime = Date.now() - startTime;
                    document.getElementById('load-time').textContent = loadTime;
                } else {
                    console.error('خطأ في تحميل الأماكن:', data.error);
                }
            } catch (error) {
                console.error('خطأ في الشبكة:', error);
            } finally {
                showLoading(false);
            }
        }
        
        // تحميل الفئات
        async function loadCategories() {
            try {
                const response = await fetch('/api/places/categories');
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('category-select');
                    data.categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.name;
                        option.textContent = `${category.name} (${category.count})`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('خطأ في تحميل الفئات:', error);
            }
        }
        
        // عرض الأماكن على الخريطة
        function displayPlaces(places) {
            markersLayer.clearLayers();
            visibleMarkers = 0;
            
            places.forEach(place => {
                if (place.lat && place.lng) {
                    const marker = L.marker([place.lat, place.lng]);
                    
                    // محتوى النافذة المنبثقة
                    const popupContent = createPopupContent(place);
                    marker.bindPopup(popupContent);
                    
                    markersLayer.addLayer(marker);
                    visibleMarkers++;
                }
            });
            
            updateVisibleMarkers(visibleMarkers);
        }
        
        // إنشاء محتوى النافذة المنبثقة
        function createPopupContent(place) {
            return `
                <div class="popup-content">
                    <h3>${place.name}</h3>
                    ${place.category ? `<div class="category">${place.category}</div>` : ''}
                    ${place.address ? `<div class="info">📍 ${place.address}</div>` : ''}
                    ${place.phone ? `<div class="info">📞 ${place.phone}</div>` : ''}
                    ${place.rating ? `<div class="info">⭐ <span class="rating">${place.rating}</span> (${place.review_count} تقييم)</div>` : ''}
                    <div class="info">📊 ID: ${place.id}</div>
                </div>
            `;
        }
        
        // البحث في الأماكن
        async function searchPlaces(query) {
            if (!query.trim()) {
                hideSearchResults();
                return;
            }
            
            try {
                const response = await fetch(`/api/places/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                if (data.success) {
                    showSearchResults(data.results);
                    document.getElementById('search-results-count').textContent = data.count;
                }
            } catch (error) {
                console.error('خطأ في البحث:', error);
            }
        }
        
        // عرض نتائج البحث
        function showSearchResults(results) {
            const container = document.getElementById('search-results');
            container.innerHTML = '';
            
            if (results.length === 0) {
                container.innerHTML = '<div class="search-result-item">لا توجد نتائج</div>';
            } else {
                results.forEach(place => {
                    const item = document.createElement('div');
                    item.className = 'search-result-item';
                    item.innerHTML = `
                        <div class="name">${place.name}</div>
                        <div class="details">
                            ${place.category || ''} ${place.address ? '• ' + place.address : ''}
                        </div>
                    `;
                    
                    item.addEventListener('click', () => {
                        if (place.lat && place.lng) {
                            map.setView([place.lat, place.lng], 15);
                            hideSearchResults();
                            document.getElementById('search-input').value = place.name;
                        }
                    });
                    
                    container.appendChild(item);
                });
            }
            
            container.style.display = 'block';
        }
        
        // إخفاء نتائج البحث
        function hideSearchResults() {
            document.getElementById('search-results').style.display = 'none';
        }
        
        // تصفية حسب الفئة
        async function filterByCategory(category) {
            if (!category) {
                displayPlaces(allPlaces);
                return;
            }
            
            showLoading(true);
            
            try {
                const response = await fetch(`/api/places/by-category/${encodeURIComponent(category)}`);
                const data = await response.json();
                
                if (data.success) {
                    displayPlaces(data.places);
                }
            } catch (error) {
                console.error('خطأ في التصفية:', error);
            } finally {
                showLoading(false);
            }
        }
        
        // تحديث عدد الأماكن
        function updatePlacesCount(count) {
            document.getElementById('places-count').textContent = count.toLocaleString('ar');
        }
        
        // تحديث عدد العلامات الظاهرة
        function updateVisibleMarkers(count) {
            document.getElementById('visible-markers').textContent = count.toLocaleString('ar');
        }
        
        // إظهار/إخفاء شاشة التحميل
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            
            // البحث
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchPlaces(this.value);
                }, 300);
            });
            
            // إخفاء نتائج البحث عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-box')) {
                    hideSearchResults();
                }
            });
            
            // تصفية الفئات
            document.getElementById('category-select').addEventListener('change', function() {
                filterByCategory(this.value);
            });
        });
    </script>
</body>
</html>
