#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث تخطيط عناصر التحكم ثلاثية الأبعاد وطبقات الخريطة
"""

import re

def update_3d_controls_layout():
    print("🎨 تحديث تخطيط عناصر التحكم ثلاثية الأبعاد...")
    
    # قراءة الملف الحالي
    with open('templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إزالة التكرارات في CSS
    content = re.sub(r'        /\* مميزات ثلاثية الأبعاد \*/.*?(?=        /\*|$)', '', content, flags=re.DOTALL)
    
    # CSS محسن للعناصر ثلاثية الأبعاد
    new_3d_css = '''
        /* عناصر التحكم ثلاثية الأبعاد المحسنة */
        .top-controls-bar {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1001;
            display: flex;
            gap: 15px;
            pointer-events: none;
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            pointer-events: auto;
            overflow: hidden;
        }
        
        .control-panel.collapsed {
            width: 50px;
            height: 50px;
        }
        
        .control-panel.collapsed .control-content {
            display: none;
        }
        
        .control-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            user-select: none;
        }
        
        .control-panel.collapsed .control-header {
            justify-content: center;
            padding: 12px;
        }
        
        .control-content {
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .control-content::-webkit-scrollbar {
            width: 6px;
        }
        
        .control-content::-webkit-scrollbar-track {
            background: #f1f3f4;
        }
        
        .control-content::-webkit-scrollbar-thumb {
            background: #dadce0;
            border-radius: 3px;
        }
        
        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 16px;
        }
        
        .control-panel.collapsed .toggle-icon {
            transform: rotate(180deg);
        }
        
        .control-panel.collapsed .control-title {
            display: none;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-label {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .control-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .control-button.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .control-slider {
            width: 100%;
            margin: 5px 0;
        }
        
        .layer-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .layer-toggle input[type="checkbox"] {
            margin-left: 8px;
        }
        
        .view-mode-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-top: 10px;
        }
        
        .layer-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .layer-item:last-child {
            border-bottom: none;
        }
        
        .layer-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .layer-opacity {
            width: 60px;
        }
        
        /* تحسينات للعرض ثلاثي الأبعاد */
        .leaflet-container {
            background: #f8f9fa !important;
        }
        
        .building-3d {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .building-3d:hover {
            filter: brightness(1.2);
            transform: scale(1.05);
        }
        
        .street-3d {
            stroke-width: 3;
            stroke: #2c3e50;
            fill: none;
            transition: all 0.3s ease;
        }
        
        .street-3d:hover {
            stroke-width: 5;
            stroke: #3498db;
        }
        
        /* مؤشر الارتفاع */
        .elevation-indicator {
            position: fixed;
            bottom: 100px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1001;
        }
        
        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .top-controls-bar {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .control-panel {
                width: 100%;
                max-width: 300px;
            }
        }
    '''
    
    # إضافة CSS الجديد
    content = content.replace('</style>', new_3d_css + '\n</style>')
    
    # تحديث HTML للعناصر
    new_controls_html = '''
    <!-- شريط التحكم العلوي -->
    <div class="top-controls-bar">
        <!-- لوحة التحكم ثلاثية الأبعاد -->
        <div class="control-panel" id="map3dControls">
            <div class="control-header" onclick="toggle3DPanel()">
                <span class="control-title">🏗️ العرض ثلاثي الأبعاد</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="control-content">
                <div class="control-group">
                    <label class="control-label">وضع العرض</label>
                    <div class="view-mode-selector">
                        <button class="control-button active" onclick="setViewMode('2d')">2D</button>
                        <button class="control-button" onclick="setViewMode('3d')">3D</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <label class="control-label">🏢 المباني</label>
                    <div class="layer-toggle">
                        <label>عرض المباني</label>
                        <input type="checkbox" id="buildingsToggle" onchange="toggleBuildings(this.checked)" checked>
                    </div>
                    <input type="range" class="control-slider" id="buildingHeight" min="1" max="10" value="5" 
                           onchange="setBuildingHeight(this.value)">
                    <small>ارتفاع المباني: <span id="heightValue">5</span>x</small>
                </div>
                
                <div class="control-group">
                    <label class="control-label">🛣️ الشوارع</label>
                    <div class="layer-toggle">
                        <label>عرض الشوارع</label>
                        <input type="checkbox" id="streetsToggle" onchange="toggleStreets(this.checked)" checked>
                    </div>
                    <div class="layer-toggle">
                        <label>أسماء الشوارع</label>
                        <input type="checkbox" id="streetNamesToggle" onchange="toggleStreetNames(this.checked)">
                    </div>
                </div>
                
                <div class="control-group">
                    <label class="control-label">🌍 طبقات الخريطة</label>
                    <button class="control-button" onclick="setMapLayer('satellite')">أقمار صناعية</button>
                    <button class="control-button" onclick="setMapLayer('terrain')">تضاريس</button>
                    <button class="control-button" onclick="setMapLayer('streets')">شوارع</button>
                </div>
                
                <div class="control-group">
                    <label class="control-label">📐 زاوية الرؤية</label>
                    <input type="range" class="control-slider" id="tiltSlider" min="0" max="60" value="0" 
                           onchange="setMapTilt(this.value)">
                    <small>الميل: <span id="tiltValue">0</span>°</small>
                </div>
                
                <div class="control-group">
                    <label class="control-label">🔄 الدوران</label>
                    <input type="range" class="control-slider" id="rotationSlider" min="0" max="360" value="0" 
                           onchange="setMapRotation(this.value)">
                    <small>الدوران: <span id="rotationValue">0</span>°</small>
                </div>
            </div>
        </div>
        
        <!-- لوحة طبقات الخريطة -->
        <div class="control-panel" id="mapLayersPanel">
            <div class="control-header" onclick="toggleLayersPanel()">
                <span class="control-title">🗺️ طبقات الخريطة</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="control-content">
                <div class="layer-item">
                    <label>الأماكن</label>
                    <div class="layer-controls">
                        <input type="range" class="layer-opacity" min="0" max="100" value="100" 
                               onchange="setLayerOpacity('places', this.value)">
                        <input type="checkbox" checked onchange="toggleLayer('places', this.checked)">
                    </div>
                </div>
                
                <div class="layer-item">
                    <label>المباني</label>
                    <div class="layer-controls">
                        <input type="range" class="layer-opacity" min="0" max="100" value="80" 
                               onchange="setLayerOpacity('buildings', this.value)">
                        <input type="checkbox" checked onchange="toggleLayer('buildings', this.checked)">
                    </div>
                </div>
                
                <div class="layer-item">
                    <label>الشوارع</label>
                    <div class="layer-controls">
                        <input type="range" class="layer-opacity" min="0" max="100" value="90" 
                               onchange="setLayerOpacity('streets', this.value)">
                        <input type="checkbox" checked onchange="toggleLayer('streets', this.checked)">
                    </div>
                </div>
                
                <div class="layer-item">
                    <label>التضاريس</label>
                    <div class="layer-controls">
                        <input type="range" class="layer-opacity" min="0" max="100" value="70" 
                               onchange="setLayerOpacity('terrain', this.value)">
                        <input type="checkbox" onchange="toggleLayer('terrain', this.checked)">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مؤشر الارتفاع -->
    <div class="elevation-indicator" id="elevationIndicator">
        الارتفاع: <span id="currentElevation">0</span> متر
    </div>'''
    
    # استبدال العناصر القديمة
    content = re.sub(r'    <!-- عناصر التحكم ثلاثية الأبعاد -->.*?<!-- مؤشر الارتفاع -->.*?</div>', 
                     new_controls_html, content, flags=re.DOTALL)
    
    # إضافة JavaScript للتحكم في الطي والفتح
    toggle_js = '''
        // ==================== التحكم في طي وفتح اللوحات ====================
        
        function toggle3DPanel() {
            const panel = document.getElementById('map3dControls');
            panel.classList.toggle('collapsed');
            
            const icon = panel.querySelector('.toggle-icon');
            if (panel.classList.contains('collapsed')) {
                icon.textContent = '▲';
            } else {
                icon.textContent = '▼';
            }
        }
        
        function toggleLayersPanel() {
            const panel = document.getElementById('mapLayersPanel');
            panel.classList.toggle('collapsed');
            
            const icon = panel.querySelector('.toggle-icon');
            if (panel.classList.contains('collapsed')) {
                icon.textContent = '▲';
            } else {
                icon.textContent = '▼';
            }
        }
        
        // طي اللوحات افتراضياً على الشاشات الصغيرة
        function checkScreenSize() {
            if (window.innerWidth <= 768) {
                document.getElementById('map3dControls').classList.add('collapsed');
                document.getElementById('mapLayersPanel').classList.add('collapsed');
            }
        }
        
        // فحص حجم الشاشة عند التحميل وتغيير الحجم
        window.addEventListener('load', checkScreenSize);
        window.addEventListener('resize', checkScreenSize);
    '''
    
    # إضافة JavaScript قبل إغلاق script
    content = content.replace('</script>', toggle_js + '\n</script>')
    
    # حفظ الملف المحدث
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم تحديث تخطيط عناصر التحكم بنجاح!")
    print("🎨 التحسينات المطبقة:")
    print("   - نقل عناصر التحكم إلى الشريط العلوي")
    print("   - إضافة إمكانية الطي والفتح")
    print("   - تحسين التصميم والاستجابة")
    print("   - ترتيب أفضل للعناصر")

if __name__ == "__main__":
    update_3d_controls_layout()
