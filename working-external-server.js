#!/usr/bin/env node
/**
 * 🌐 خادم خرائط اليمن الأصلي - الإعدادات الثابتة
 * المحلي: http://localhost:5000
 * الخارجي: https://yemenmaps.com:8443 + https://***********:8443
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

console.log('🚀 تشغيل خادم خرائط اليمن الأصلي...');
console.log('=====================================');

const config = {
    local: {
        port: 5000,
        host: '127.0.0.1'
    },
    external: {
        port: 8443,
        host: '0.0.0.0',
        domain: 'yemenmaps.com'
    },
    ssl: {
        key: 'D:/yemen-maps/ssl/yemenmaps.com.key',
        cert: 'D:/yemen-maps/ssl/yemenmaps_com/yemenmaps_com.crt'
    }
};

// معالج الطلبات
function handleRequest(req, res, isHttps = false) {
    const url = req.url;

    // API endpoints
    if (url === '/api/locations') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'success', data: [] }));
        return;
    }

    if (url.startsWith('/api/search-locations')) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'success', results: [] }));
        return;
    }

    // Admin login
    if (url === '/admin') {
        const adminHtml = `
        <!DOCTYPE html>
        <html>
        <head><title>Admin - Yemen Maps</title></head>
        <body>
            <h1>Admin Panel</h1>
            <form>
                <input type="text" placeholder="Username: admin" />
                <input type="password" placeholder="Password: yemen123" />
                <button type="submit">Login</button>
            </form>
        </body>
        </html>`;
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(adminHtml);
        return;
    }

    // Default page
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end('<h1>Yemen Maps Server Running</h1>');
}

// خادم HTTP المحلي - المنفذ 5000
const httpServer = http.createServer((req, res) => {
    handleRequest(req, res, false);
});

httpServer.listen(config.local.port, config.local.host, () => {
    console.log('✅ خادم HTTP المحلي يعمل على:');
    console.log(`   🌐 http://localhost:5000/`);
    console.log(`   🛠️ http://localhost:5000/admin`);
    console.log(`   📱 http://localhost:5000/api/locations`);
});

// خادم HTTPS الخارجي - المنفذ 8443
try {
    const sslOptions = {
        key: fs.readFileSync(config.ssl.key),
        cert: fs.readFileSync(config.ssl.cert)
    };

    const httpsServer = https.createServer(sslOptions, (req, res) => {
        handleRequest(req, res, true);
    });

    httpsServer.listen(config.external.port, config.external.host, () => {
        console.log('🔒 خادم HTTPS الخارجي يعمل على:');
        console.log(`   🌐 https://yemenmaps.com:8443/`);
        console.log(`   🛠️ https://yemenmaps.com:8443/admin`);
        console.log(`   📱 https://yemenmaps.com:8443/api/locations`);
        console.log('');
        console.log('🔐 بيانات الدخول للإدارة:');
        console.log('   Username: admin');
        console.log('   Password: yemen123');
    });
} catch (error) {
    console.log('❌ فشل في تشغيل HTTPS:', error.message);
}

// معالجة الإيقاف
process.on('SIGINT', () => {
    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   إجمالي الطلبات: ${stats.total}`);
    console.log(`   الطلبات الخارجية: ${stats.external}`);
    console.log(`   طلبات HTTPS: ${stats.https}`);
    console.log('\n🛑 إيقاف خادم HTTPS...');
    process.exit(0);
});

